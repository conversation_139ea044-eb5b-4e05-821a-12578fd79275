[core]
reloadable = false
order = 0

[package]
version = "2.0.6"
category = "Simulation"
title = "Isaac Sim Kit Helpers"
description = "This Extension that provides a way to launch a python app"
keywords = ["isaac", "kit", "python"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true
toggleable = false

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.simulation_app."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.simulation_app" = {}

[[python.module]]
name = "omni.isaac.kit"
