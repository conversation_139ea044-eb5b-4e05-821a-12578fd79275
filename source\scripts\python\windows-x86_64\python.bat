:: SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
:: SPDX-License-Identifier: Apache-2.0
::
:: Licensed under the Apache License, Version 2.0 (the "License");
:: you may not use this file except in compliance with the License.
:: You may obtain a copy of the License at
::
:: http://www.apache.org/licenses/LICENSE-2.0
::
:: Unless required by applicable law or agreed to in writing, software
:: distributed under the License is distributed on an "AS IS" BASIS,
:: WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
:: See the License for the specific language governing permissions and
:: limitations under the License.

@echo off
setlocal

:: Setup python env from generated file (generated by tools/repoman/build.py)
if exist "%~dp0setup_python_env.bat" (
    call "%~dp0setup_python_env.bat"
)

:: The executable path will be where python is located. So we set Cabonite app path through env var:
set CARB_APP_PATH=%~dp0kit
set ISAAC_PATH=%~dp0
set EXP_PATH=%~dp0apps

:: By default use our python, but allow overriding it by checking if PYTHONEXE env var is defined:
if "%PYTHONEXE%"=="" (
    REM In the DX driver, the omniverse profile is set with the process name "kit.exe".
    REM so it's necessary to change the name of the python executable to "kit.exe" to get proper optimization.
    REM Later, when kit 107.0 uses the Vulkan as a default, this workaround needs to be removed.
    if not exist "%~dp0kit\python\kit.exe" (
        copy "%~dp0kit\python\python.exe" "%~dp0kit\python\kit.exe"
    )
    set PYTHONEXE="%~dp0kit\python\kit.exe"
    REM set PYTHONEXE="%~dp0kit\python\python.exe"
)

call %PYTHONEXE% %*

if errorlevel 1 ( goto ErrorRunningPython )

:Success
endlocal
exit /B 0

:ErrorRunningPython
echo There was an error running python.
endlocal
exit /B 1
