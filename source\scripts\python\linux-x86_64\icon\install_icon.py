# SPDX-FileCopyrightText: Copyright (c) 2023-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import sys

if sys.platform == "win32":
    pass
else:
    icon_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "omni.isaac.sim.png")
    user_apps_folder = os.path.expanduser("~/.local/share/applications")
    if os.path.exists(user_apps_folder):
        with open(os.path.expanduser("~/.local/share/applications/IsaacSim.desktop"), "w") as file:
            print("Writing Isaac Sim icon file")
            file.write(
                f"""[Desktop Entry]
Version=1.0
Name=Isaac Sim
Icon={icon_path}
Terminal=false
Type=Application
StartupWMClass=IsaacSim"""
            )
