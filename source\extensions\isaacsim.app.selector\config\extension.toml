[package]
version = "2.9.6"
category = "Simulation"
title = "Isaac Sim App Selector"
description = "Mini Launch Pad app for Isaac Sim Sub Apps"
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "icons/preview.png"
icon = "icons/isaacsim.exp.full.png"
toggleable = false
writeTarget.kit = true

[dependencies]
"isaacsim.core.version" = {}
"omni.kit.pip_archive" = {} # pulls in pyperclip
"omni.kit.uiapp" = {}
"omni.kit.window.title" = {}

[[python.module]]
name = "isaacsim.app.selector"
