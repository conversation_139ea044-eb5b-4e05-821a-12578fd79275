[package]
version = "2.2.1"
category = "Simulation"
title = "UI components for the Isaac Sim Occupancy Map"
description = "UI components for the Isaac Sim Occupancy Map provides UI for 2D Occupancy Map Generation"
keywords = ["isaac", "occupancy map"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.asset.gen.omap" = {}
"isaacsim.core.api" = {}
"isaacsim.core.deprecation_manager" = {}
"isaacsim.gui.components" = {}

[[python.module]]
name = "isaacsim.asset.gen.omap.ui"

[[python.module]]
name = "isaacsim.asset.gen.omap.ui.tests"
