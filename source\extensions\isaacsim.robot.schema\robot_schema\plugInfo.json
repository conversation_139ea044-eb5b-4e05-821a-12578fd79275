# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "RobotSchemaIsaacAttachmentPointAPI": {
                        "alias": {
                            "UsdSchemaBase": "IsaacAttachmentPointAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "RobotSchemaIsaacJointAPI": {
                        "alias": {
                            "UsdSchemaBase": "IsaacJointAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "RobotSchemaIsaacLinkAPI": {
                        "alias": {
                            "UsdSchemaBase": "IsaacLink<PERSON><PERSON>"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "RobotSchemaIsaacReferencePointAPI": {
                        "alias": {
                            "UsdSchemaBase": "IsaacReferencePointAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "RobotSchemaIsaacRobotAPI": {
                        "alias": {
                            "UsdSchemaBase": "IsaacRobotAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "RobotSchemaIsaacSurfaceGripper": {
                        "alias": {
                            "UsdSchemaBase": "IsaacSurfaceGripper"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaKind": "abstractTyped"
                    }
                }
            }, 
            "LibraryPath": "@PLUG_INFO_LIBRARY_PATH@", 
            "Name": "RobotSchema", 
            "ResourcePath": ".", 
            "Root": ".", 
            "Type": "resource"
        }
    ]
}
