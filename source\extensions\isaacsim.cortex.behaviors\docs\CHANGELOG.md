# Changelog
## [2.0.9] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [2.0.8] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.0.7] - 2025-05-10
### Changed
- Enable FSD in test settings

## [2.0.6] - 2025-04-09
### Changed
- Update all test args to be consistent

## [2.0.5] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.0.4] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.0.3] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.2] - 2024-12-23
### Changed
- Increased approach height when placing bins on stacking example

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-09-28
### Changed
- Extension renamed to isaacsim.cortex.behaviors

## [1.0.6] - 2024-07-25
### Removed
- Deprecation tag

## [1.0.5] - 2024-05-22
### Added
- Deprecation tag

## [1.0.4] - 2023-10-12
### Fixed
- Fix invalid Rotation Matrix in UR10 bin stacking behavior waypoints

## [1.0.3] - 2023-10-10
### Fixed
- Fix typos in UR10 bin stacking behavior waypoints - Cross products now use both primary and secondary axes accordingly;

## [1.0.2] - 2023-08-25
### Fixed
- Cleanup extra dependencies

## [1.0.1] - 2023-08-18
### Fixed
- Fix bug in UR10 bin stacking behavior; make flip timing agnostic to robot speed.

## [1.0.0] - 2023-08-15
### Added
- Moved The Cortex sample Behaviors into their own extension
