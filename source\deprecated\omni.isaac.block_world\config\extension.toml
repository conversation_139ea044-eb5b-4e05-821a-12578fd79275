[package]
version = "2.0.6"
category = "Simulation"
title = "Block world extension for Isaac Sim Occupancy Map"
description = "2D Occupancy Map Generation"
keywords = ["isaac", "occupancy map"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.asset.importer.heightmap"

[dependencies]
"isaacsim.asset.importer.heightmap" = {}
"isaacsim.core.deprecation_manager" = {}

[[python.module]]
name = "omni.isaac.block_world"
