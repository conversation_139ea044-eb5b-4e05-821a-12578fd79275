[package]
description = "Headless Isaac Sim with Livestream using WebRTC"
execFile = "isaac-sim.streaming"
title = "Isaac Sim Full Streaming"
version = "5.0.0"

# That makes it browsable in UI with "experience" filter
keywords = ["experience", "app", "usd"]

[dependencies]
"isaacsim.exp.full" = {}
"omni.services.livestream.nvcf" = {}

[settings]
app.name = "Isaac-Sim Streaming"
app.version = "5.0.0"
app.vulkan = true                        # Explicitly enable Vulkan (on by default on Linux, off by default on Windows)
app.enableDeveloperWarnings = false      # disable developer warnings to reduce log noise

[settings.app.exts]
folders = [
    "${app}",
    "${app}/../exts",
    "${app}/../extscache",
    "${app}/../extsUser",
    "${app}/../extsDeprecated",
]

[settings.app.window]
hideUi = false
title = "Isaac Sim Full Streaming"
drawMouse = false

[settings.app.livestream]
allowResize = true
outDirectory = "${data}"

[settings.exts."omni.kit.registry.nucleus"]
registries = [
	{ name = "kit/default", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/107/shared" },
	{ name = "kit/sdk", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/sdk/${kit_version_short}/${kit_git_hash}" },
	{ name = "kit/community", url = "https://dw290v42wisod.cloudfront.net/exts/kit/community" },
]
