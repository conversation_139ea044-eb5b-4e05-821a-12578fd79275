# SPDX-FileCopyrightText: Copyright (c) 2018-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": True})

# enable the extension
import isaacsim.core.utils.extensions as extensions_utils

simulation_app.update()
extensions_utils.enable_extension("isaacsim.test.docstring")
simulation_app.update()

# run test
from isaacsim.test.docstring import StandaloneDocTestCase

tester = StandaloneDocTestCase()
tester.assertDocTests(StandaloneDocTestCase)

# quit
simulation_app.close()
