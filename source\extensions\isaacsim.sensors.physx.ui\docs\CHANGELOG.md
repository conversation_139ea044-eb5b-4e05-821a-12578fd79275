# Changelog
## [2.2.8] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [2.2.7] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.2.6] - 2025-05-10
### Changed
- Enable FSD in test settings

## [2.2.5] - 2025-04-09
### Changed
- Update all test args to be consistent

## [2.2.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.2.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.2.2] - 2025-02-14
### Added
- sensors to context menu

## [2.2.1] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.2.0] - 2024-12-10
### Added
- Lightbeam Sensor button moved to this extension

## [2.1.2] - 2024-12-04
### Changed
- glyph for the Create Menu

## [2.1.1] - 2024-11-25
### Fixed
- a bug in shutdown code

## [2.1.0] - 2024-11-01
### Changed
- menu name and location

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-10-04
### Removed
- Ultrasonic sensor UI elements

## [1.0.1] - 2024-07-17
### Fixed
- missing omni.kit.context_menu dependency

## [1.0.0] - 2024-03-12
### Added
- Initial version of Isaac Sim Range sensor extension examples
