API
===

.. warning::

    **The API featured in this extension is experimental and subject to change without deprecation cycles.**
    Although we will try to maintain backward compatibility in the event of a change, it may not always be possible.

.. contents:: API Content
    :local:

Python API
----------

.. Summary

The following table summarizes the available objects.

.. currentmodule:: isaacsim.core.experimental.objects

.. rubric:: shapes
.. autosummary::
    :nosignatures:

    Capsule
    Cone
    Cube
    Cylinder
    Shape
    Sphere

.. rubric:: meshes
.. autosummary::
    :nosignatures:

    Mesh

.. rubric:: lights
.. autosummary::
    :nosignatures:

    CylinderLight
    DiskLight
    DistantLight
    DomeLight
    Light
    RectLight
    SphereLight

.. Details

.. API

Shapes
^^^^^^

.. autoclass:: isaacsim.core.experimental.objects.Capsule
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.Cone
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.Cube
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.Cylinder
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.Shape
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.Sphere
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

Meshes
^^^^^^

.. autoclass:: isaacsim.core.experimental.objects.Mesh
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

Lights
^^^^^^

.. autoclass:: isaacsim.core.experimental.objects.CylinderLight
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.DiskLight
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.DistantLight
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.DomeLight
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.Light
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.RectLight
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.core.experimental.objects.SphereLight
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
