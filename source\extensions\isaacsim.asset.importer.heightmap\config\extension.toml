[package]
version = "2.0.6"
category = "Simulation"
title = "Height Map extension for Isaac Sim Occupancy Map"
description = "The Height Map extension provides tools for 2D Occupancy Map generation"
keywords = ["isaac", "occupancy map"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.core.deprecation_manager" = {}
"isaacsim.gui.components" = {}
"omni.kit.uiapp" = {}
"omni.kit.viewport.window" = {}

[[python.module]]
name = "isaacsim.asset.importer.heightmap"
