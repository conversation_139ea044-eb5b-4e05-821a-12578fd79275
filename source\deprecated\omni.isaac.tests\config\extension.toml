[package]
version = "2.0.7"
category = "Simulation"
title = "Isaac Sim Tests"
description = "Collection of tests for isaac sim that are not tied to a specific extension"
keywords = ["isaac", "tests"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.test.collection."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.test.collection" = {}

[[python.module]]
name = "omni.isaac.tests.tests"

[[test]]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
dependencies = [
    "omni.isaac.core",
    "omni.isaac.nucleus",
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
