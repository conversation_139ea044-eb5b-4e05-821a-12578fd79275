[package]
version = "4.8.3"
category = "Simulation"
title = "ROS 2 Bridge"
description = "The ROS 2 Bridge extension enables communication between the Isaac Sim and ROS 2 systems. It allows for the publishing and subscribing of ROS 2 topics and services via OmniGraph nodes and Action graphs. ROS 2 publishers, subscribers and services are only active when play is pressed. To enable this extension, ensure ROS 2 libraries are sourced in the terminal before running Isaac Sim or source the lightweight ROS 2 libraries included with Isaac Sim as an alternative."
keywords = ["isaac", "ROS2"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true
writeTarget.platform = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.nodes" = {}
"isaacsim.examples.browser" = {}
"isaacsim.gui.components" = {}
"isaacsim.robot.schema" = {}
"isaacsim.sensors.camera" = {}
"isaacsim.sensors.physics" = {}
"isaacsim.sensors.physx" = {}
"isaacsim.sensors.rtx" = {}
"isaacsim.storage.native" = {}
"omni.graph" = {}
"omni.graph.tools" = {}
"omni.isaac.dynamic_control" = {}
"omni.kit.menu.utils" = {}
"omni.kit.uiapp" = {}
"omni.pip.compute" = {}
"omni.replicator.core" = {}
"omni.syntheticdata" = {}
"usdrt.scenegraph" = {}

[[python.module]]
name = "isaacsim.ros2.bridge"

[[python.module]]
name = "isaacsim.ros2.bridge.tests"
public = false

[[python.module]]
name = "isaacsim.ros2.bridge.impl.samples.ros_samples"

[[python.module]]
name = "isaacsim.ros2.bridge.impl.samples.ros_waypoint_follower_sample"

[[python.module]]
name = "isaacsim.ros2.bridge.impl.samples.ros_moveit_sample"

[[python.module]]
name = "isaacsim.ros2.bridge.impl.og_shortcuts.og_shortcut_menu"

[settings]
# ROS 2 distributions to fallback onto if none were sourced.
exts."isaacsim.ros2.bridge".ros_distro = "humble"
# Whether ROS 2 publishers are allowed to publish even if there is no active subscription for their topics.
exts."isaacsim.ros2.bridge".publish_without_verification = false
# Whether to disable multithreading use in the *ROS2PublishImage* OmniGraph node.
exts."isaacsim.ros2.bridge".publish_multithreading_disabled = false

[fswatcher.patterns]
include = ["*.ogn", "*.py", "*.toml"]
exclude = ["Ogn*Database.py"]

[fswatcher.paths]
exclude = ["*/rclpy/*", "*/bin", "*/__pycache__/*", "*/.git/*"]

[[test]]

timeout = 900
dependencies = [
    "isaacsim.robot.wheeled_robots",
    "omni.graph.ui",
]

stdoutFailPatterns.exclude = [
    "*[Error] [omni.graph.core.plugin] /ActionGraph/ReadLidarPCL: [/ActionGraph] no prim path found for the lidar*",
    "*[Error] [omni.graph.core.plugin] /TestGraph/Template_isaacsim_ros2_bridge_ROS2PublishJointState: [/TestGraph] Could not find target prim*",
    "*[Error] [omni.graph.core.plugin] /TestGraph/Template_isaacsim_ros2_bridge_ROS2PublishImage: [/TestGraph] Width 0 or height 0 is not valid*",
    "*[Error] [omni.physx.plugin] Setting rigidBodyEnabled to false is not supported if the rigid body is part of an articulation*",
    "*[Error] [omni.graph.core.plugin] /TestGraph/Template_isaacsim_ros2_bridge_*: [/TestGraph] Unable to create ROS2 node, please check that namespace is valid*",
    '*[Error] [carb] [Plugin: omni.sensors.nv.lidar.ext.plugin] Dependency: [omni::sensors::lidar::IGenericModelOutputIOFactory v0.1] failed to be resolved.*', # feature not included in Windows
    "*[Error] [isaacsim.sensors.physics.plugin] *** error, No valid parent for /Hawk/Imu_Sensor with a rigid body api was found, sensor will not be created*",
    "*[Error] [isaacsim.sensors.physics.plugin] Failed to create imu sensor, parent prim is not found or invalid*",
    "*[Error] [omni.graph.core.plugin] Failed to create ComputeGraph \"/Graph/ROS_Nav2_Waypoint_Follower\". A graph already exists at this path*",
    "*[Error] [omni.graph.core.plugin] /TestGraph/Template_isaacsim_ros2_bridge_ROS2PublishTransformTree: [/TestGraph] Please specify at least one valid target prim for the ROS pose tree component*",
    "*[Error] [omni.physx.tensors.plugin] Failed to create simulation view: no active physics scene found*"
]


args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
    ### Extension specific args
    '--/exts/isaacsim.ros2.bridge/ros_distro = "humble"',
    "--/exts/isaacsim.ros2.bridge/publish_without_verification=1",

]

[[test]]
name = "startup"
args = [
    "--/app/settings/fabricDefaultStageFrameHistoryCount = 3",
]
