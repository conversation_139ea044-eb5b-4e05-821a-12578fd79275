[package]
version = "1.2.5"
category = "Simulation"
title = "Isaac Sim USD to URDF exporter"
description = "Extension that exports USD files to URDF"
keywords = ["isaac", "URDF"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true
writeTarget.platform = true # pip prebundle makes this extension os specific

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.utils" = {}
"isaacsim.gui.components" = {}
"omni.kit.pip_archive" = {}

[[python.module]]
path = "pip_prebundle"

[[python.module]]
name = "isaacsim.asset.exporter.urdf"

[[python.module]]
name = "isaacsim.asset.exporter.urdf.tests"

[[test]]

dependencies = []
