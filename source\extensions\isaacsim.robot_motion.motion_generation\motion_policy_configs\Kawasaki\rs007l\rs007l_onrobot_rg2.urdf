<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from urdf/rs007l_onrobot_rg2.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="khi_rs007l">
  <transmission name="$join1_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <actuator name="joint1_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
    <joint name="joint1">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
  </transmission>
  <transmission name="$join2_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <actuator name="joint2_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
    <joint name="joint2">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
  </transmission>
  <transmission name="$join3_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <actuator name="joint3_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
    <joint name="joint3">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
  </transmission>
  <transmission name="$join4_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <actuator name="joint4_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
    <joint name="joint4">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
  </transmission>
  <transmission name="$join5_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <actuator name="joint5_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
    <joint name="joint5">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
  </transmission>
  <transmission name="$join6_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <actuator name="joint6_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
    <joint name="joint6">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
  </transmission>
  <!-- gazebo plugin -->
  <gazebo>
    <plugin filename="libgazebo_ros_control.so" name="gazebo_ros_control">
      <robotNamespace>/</robotNamespace>
    </plugin>
    <!--
    <plugin name="gazebo_ros_power_monitor_controller" filename="libgazebo_ros_power_monitor.so">
      <alwaysOn>true</alwaysOn>
      <updateRate>1.0</updateRate>
      <timeout>5</timeout>
      <powerStateTopic>power_state</powerStateTopic>
      <powerStateRate>10.0</powerStateRate>
      <fullChargeCapacity>87.78</fullChargeCapacity>     
      <dischargeRate>-474</dischargeRate>
      <chargeRate>525</chargeRate>
      <dischargeVoltage>15.52</dischargeVoltage>
      <chargeVoltage>16.41</chargeVoltage>
    </plugin>
-->
  </gazebo>
  <!-- link rviz colors -->
  <material name="White">
    <color rgba="1 1 1 1"/>
  </material>
  <material name="Black">
    <color rgba="0 0 0 1"/>
  </material>
  <!-- rs007l start -->
  <!-- Link 0 -->
  <link name="base_link">
    <visual>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J0.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J0.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="11"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <!-- Link 1 -->
  <joint name="joint1" type="revolute">
    <axis rpy="0 0 0" xyz="0 0 -1"/>
    <limit effort="1000.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.457718232379019"/>
    <origin rpy="0 0 0" xyz="0 0 0.36"/>
    <parent link="base_link"/>
    <child link="link1"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link1">
    <visual>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J1.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J1.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="8.188"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <!-- Link 2 -->
  <joint name="joint2" type="revolute">
    <axis rpy="0 0 0" xyz="0 0 1"/>
    <limit effort="1000.0" lower="-2.356194490192345" upper="2.356194490192345" velocity="5.410520681182422"/>
    <origin rpy="0 -1.5707963267948966 0" xyz="0 0 0.0"/>
    <parent link="link1"/>
    <child link="link2"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link2">
    <visual>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J2.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J2.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="6.826"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <!-- Link 3 -->
  <joint name="joint3" type="revolute">
    <axis rpy="0 0 0" xyz="0 0 -1"/>
    <limit effort="1000.0" lower="-2.7401669256310974" upper="2.7401669256310974" velocity="7.155849933176751"/>
    <origin rpy="0 0 0" xyz="0.455 0 0"/>
    <parent link="link2"/>
    <child link="link3"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link3">
    <visual>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J3.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J3.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="5.236"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <!-- Link 4 -->
  <joint name="joint4" type="revolute">
    <axis rpy="0 0 0" xyz="0 0 1"/>
    <limit effort="1000.0" lower="-3.490658503988659" upper="3.490658503988659" velocity="9.599310885968812"/>
    <origin rpy="0 1.5707963267948966 0" xyz="0.0925 0 0"/>
    <parent link="link3"/>
    <child link="link4"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link4">
    <visual>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J4.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 0 0" xyz="0 0 0.3825"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J4.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 0 0" xyz="0 0 0.3825"/>
    </collision>
    <inertial>
      <mass value="5.066"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <!-- Link 5 -->
  <joint name="joint5" type="revolute">
    <axis rpy="0 0 0" xyz="0 0 -1"/>
    <limit effort="1000.0" lower="-2.181661564992912" upper="2.181661564992912" velocity="9.599310885968812"/>
    <origin rpy="0 -1.5707963267948966 0" xyz="0 0 0.3825"/>
    <parent link="link4"/>
    <child link="link5"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link5">
    <visual>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J5.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J5.stl"/>
      </geometry>
      <material name="White"/>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="1.625"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <!-- Link 6 -->
  <joint name="joint6" type="revolute">
    <axis rpy="0 0 0" xyz="0 0 1"/>
    <limit effort="1000.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="17.453292519943297"/>
    <origin rpy="0 1.5707963267948966 0" xyz="0.078 0 0"/>
    <parent link="link5"/>
    <child link="link6"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link6">
    <visual>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J6.stl"/>
      </geometry>
      <material name="Black"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://khi_rs_description/meshes/RS007L_J6.stl"/>
      </geometry>
      <material name="Black"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="0.625"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <!-- Fix rs007l to world -->
  <link name="world"/>
  <joint name="world2base" type="fixed">
    <parent link="world"/>
    <child link="base_link"/>
    <origin rpy="0 0 -1.5707963267948966" xyz="0 0 0"/>
  </joint>
  <link name="onrobot_rg2_base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.7"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/visual/base_link.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/collision/base_link.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="left_outer_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/visual/outer_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/collision/outer_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="left_inner_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/visual/inner_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/collision/inner_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="left_inner_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/visual/inner_finger.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/collision/inner_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="right_outer_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/visual/outer_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/collision/outer_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="right_inner_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/visual/inner_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/collision/inner_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="right_inner_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/visual/inner_finger.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg2_visualization/meshes/collision/inner_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="finger_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.017178 0.125797"/>
    <parent link="onrobot_rg2_base_link"/>
    <child link="left_outer_knuckle"/>
    <axis xyz="-1 0 0"/>
    <limit effort="1000" lower="-0.558505" upper="0.785398" velocity="2.0"/>
  </joint>
  <joint name="left_inner_knuckle_joint" type="revolute">
    <origin rpy="0 0 0.0" xyz="0 -0.007678 0.1422970"/>
    <parent link="onrobot_rg2_base_link"/>
    <child link="left_inner_knuckle"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.558505" upper="0.785398" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="-1" offset="0"/>
  </joint>
  <joint name="left_inner_finger_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.039592 0.038177000000000016"/>
    <parent link="left_outer_knuckle"/>
    <child link="left_inner_finger"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.872665" upper="0.872665" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="1" offset="0"/>
  </joint>
  <joint name="right_outer_knuckle_joint" type="revolute">
    <origin rpy="0 0 3.141592653589793" xyz="0 0.017178 0.125797"/>
    <parent link="onrobot_rg2_base_link"/>
    <child link="right_outer_knuckle"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.558505" upper="0.785398" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="-1" offset="0"/>
  </joint>
  <joint name="right_inner_knuckle_joint" type="revolute">
    <origin rpy="0 0 -3.141592653589793" xyz="0 0.007678 0.1422970"/>
    <parent link="onrobot_rg2_base_link"/>
    <child link="right_inner_knuckle"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.558505" upper="0.785398" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="-1" offset="0"/>
  </joint>
  <joint name="right_inner_finger_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.039592 0.038177000000000016"/>
    <parent link="right_outer_knuckle"/>
    <child link="right_inner_finger"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.872665" upper="0.872665" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="1" offset="0"/>
  </joint>
  <transmission name="finger_joint_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="finger_joint">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="finger_joint_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <!-- Fix rg2 to rs007l -->
  <joint name="rs007l2rg2" type="fixed">
    <parent link="link6"/>
    <child link="onrobot_rg2_base_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>

  <link name="gripper_center"/>
  <joint name="gripper_center_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.0 0.0 .2"/>
    <parent link="onrobot_rg2_base_link"/>
    <child link="gripper_center"/>
  </joint>
</robot>

