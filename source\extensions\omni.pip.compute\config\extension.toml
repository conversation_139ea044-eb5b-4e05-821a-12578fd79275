[core]
# Load as early as possible
order = -1000
reloadable = false

[package]
version = "1.6.2"
category = "Internal"
title = "Compute package Pip Archive"
description = "General packages for compute"
keywords = ["isaac", "pip"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true
writeTarget.platform = true # pip prebundle makes this extension os specific
writeTarget.python = true

[dependencies]
"omni.kit.pip_archive" = {} # import the base kit python archive

# We bundle not only "scipy" package, but many other. No need to mention all of them, just importing one will add whole
# folder (pip_prebundle) to sys.path.
[[python.module]]
path = "pip_prebundle/cv2"

[[python.module]]
path = "pip_prebundle"

# That is empty module, added here only to make tests discoverable in test run
[[python.module]]
name = "omni.pip.compute"
