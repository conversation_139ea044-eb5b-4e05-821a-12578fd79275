mdl 1.4;

import ::anno::author;
import ::anno::copyright_notice;
import ::anno::description;
import ::anno::display_name;
import ::anno::in_group;
import ::anno::key_words;
import ::anno::hidden;
import ::df::*;
import ::state::normal;
import ::math::*;
import ::base::*;
import ::state::texture_tangent_u;

export material voltest_02(
	color absorption 			= color(0.8,0.8,0.8),
	color scattering 			= color(.5),
	color transmission_color = color(1,1,1),
	float distance_scale 		= 1,
	float emissive_scale = 0,
	color emission_color = color(1,1,1)
)
 = 
let {
	
        material_volume tmp4 = material_volume(
			scattering: vdf(), 
			absorption_coefficient: (distance_scale <= 0)? color(0): math::log(absorption) / -distance_scale,
			scattering_coefficient: (distance_scale <= 0)? color(0): math::log(scattering) / -distance_scale
		);
        bsdf frosted_bsdf = df::specular_bsdf(
			tint: transmission_color,
			mode: df::scatter_transmit
		);

    } in
	
material(
        thin_walled: false,
        surface: material_surface(
			scattering: frosted_bsdf,
			emission:
            material_emission (
                emission:  df::diffuse_edf (),
                intensity: emission_color * emissive_scale
                )
		),
        volume: tmp4
);
