# SPDX-FileCopyrightText: Copyright (c) 2024 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: LicenseRef-NvidiaProprietary
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.

[package]
version = "2.4.13"  # Semantic Versionning is used: https://semver.org/
category = "Simulation"
title = "Urdf Importer Extension"
description = "URDF Importer"
keywords = ["urdf", "importer", "isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.examples.browser" = {order = 1000}
"isaacsim.gui.components" = {order = 1000}
"isaacsim.robot.schema" = {}
"omni.kit.browser.folder.core" = {}
"omni.kit.commands" = {}
"omni.kit.helper.file_utils" = {}
"omni.kit.pip_archive" = {} # pulls in pillow
"omni.kit.tool.asset_importer" = {}
"omni.kit.uiapp" = {}
"omni.kit.viewport.utility" = {}
"omni.kit.window.content_browser" = {}
"omni.kit.window.extensions" = {}
"omni.kit.window.filepicker" = {}
"omni.kit.window.property" = {}
"omni.usd.libs" = {}
"omni.usd.schema.physx" = {}

[[python.module]]
name = "isaacsim.asset.importer.urdf"

[[python.module]]
name = "isaacsim.asset.importer.urdf.tests"

[[python.module]]
name = "isaacsim.asset.importer.urdf.impl.ui"

[[python.module]]
name = "isaacsim.asset.importer.urdf.impl.samples.import_carter"

[[python.module]]
name = "isaacsim.asset.importer.urdf.impl.samples.import_franka"

[[python.module]]
name = "isaacsim.asset.importer.urdf.impl.samples.import_kaya"

[[python.module]]
name = "isaacsim.asset.importer.urdf.impl.samples.import_ur10"

[[native.plugin]]
path = "bin/*.plugin"
recursive = false

[settings]
# Set the initial width of the file picker to 390
exts."omni.kit.window.filepicker".detail_width_init = 390
# Set the minimum width of the file picker to 390
exts."omni.kit.window.filepicker".detail_width_min = 390
# Set the maximum width of the file picker to 600
exts."omni.kit.window.filepicker".detail_width_max = 600

[[test]]
dependencies = [ "omni.hydra.usdrt_delegate"]


args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]

[documentation]
pages = [
    "docs/Overview.md",
    "docs/CHANGELOG.md",
]
