[package]
version = "2.0.9"
category = "Simulation"
title = "Isaac Sim Conveyor belt utility UI"
description="UI components for omni.isaac.conveyor"
keywords = ["kit", "example", "ui"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.asset.gen.conveyor.ui."

[dependencies]
"isaacsim.asset.gen.conveyor.ui" = {}
"isaacsim.core.deprecation_manager" = {}

[[python.module]]
name = "omni.isaac.conveyor.ui"

[[python.module]]
name = "omni.isaac.conveyor.ui.tests"

[[test]]
dependencies = [
    "omni.kit.ui_test"
]
stdoutFailPatterns.exclude = [
    "*[Error] [carb.glinterop.plugin] GLInteropContext::init: carb::windowing is not available*",
]
