# Changelog
## [1.0.5] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.0.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.0.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-10-03
### Deprecated
- Extension deprecated since <PERSON> Sim 4.5.0.

## [0.1.3] - 2024-03-04
### Changed
- Updated omnigraph nodes to use per instance state instead of internal state

## [0.1.2] - 2023-08-10
### Changed
- Changed robot prim type from bundle to target

## [0.1.1] - 2023-06-12
### Changed
- Update to kit 105.1
- make targetPrim on IsaacPickPlaceController node optional

## [0.1.0] - 2022-11-22
### Added
- Initial version
