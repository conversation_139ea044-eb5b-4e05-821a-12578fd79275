# Changelog

## [1.1.0] - 2025-06-10
### Changed
- Set the default IP address for the Jupyter and extension servers to 127.0.0.1

### Removed
- Remove the classic Jupyter Notebook interface

## [1.0.14] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.0.13] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [1.0.12] - 2025-05-10
### Changed
- Cleanup extension.toml

## [1.0.11] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.0.10] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.0.9] - 2025-01-27
### Changed
- Updated docs link

## [1.0.8] - 2025-01-23
### Fixed
- Remove menu items created when shutting down the extension
- Run socket server closing coroutine thread-safe

## [1.0.7] - 2025-01-22
### Changed
- Add `requests` and `h11` to extension's Python dependencies

## [1.0.6] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.0.5] - 2025-01-17
### Changed
- Temporarily direction for docs link

## [1.0.4] - 2024-12-07
### Fixed
- Moved from deprecated editor_menu.add_item to omni.kit.menu.utils

## [1.0.3] - 2024-11-19
### Fixed
- Startup test

## [1.0.2] - 2024-10-28
### Changed
- Remove test imports from runtime

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-09-23
### Changed
- Extension renamed to isaacsim.code_editor.jupyter

## [0.1.0] - 2024-01-24
### Added
-   Initial release
