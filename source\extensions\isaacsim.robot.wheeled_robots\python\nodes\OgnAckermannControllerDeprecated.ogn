{"AckermannControllerDeprecated": {"version": 1, "description": "NOTE: DEPRECATED Ackermann Controller. Please you replace with the AckermannController OmniGraph node instead.", "language": "Python", "categories": {"isaacWheeledRobots": "<PERSON><PERSON><PERSON> controller for wheels in Isaac <PERSON>"}, "metadata": {"uiName": "Ackermann Controller Deprecated", "hidden": "true"}, "$comment": "", "inputs": {"execIn": {"type": "execution", "description": "The input execution"}, "acceleration": {"type": "double", "description": "Desired forward acceleration for the robot in m/s^2", "default": 1.0}, "speed": {"type": "double", "description": "Desired forward speed in m/s", "default": 1.0}, "steeringAngle": {"type": "double", "description": "Desired virtual angle in radians. Corresponds to the yaw of a virtual wheel located at the center of the front axle. By default it is positive for turning left and negative for turning right for front wheel drive.", "default": 0.0}, "currentLinearVelocity": {"type": "vectord[3]", "description": "Current linear velocity of the robot in m/s", "default": [0.0, 0.0, 0.0]}, "wheelBase": {"type": "double", "description": "Distance between the front and rear axles of the robot in meters", "default": 0.0}, "trackWidth": {"type": "double", "description": "Distance between the left and right rear wheels of the robot in meters", "default": 0.0}, "turningWheelRadius": {"type": "double", "description": "Radius of the front wheels of the robot in meters", "default": 0.0}, "maxWheelVelocity": {"type": "double", "description": "Maximum angular velocity of the robot wheel in rad/s", "default": 100000}, "invertSteeringAngle": {"type": "bool", "description": "Flips the sign of the steering angle, Set to true for rear wheel steering", "default": false}, "useAcceleration": {"type": "bool", "description": "Use acceleration as an input, Set to false to use speed as input instead", "default": false}, "maxWheelRotation": {"type": "double", "description": "Maximum angle of rotation for the front wheels in radians", "default": 6.28}, "DT": {"type": "double", "description": "Delta time for the simulation step"}}, "outputs": {"execOut": {"type": "execution", "description": "The output execution"}, "leftWheelAngle": {"type": "double", "description": "<PERSON>le for the left turning wheel in radians"}, "rightWheelAngle": {"type": "double", "description": "Angle for the right turning wheel in radians"}, "wheelRotationVelocity": {"type": "double", "description": "Angular velocity for the turning wheels in rad/s"}}}}