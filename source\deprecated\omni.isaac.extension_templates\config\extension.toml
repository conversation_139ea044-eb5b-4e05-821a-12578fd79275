[package]
version = "2.0.7"
category = "Simulation"
title = "Generate Extension"
description = "UI tool for generating extension templates for different common workflows"
keywords = []
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.examples.extension."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.examples.extension" = {}

[[python.module]]
name = "omni.isaac.extension_templates"
