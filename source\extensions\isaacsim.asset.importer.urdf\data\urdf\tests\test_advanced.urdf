<?xml version="1.0" encoding="UTF-8"?>
<robot name="test_advanced">
  <gazebo>
    <plugin filename="libgazebo_ros_control.so" name="gazebo_ros_control">
      <robotNamespace>/gripper</robotNamespace>
      <robotSimType>gazebo_ros_control/DefaultRobotHWSim</robotSimType>
      <legacyModeNS>true</legacyModeNS>
    </plugin>
  </gazebo>

  <material name="blue">
    <color rgba="0 0 .8 1"/>
  </material>
  <material name="red">
    <color rgba="0.8 0 0 1"/>
  </material>
  <material name="green">
    <color rgba="0 0.8 0 1"/>
  </material>
  <material name="yellow">
    <color rgba="0.8 0.8 0 1"/>
  </material>
  <material name="cyan">
    <color rgba="0 0.8 0.8 1"/>
  </material>



  <link name="root_link"/>

  <joint name="root_to_base" type="fixed">
    <parent link="root_link"/>
    <child link="base_link"/>
  </joint>

  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.3 .3 .2"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.3 .3 .2"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>

  <joint name="base_joint" type="continuous">
    <parent link="base_link"/>
    <child link="link_1"/>
    <axis xyz="0 0 1"/>
    <origin xyz="0 0 0.45"/>
  </joint>

  <link name="link_1">
    <visual>
      <geometry>
        <cylinder length="0.8" radius=".1"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.8" radius=".1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>


  <joint name="elbow_joint" type="revolute">
    <parent link="link_1"/>
    <child link="link_2"/>
    <origin xyz="0 0 0.4" rpy="1.571 0 0"/>
    <axis xyz="1 0 0"/>

    <calibration rising="0.0" falling="0.1"/>
    <dynamics damping="0.10" friction="0.1"/>
    <limit effort="1000.0" lower="-0.6" upper="0.6" velocity="0.5"/>
    <safety_controller k_velocity="10" k_position="15" soft_lower_limit="-2.0" soft_upper_limit="0.5"/>

  </joint>

  <transmission name="trans_1">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="elbow_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="elbow_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>12</mechanicalReduction>
    </actuator>
  </transmission>

  <link name="link_2">
    <visual>
      <geometry>
        <cylinder length="0.6" radius=".06"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.6" radius=".06"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>

  <joint name="wrist_joint" type="continuous">
    <parent link="link_2"/>
    <child link="palm_link"/>
    <origin xyz="0 0 0.33"/>
    <axis xyz="0 0 1"/>
  </joint>

  <link name="palm_link">
    <visual>
      <geometry>
        <box size="0.2 .06 .06"/>
      </geometry>
      <material name="yellow"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.2 .06 .06"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>


  <sensor name="my_camera_sensor" update_rate="20">
    <parent link="palm_link"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <camera>
      <image width="640" height="480" hfov="1.5708" format="RGB8" near="0.01" far="50.0"/>
    </camera>
  </sensor>


  <joint name="finger_1_joint" type="prismatic">
    <parent link="palm_link"/>
    <child link="finger_link_1"/>
    <limit effort="1000.0" lower="-0.02" upper="0.08" velocity="0.5"/>
    <origin rpy="0 1.571 0" xyz="-0.10 0 0.05"/>
    <axis xyz="0 0 1"/>
  </joint>

  <link name="finger_link_1">
    <visual>
      <geometry>
        <box size="0.06 .01 .01"/>
      </geometry>
      <material name="cyan"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.06 .1 .1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>

  <joint name="finger_2_joint" type="prismatic">
    <parent link="palm_link"/>
    <child link="finger_link_2"/>
    <limit effort="1000.0" lower="-0.08" upper="0.02" velocity="0.5"/>
    <origin rpy="0 1.571 0" xyz="0.10 0 0.05"/>
    <axis xyz="0 0 1"/>
  </joint>

  <link name="finger_link_2">
    <visual>
      <geometry>
        <box size="0.06 .01 .01"/>
      </geometry>
      <material name="cyan"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.06 .1 .1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>



</robot>
