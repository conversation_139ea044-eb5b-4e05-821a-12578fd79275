# Changelog
## [0.1.12] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [0.1.11] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.1.10] - 2025-05-10
### Changed
- Enable FSD in test settings

## [0.1.9] - 2025-04-11
### Changed
- Update Isaac Sim robot asset path
- Update Isaac Sim robot asset path for the IsaacSim folder

## [0.1.8] - 2025-04-09
### Changed
- Update all test args to be consistent

## [0.1.7] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [0.1.6] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [0.1.5] - 2025-03-11
### Changed
- Switch asset root for tests to internal nucleus

## [0.1.4] - 2025-01-27
### Changed
- Updated docs link

## [0.1.3] - 2025-01-26
### Changed
- Update test settings

## [0.1.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [0.1.1] - 2025-01-17
### Changed
- Temporarily changed docs link

## [0.1.0] - 2024-12-16
### Added
- Initial version of Isaac Sim Physics sensor extension examples
