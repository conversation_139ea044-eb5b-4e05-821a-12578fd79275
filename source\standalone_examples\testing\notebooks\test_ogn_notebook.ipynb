{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.\n", "# SPDX-License-Identifier: Apache-2.0\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# http://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n"]}, {"cell_type": "code", "execution_count": null, "id": "15c98eca", "metadata": {}, "outputs": [], "source": ["from isaacsim import SimulationApp\n", "\n", "simulation_app = SimulationApp()\n", "import omni\n", "\n", "simulation_app.update()\n", "omni.usd.get_context().new_stage()\n", "simulation_app.update()\n"]}, {"cell_type": "code", "execution_count": null, "id": "8743b6c2", "metadata": {}, "outputs": [], "source": ["import omni.graph.core as og\n", "\n", "keys = og.Controller.Keys\n", "(graph, (tick_node, test_node, str_node), _, _) = og.Controller.edit(\n", "    {\"graph_path\": \"/controller_graph\", \"evaluator_name\": \"push\"},\n", "    {\n", "        keys.CREATE_NODES: [\n", "            (\"OnTick\", \"omni.graph.action.OnTick\"),\n", "            (\"IsaacTest\", \"isaacsim.core.nodes.IsaacTestNode\"),\n", "            (\"TestStr\", \"omni.graph.nodes.ConstantString\"),\n", "        ],\n", "        keys.SET_VALUES: [\n", "            (\"TestStr.inputs:value\", \"Hello\"),\n", "            (\"OnTick.inputs:onlyPlayback\", False),  # always tick\n", "        ],\n", "        keys.CONNECT: [\n", "            (\"OnTick.outputs:tick\", \"IsaacTest.inputs:execIn\"),\n", "            (\"TestStr.inputs:value\", \"IsaacTest.inputs:input\"),\n", "        ],\n", "    },\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "57ae724f", "metadata": {}, "outputs": [], "source": ["input_attr = og.Controller.attribute(\"inputs:value\", str_node)\n", "output_attr = og.Controller.attribute(\"outputs:output\", test_node)\n", "og.DataView.set(input_attr, \"Hello\")\n", "simulation_app.update()\n", "value = og.DataView.get(output_attr)\n", "print(value)\n", "if value != \"Hello\":\n", "    raise ValueError(\"Output does not equal Hello\")\n", "simulation_app.update()\n", "og.DataView.set(input_attr, \"Goodbye\")\n", "simulation_app.update()\n", "value = og.DataView.get(output_attr)\n", "print(value)\n", "if value != \"Goodbye\":\n", "    raise ValueError(\"Output does not equal Goodbye\")\n", "\n", "simulation_app.update()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f4687a03", "metadata": {}, "outputs": [], "source": ["# Cleanup application\n", "simulation_app.close()\n"]}], "metadata": {"kernelspec": {"display_name": "Isaac Sim Python 3", "language": "python", "name": "isaac_sim_python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}}, "nbformat": 4, "nbformat_minor": 5}