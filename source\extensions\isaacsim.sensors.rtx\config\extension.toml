[package]
version = "15.3.1"
category = "Simulation"
title = "Isaac Sim Isaac Sensor Simulation"
description = "Provides APIs for RTX-based sensors, including RTX Lidar & RTX Radar."
keywords = ["isaac", "physics", "robotics"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.nodes" = {}
"isaacsim.gui.components" = {}
"isaacsim.robot.schema" = {}
"isaacsim.storage.native" = {}
"isaacsim.util.debug_draw" = {}
"omni.graph" = {}
"omni.graph.action" = {}
"omni.kit.numpy.common" = {}
"omni.kit.pip_archive" = {} # pulls in numpy
"omni.replicator.core" = {}
"omni.sensors.nv.common" = {}
"omni.sensors.nv.ids" = {}
"omni.sensors.nv.lidar" = {}
"omni.sensors.nv.radar" = {}
"omni.syntheticdata" = {}
"omni.timeline" = {} # Needed for simulation to occur
"omni.usd" = {} # needed for call to add aov
"omni.usd.schema.omni_sensors" = {}
"usdrt.scenegraph" = {}

[[python.module]]
name = "isaacsim.sensors.rtx"

[[python.module]]
name = "isaacsim.sensors.rtx.generic_model_output"

[[python.module]]
name = "isaacsim.sensors.rtx.tests"
public = false

[[native.plugin]]
path = "bin/*.plugin"
recursive = false

[settings]
# List of directories which renderer will search to find Lidar profile for (deprecated) camera-based Lidar
app.sensors.nv.lidar.profileBaseFolder=[
    "${omni.sensors.nv.common}/data/lidar/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/HESAI/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/NVIDIA/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/Ouster/OS0/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/Ouster/OS1/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/Ouster/OS2/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/SICK/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/SLAMTEC/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/Velodyne/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/ZVISION/",
    "${isaacsim.sensors.rtx}/data/lidar_configs/"
]

# Renderer copies Lidar return buffer onto CPU before postprocessing
app.sensors.nv.lidar.outputBufferOnGPU = false
# Renderer copies Radar return buffer onto CPU before postprocessing
app.sensors.nv.radar.outputBufferOnGPU = false
# Enable motion BVH for sensor motion effects
renderer.raytracingMotion.enabled=true
# Enable motion raytracing Hydra engine masking support
renderer.raytracingMotion.enableHydraEngineMasking=true
# Enable motion raytracing for Hydra engines 0-4
renderer.raytracingMotion.enabledForHydraengines="0,1,2,3,4"
# Enable non-visual materials using default hard-coded map of material names to material IDs
rtx.materialDb.rtSensorNameToIdMap="DefaultMaterial:0;AsphaltStandardMaterial:1;AsphaltWeatheredMaterial:2;VegetationGrassMaterial:3;WaterStandardMaterial:4;GlassStandardMaterial:5;FiberGlassMaterial:6;MetalAlloyMaterial:7;MetalAluminumMaterial:8;MetalAluminumOxidizedMaterial:9;PlasticStandardMaterial:10;RetroMarkingsMaterial:11;RetroSignMaterial:12;RubberStandardMaterial:13;SoilClayMaterial:14;ConcreteRoughMaterial:15;ConcreteSmoothMaterial:16;OakTreeBarkMaterial:17;FabricStandardMaterial:18;PlexiGlassStandardMaterial:19;MetalSilverMaterial:20"

[[test]]
timeout = 900
stdoutFailPatterns.exclude = [
    # This is excluded in at least 3 kit tests.
    "*Missing call to destroyResourceBindingSignature()*",
    "*Error processing node attribute `outputs:pointInstanceDataType`: dataType attribute is empty*",
    "*RuntimeWarning: invalid value encountered in divide*"
]
stdoutFailPatterns.include = [
    "*Could not open asset * for reference*",
    "*CUDA error*"
]
args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]

[[test]]
name = "startup"
args = [
    "--/app/settings/fabricDefaultStageFrameHistoryCount = 3",
]
