{"comment1": "parameters obtained from https://data.ouster.io/downloads/datasheets/datasheet-rev06-v2p5-os2.pdf", "comment2": "parameters without comments are assumed correct, but will ultimately need checking", "class": "sensor", "type": "lidar", "name": "OS2 REV6 128 20hz @ 1024 resolution", "comment3": "Ouster OS2 REV6 128 channels @ 20Hz 1024 horizontal resolution", "driveWorksId": "GENERIC", "profile": {"scanType": "rotary", "intensityProcessing": "normalization", "rotationDirection": "CW", "rayType": "IDEALIZED", "nearRangeM": 1.0, "comment4": "OPTICAL PERFORMANCE-Minimum Range 1.0 m for point cloud data", "farRangeM": 240.0, "comment5": "OPTICAL PERFORMANCE- Range 210 m @ >90% detection probability, 100 klx sunlight 240 m @ >50% detection probability, 100 klx sunlight", "startAzimuthDeg": 0.0, "comment6": "x+ is 0°  y+ is 90° (and z is up, right handed)", "endAzimuthDeg": 360.0, "comment7": "OPTICAL PERFORMANCE- Field of View Horizontal: 360°", "upElevationDeg": 11.25, "comment8": "OPTICAL PERFORMANCE- Field of View Vertical: 45° (+22.5° to -22.5°)", "downElevationDeg": -11.25, "rangeResolutionM": 0.001, "comment9": "OPTICAL PERFORMANCE- Range Resolution 0.1 cm", "rangeAccuracyM": 0.03, "comment10": "OPTICAL PERFORMANCE- Range Accuracy ±3 cm for lambertian targets, ±10 cm for retroreflectors", "avgPowerW": 0.002, "minReflectance": 0.1, "comment11": "OPTICAL PERFORMANCE - Range (10% Lambertian reflectivity, 2048 @ 10 Hz mode)", "minReflectanceRange": 100.0, "comment12": "OPTICAL PERFORMANCE - Range 80 m @ >90% detection probability, 100 klx sunlight 100 m @ >50% detection probability, 100 klx sunlight", "wavelengthNm": 865.0, "comment13": "LASER-  Laser Wavelength 865 nm", "pulseTimeNs": 6, "comment14": "These add noise to the emitter direction to each point randomly if Std is not 0.0", "azimuthErrorMean": 0.0, "azimuthErrorStd": 0.01, "comment15": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Horizontal: ±0.01°", "elevationErrorMean": 0.0, "elevationErrorStd": 0.01, "comment16": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Vertical: ±0.01°", "maxReturns": 2, "scanRateBaseHz": 20.0, "reportRateBaseHz": 20480, "numberOfEmitters": 128, "emitterStateCount": 1, "emitterStates": [{"comment19": "beam_azimuth_angles from get_beam_intrinsics", "azimuthDeg": [2.07, 0.66, -0.71, -2.08, 2.07, 0.69, -0.7, -2.09, 2.06, 0.67, -0.71, -2.06, 2.06, 0.69, -0.71, -2.07, 2.07, 0.69, -0.71, -2.07, 2.07, 0.69, -0.7, -2.08, 2.05, 0.68, -0.69, -2.06, 2.06, 0.69, -0.69, -2.07, 2.08, 0.69, -0.69, -2.06, 2.08, 0.69, -0.69, -2.07, 2.08, 0.69, -0.69, -2.05, 2.08, 0.7, -0.68, -2.06, 2.09, 0.7, -0.67, -2.05, 2.08, 0.7, -0.68, -2.05, 2.08, 0.71, -0.67, -2.06, 2.08, 0.71, -0.68, -2.05, 2.07, 0.71, -0.67, -2.05, 2.08, 0.7, -0.68, -2.05, 2.09, 0.71, -0.66, -2.05, 2.09, 0.71, -0.67, -2.05, 2.09, 0.71, -0.66, -2.05, 2.1, 0.71, -0.66, -2.05, 2.09, 0.73, -0.67, -2.05, 2.1, 0.72, -0.66, -2.05, 2.09, 0.72, -0.66, -2.05, 2.11, 0.73, -0.69, -2.03, 2.1, 0.72, -0.65, -2.04, 2.11, 0.72, -0.67, -2.05, 2.1, 0.72, -0.66, -2.04, 2.11, 0.72, -0.66, -2.04, 2.1, 0.72, -0.65, -2.03, 2.11, 0.73, -0.66, -2.04], "comment20": "beam_altitude_angles from get_beam_intrinsics", "elevationDeg": [10.76, 10.58, 10.42, 10.24, 10.1, 9.93, 9.76, 9.57, 9.41, 9.24, 9.09, 8.91, 8.75, 8.57, 8.41, 8.23, 8.08, 7.9, 7.74, 7.55, 7.39, 7.21, 7.03, 6.86, 6.7, 6.53, 6.35, 6.18, 6.02, 5.83, 5.67, 5.5, 5.32, 5.14, 4.97, 4.81, 4.63, 4.46, 4.28, 4.11, 3.95, 3.76, 3.58, 3.43, 3.24, 3.07, 2.9, 2.72, 2.55, 2.37, 2.21, 2.03, 1.87, 1.69, 1.51, 1.32, 1.16, 0.98, 0.81, 0.63, 0.46, 0.29, 0.11, -0.05, -0.25, -0.42, -0.58, -0.76, -0.93, -1.11, -1.28, -1.45, -1.63, -1.81, -1.97, -2.16, -2.32, -2.5, -2.68, -2.85, -3.02, -3.19, -3.37, -3.55, -3.73, -3.9, -4.07, -4.25, -4.4, -4.58, -4.75, -4.94, -5.11, -5.28, -5.46, -5.64, -5.8, -5.96, -6.15, -6.32, -6.49, -6.66, -6.84, -7, -7.17, -7.35, -7.51, -7.69, -7.86, -8.04, -8.22, -8.38, -8.55, -8.72, -8.89, -9.06, -9.23, -9.4, -9.57, -9.74, -9.91, -10.07, -10.24, -10.41, -10.58, -10.75, -10.92, -11.09], "fireTimeNs": [190, 571, 952, 1333, 1714, 2095, 2476, 2857, 3238, 3619, 4000, 4381, 4762, 5143, 5524, 5905, 6286, 6667, 7048, 7429, 7810, 8191, 8572, 8953, 9334, 9715, 10096, 10477, 10858, 11239, 11620, 12001, 12382, 12763, 13144, 13525, 13906, 14287, 14668, 15049, 15430, 15811, 16192, 16573, 16954, 17335, 17716, 18097, 18478, 18859, 19240, 19621, 20002, 20383, 20764, 21145, 21526, 21907, 22288, 22669, 23050, 23431, 23812, 24193, 24574, 24955, 25336, 25717, 26098, 26479, 26860, 27241, 27622, 28003, 28384, 28765, 29146, 29527, 29908, 30289, 30670, 31051, 31432, 31813, 32194, 32575, 32956, 33337, 33718, 34099, 34480, 34861, 35242, 35623, 36004, 36385, 36766, 37147, 37528, 37909, 38290, 38671, 39052, 39433, 39814, 40195, 40576, 40957, 41338, 41719, 42100, 42481, 42862, 43243, 43624, 44005, 44386, 44767, 45148, 45529, 45910, 46291, 46672, 47053, 47434, 47815, 48196, 48577]}], "intensityMappingType": "LINEAR"}}