{"OgnSampleBetweenSpheres": {"version": 1, "description": "Assignes uniformly sampled between two spheres", "language": "Python", "categoryDefinitions": "config/CategoryDefinition.json", "categories": ["isaacReplicatorExamples"], "icon": "icons/isaac-sim.svg", "metadata": {"uiName": "Sample Between Spheres"}, "inputs": {"prims": {"type": "target", "description": "prims to randomize", "default": []}, "execIn": {"type": "execution", "description": "exec", "default": 0}, "radius1": {"type": "float", "description": "inner sphere radius", "default": 0.5}, "radius2": {"type": "float", "description": "outer sphere radius", "default": 1.0}}, "outputs": {"execOut": {"type": "execution", "description": "exec"}}}}