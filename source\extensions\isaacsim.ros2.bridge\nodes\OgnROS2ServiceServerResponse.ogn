{"OgnROS2ServiceServerResponse": {"version": 1, "icon": "icons/isaac-sim.svg", "description": ["This node is a generic service server that provides interface for a ROS service. The response fields of the service are parsed and are made accessible via the node based on the service specified from messagePackage, messageSubfolder, messageName. The server sends a response (commanded from the node inputs) to the client. This node can only receive the requests, and should be connected to a OgnROS2ServiceServerRequest through the out serverHandle parameter in order to send a response."], "metadata": {"uiName": "ROS2 Service Server Response"}, "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2:service", "inputs": {"onReceived": {"type": "execution", "description": "The input execution port when a request is received"}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0, "metadata": {"displayGroup": "parameters"}}, "nodeNamespace": {"type": "string", "description": "Name of ROS2 Node, prepends any topic published/subscribed by the node name", "default": "", "metadata": {"displayGroup": "parameters"}}, "serverHandle": {"type": "uint64", "description": "handle to the server", "default": 0, "metadata": {"displayGroup": "parameters"}}, "messagePackage": {"type": "string", "description": "Package name (e.g.: example_interfaces for example_interfaces/srv/AddTwoInts)", "default": "", "metadata": {"displayGroup": "parameters"}}, "messageSubfolder": {"type": "string", "description": "Subfolder name (e.g.: srv for example_interfaces/srv/AddTwoInts)", "default": "srv", "metadata": {"displayGroup": "parameters"}}, "messageName": {"type": "string", "description": "Service name (e.g.: AddTwoInts for example_interfaces/srv/AddTwoInts)", "default": "", "metadata": {"displayGroup": "parameters"}}}, "outputs": {"execOut": {"type": "execution", "description": "Output execution triggers when a response is sent"}}}}