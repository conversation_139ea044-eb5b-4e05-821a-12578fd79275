[package]
version = "2.0.5"
category = "Simulation"
title = "Gain Tuner"
description = "<PERSON><PERSON> Tuner for Articulation PD Gains"
keywords = ["isaac", "physics", "analyze", "tune"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot_setup.gain_tuner."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.robot_setup.gain_tuner" = {}
