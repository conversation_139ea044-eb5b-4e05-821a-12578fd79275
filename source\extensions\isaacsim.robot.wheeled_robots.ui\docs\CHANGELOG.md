# Changelog

## [2.1.18] - 2025-06-13
### Changed
- Fix menu test timeout

## [2.1.17] - 2025-06-06
### Changed
- increase timeout for UI tests

## [2.1.16] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [2.1.15] - 2025-05-30
### Changed
- Update timeouts to fix test

## [2.1.14] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.1.13] - 2025-05-10
### Changed
- Enable FSD in test settings

## [2.1.12] - 2025-05-03
### Changed
- Update test settings to fix menu test failures

## [2.1.11] - 2025-04-09
### Changed
- Update all test args to be consistent
- Update Isaac Sim NVIDIA robot asset path

## [2.1.10] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.1.9] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.1.8] - 2025-03-11
### Changed
- Switch asset root for tests to internal nucleus

## [2.1.7] - 2025-03-04
### Changed
- Update to kit 107.1 and fix build issues

## [2.1.6] - 2025-02-10
### Added
- Added test for differential robot graph shortcut

## [2.1.5] - 2025-01-27
### Changed
- Updated docs link

## [2.1.4] - 2025-01-26
### Changed
- Update test settings

## [2.1.3] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.1.2] - 2025-01-17
### Changed
- Temporarily changed docs link

## [2.1.1] - 2024-12-05
### Changed
- Updated OmniGraph naming

## [2.1.0] - 2024-11-01
### Changed
- menu location and name

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-10-02
### Changed
- extension renamed to isaacsim.robot.wheeled_robots.ui

## [1.1.2] - 2024-09-13
### Fixed
- changed pxr.OmniGraphSchema import to OmniGraphSchema

## [1.1.1] - 2024-05-22
### Changed
- docs link changed from internal to external

## [1.1.0] - 2024-05-09
### Changed
- only ask for robot parent prim, automatically search for Articulation Root API under the hood

## [1.0.3] - 2024-04-19
### Added
- Reverted the name of "Differential Robots" menu option to "Differential Controller"

## [1.0.2] - 2024-04-14
### Added
- button to documentation for omnigraph shortcut

## [1.0.1] - 2024-03-25
### Changed
- option to add to a existing graph for omnigraph controller shortcuts

## [1.0.0] - 2024-02-28
### Added
- Initial version
