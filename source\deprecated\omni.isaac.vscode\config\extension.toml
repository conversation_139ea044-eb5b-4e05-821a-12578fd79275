[package]
version = "1.0.5"
category = "Utility"
title = "VS Code integration"
description = "VS Code version of Omniverse's script editor"
keywords = ["isaac", "python", "vscode", "editor"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.code_editor.vscode."

[dependencies]
"isaacsim.code_editor.vscode" = {}
"isaacsim.core.deprecation_manager" = {}
