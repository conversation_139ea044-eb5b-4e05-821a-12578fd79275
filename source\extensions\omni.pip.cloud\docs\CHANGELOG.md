# Changelog
## [1.3.6] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.3.5] - 2025-04-03
### Changed
- Version bump to fix pywin32 issues

## [1.3.4] - 2025-04-02
### Changed
- Version bump to fix pywin32 issues

## [1.3.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.3.2] - 2025-03-04
### Changed
- Update to kit 107.1 and fix build issues

## [1.3.1] - 2025-01-30
### Changed
- Updated to latest release 4.5 changes

## [1.3.0] - 2025-01-16
### Changed
- updated boto3[crt]==1.36.1, botocore==1.36.1
- added jmespath==1.0.1, python-dateutil==2.9.0.post0, six==1.17.0

## [1.2.0] - 2025-01-15
### Changed
- Update to Kit 107.x, Python 3.11

## [1.1.7] - 2025-01-14
### Changed
- Update extension description and add extension specific test settings

## [1.1.6] - 2025-01-11
### Changed
- Make this extension kit version specific

## [1.1.5] - 2024-12-01
### Changed
- Make this extension python version specific

## [1.1.4] - 2024-10-28
### Changed
- Remove test imports from runtime

## [1.1.3] - 2024-05-24
### Fixed
- Manually add paths for pywintypes import

## [1.1.2] - 2024-05-15
### Changed
- Replace pypiwin32==223 with pywin32==306

## [1.1.1] - 2024-05-14
### Changed
- Update cryptography to 42.0.7

## [1.1.0] - 2024-04-19
### Changed
- update typing extensions to typing_extensions==4.10.0

## [1.0.2] - 2023-08-02
### Fixed
- unit test failure due to imports

## [1.0.1] - 2023-08-02
### Fixed
- typing-extensions not loading for azure cloud, force reload in extension startup

## [1.0.0] - 2023-08-02
### Added
- Initial version of Cloud Pip Archive
