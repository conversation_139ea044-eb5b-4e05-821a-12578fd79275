<?xml version="1.0" encoding="UTF-8"?>
<robot name="test_floating">

  <link name="root_link"/>

  <joint name="root_to_base" type="fixed">
    <parent link="root_link"/>
    <child link="base_link"/>
  </joint>

  <link name="base_link">
    <visual>
      <geometry>
        <sphere radius="0.2"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <sphere radius="0.2"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>

  <joint name="base_joint" type="continuous">
    <parent link="base_link"/>
    <child link="link_1"/>
    <axis xyz="0 0 1"/>
    <origin xyz="0 0 0.45"/>
  </joint>

  <link name="link_1">
    <visual>
      <geometry>
        <cylinder length="0.8" radius=".1"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.8" radius=".1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <link name="floating_link">
    <visual>
      <geometry>
        <cylinder length="0.8" radius=".1"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.8" radius=".1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/>
    </inertial>
  </link>
  <joint name="floating_joint" type="floating">
    <parent link="link_1"/>
    <child link="floating_link"/>
    <origin xyz="0.0 0.0 1.0"/>
  </joint>
</robot>
