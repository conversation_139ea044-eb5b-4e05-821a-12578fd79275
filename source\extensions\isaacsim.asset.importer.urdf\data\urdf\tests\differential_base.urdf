<?xml version="1.0" encoding="UTF-8"?>
<robot name="differential_base">
   <link name="chassis">
      <visual>
         <origin rpy="0 0 0" xyz="0 0 0" />
         <geometry>
            <box size=".5 .5 .5"/>
         </geometry>
      </visual>
      <collision>
         <origin rpy="0 0 0" xyz= "0 0 0" />
         <geometry>
            <box size=".5 .5 .5"/>
         </geometry>
      </collision>
      <inertial>
         <mass value="10" />
      </inertial>
   </link>
   <link name="left_wheel_link">
      <visual>
         <origin rpy="0 0 0" xyz="0 0 0" />
         <geometry>
            <sphere radius = "0.5"/>
         </geometry>
      </visual>
      <collision>
         <origin rpy="0 0 0" xyz="0 0 0" />
         <geometry>
            <sphere radius = "0.5"/>
         </geometry>
      </collision>
      <inertial>
         <mass value="1" />
      </inertial>
   </link>
   <link name="right_wheel_link">
      <visual>
         <origin rpy="0 0 0" xyz="0 0 0" />
         <geometry>
            <sphere radius = "0.5"/>
         </geometry>
      </visual>
      <collision>
         <origin rpy="0 0 0" xyz="0 0 0" />
         <geometry>
            <sphere radius = "0.5"/>
         </geometry>
      </collision>
      <inertial>
         <mass value="1" />
      </inertial>
   </link>
   <joint name="left_wheel" type="continuous">
      <origin rpy="0 0 0" xyz="0 0.5 0" />
      <axis xyz="0 1 0" />
      <parent link="chassis" />
      <child link="left_wheel_link" />
   </joint>
   <joint name="right_wheel" type="continuous">
      <origin rpy="0 0 0" xyz="0 -0.5 0" />
      <axis xyz="0 1 0" />
      <parent link="chassis" />
      <child link="right_wheel_link" />
   </joint>
</robot>