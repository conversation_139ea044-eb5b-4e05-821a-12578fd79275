[package]
version = "1.0.6"
category = "Simulation"
title = "Camera Inspector"
description = "This extension can inspect and modify the properties of cameras in the scene."
keywords = ["isaac", "camera", "analyze", "inspect", "manager"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.util.camera_inspector."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.util.camera_inspector" = {}

[[python.module]]
name = "omni.isaac.camera_inspector"

[[test]]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
