# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot descriptor defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0


# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# RMPflow will only use these joints to control the robot position.
cspace:
    - joint_1
    - joint_2
    - joint_3
    - joint_4
    - joint_5
    - joint_6


# Global frame of the URDF
root_link: world


# The default cspace position of this robot
default_q: [
    0.0,0.3,1.2,0.0,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# RMPflow uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, RMPflow will
# not be able to avoid obstacles.  

collision_spheres:
  - J1:
    - "center": [0.0, 0.0, 0.1]
      "radius": 0.09
    - "center": [0.0, 0.0, 0.15]
      "radius": 0.09
    - "center": [0.0, 0.0, 0.2]
      "radius": 0.09
  - J2:
    - "center": [0.0, 0.08, 0.0]
      "radius": 0.09
    - "center": [0.0, 0.16, 0.0]
      "radius": 0.09
    - "center": [0.0, 0.2, 0.0]
      "radius": 0.09
    - "center": [0.0, 0.197, 0.05]
      "radius": 0.08
    - "center": [0.0, 0.195, 0.1]
      "radius": 0.08
    - "center": [0.0, 0.192, 0.15]
      "radius": 0.08
    - "center": [0.0, 0.19, 0.2]
      "radius": 0.065
    - "center": [0.0, 0.187, 0.25]
      "radius": 0.065
    - "center": [0.0, 0.185, 0.3]
      "radius": 0.065 
    - "center": [0.0, 0.182, 0.35]
      "radius": 0.065
    - "center": [0.0, 0.18, 0.4]
      "radius": 0.065 
    - "center": [0.0, 0.177, 0.45]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.5]
      "radius": 0.065 
    - "center": [0.0, 0.174, 0.55]
      "radius": 0.065 
    - "center": [0.0, 0.173, 0.6]
      "radius": 0.065
    - "center": [0.0, 0.172, 0.65]
      "radius": 0.075 
    - "center": [0.0, 0.16, 0.7]
      "radius": 0.075 
  - J3:
    - "center": [0.0, 0.025, 0]
      "radius": 0.075
    - "center": [0.0, -0.045, 0]
      "radius": 0.065
    - "center": [0.0, -0.045, 0.05]
      "radius": 0.065
    - "center": [0.0, -0.045, 0.1]
      "radius": 0.065
    - "center": [0.0, -0.045, 0.15]
      "radius": 0.06
    - "center": [0.0, -0.045, 0.2]
      "radius": 0.06
    - "center": [0.0, -0.045, 0.25]
      "radius": 0.06
    - "center": [0.0, -0.045, 0.3]
      "radius": 0.06
    - "center": [0.0, -0.045, 0.35]
      "radius": 0.055
    - "center": [0.0, -0.05, 0.4]
      "radius": 0.055
    - "center": [0.0, -0.05, 0.45]
      "radius": 0.055
    - "center": [0.0, -0.05, 0.5]
      "radius": 0.055
    - "center": [0.0, -0.05, 0.55]
      "radius": 0.055
    - "center": [0.0, -0.05, 0.59]
      "radius": 0.055
  - J5:
    - "center": [0.0, 0.05, 0]
      "radius": 0.055
    - "center": [0.0, 0.1, 0]
      "radius": 0.055
  - J6:
    - "center": [0.0, 0.0, -0.05]
      "radius": 0.05
    - "center": [0.0, 0.0, -0.1]
      "radius": 0.05
    - "center": [0.0, 0.0, -0.15]
      "radius": 0.05
    - "center": [0.0, 0.0, 0.04]
      "radius": 0.035
    - "center": [0.0, 0.0, 0.08]
      "radius": 0.035
    - "center": [0.0, 0.0, 0.12]
      "radius": 0.035
  - right_inner_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.02
    - "center": [0.0, -0.03, 0.025]
      "radius": 0.02
    - "center": [0.0, -0.05, 0.05]
      "radius": 0.02
  - right_inner_finger:
    - "center": [0.0, 0.02, 0.0]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.015]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.03]
      "radius": 0.015
    - "center": [0.0, 0.025, 0.04]
      "radius": 0.01
  - left_inner_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.02
    - "center": [0.0, -0.03, 0.025]
      "radius": 0.02
    - "center": [0.0, -0.05, 0.05]
      "radius": 0.02
  - left_inner_finger:
    - "center": [0.0, 0.02, 0.0]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.015]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.03]
      "radius": 0.015
    - "center": [0.0, 0.025, 0.04]
      "radius": 0.01
