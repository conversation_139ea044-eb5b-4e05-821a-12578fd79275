{"ROS2PublishImage": {"version": 1, "icon": "icons/isaac-sim.svg", "description": ["This node publishes ROS2 Image messages"], "metadata": {"uiName": "ROS2 Publish Image"}, "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2:publisher", "inputs": {"execIn": {"type": "execution", "description": "The input execution port."}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0}, "nodeNamespace": {"type": "string", "description": "Namespace of ROS2 Node, prepends any published/subscribed topic by the node namespace", "default": ""}, "frameId": {"type": "string", "description": "FrameId for ROS2 message", "default": "sim_camera"}, "topicName": {"type": "string", "description": "Name of ROS2 Topic", "default": "rgb"}, "qosProfile": {"type": "string", "description": "QoS profile config", "default": ""}, "queueSize": {"type": "uint64", "description": "The number of messages to queue up before throwing some away, in case messages are collected faster than they can be sent. Only honored if 'history' QoS policy was set to 'keep last'. This setting can be overwritten by qosProfile input.", "default": 10}, "timeStamp": {"type": "double", "description": "Time in seconds to use when publishing the message", "default": 0.0}, "data": {"type": "uchar[]", "description": "Buffer array data", "memoryType": "any", "default": []}, "width": {"type": "uint", "description": "Buffer array width", "default": 0}, "height": {"type": "uint", "description": "Buffer array height", "default": 0}, "encoding": {"type": "token", "description": "ROS encoding format for the input data, taken from the list of strings in include/sensor_msgs/image_encodings.h. Input data is expected to already be in this format, no conversions are performed", "metadata": {"allowedTokens": {"Type_RGB8": "rgb8", "Type_RGBA8": "rgba8", "Type_32FC1": "32FC1", "Type_32SC1": "32SC1"}}, "default": "rgb8"}, "dataPtr": {"type": "uint64", "description": "Pointer to the raw rgba array data", "default": 0}, "cudaDeviceIndex": {"type": "int", "description": "Index of the device where the data lives (-1 for host data)", "default": -1}, "bufferSize": {"type": "uint", "description": "Size (in bytes) of the buffer (0 if the input is a texture)"}, "format": {"type": "uint64", "description": "Format"}}}}