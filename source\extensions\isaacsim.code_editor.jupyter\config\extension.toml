[package]
version = "1.1.0"
category = "Utility"
title = "Jupyter notebook integration"
description = "Jupyter notebook version of Omniverse's script editor"
keywords = ["isaac", "python", "jupyter", "notebook"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"omni.kit.notification_manager" = { optional = true }
"omni.kit.uiapp" = { optional = true }

[[python.module]]
name = "isaacsim.code_editor.jupyter"

[python.pipapi]
requirements = ["jupyterlab", "chardet", "python-language-server", "jedi", "requests", "h11"]
use_online_index = true
ignore_import_check = true

[settings]
# IP address where the extension server will listen for connections.
exts."isaacsim.code_editor.jupyter".host = "127.0.0.1"
# Port number where the extension server will listen for connections.
exts."isaacsim.code_editor.jupyter".port = 8227
# Whether to kill applications/processes that use the same ports before enabling the extension.
# Disable this option if you want to launch multiple applications with this extension enabled.
exts."isaacsim.code_editor.jupyter".kill_processes_with_port_in_use = false

# jupyter notebook settings

# IP address where the Jupyter server is being started.
exts."isaacsim.code_editor.jupyter".notebook_ip = "127.0.0.1"
# Port number where the Jupyter server is being started.
exts."isaacsim.code_editor.jupyter".notebook_port = 8228
# Jupyter server token for token-based authentication. If empty, the server will start without authentication.
exts."isaacsim.code_editor.jupyter".notebook_token = ""
# The directory to use for notebooks. If empty, the directory is ``data/notebooks`` within the extension tree.
exts."isaacsim.code_editor.jupyter".notebook_dir = ""
# Jupyter server command line options other than ``--ip``, ``--port``, ``--token`` and ``--notebook-dir``.
exts."isaacsim.code_editor.jupyter".command_line_options = "--allow-root --no-browser --JupyterApp.answer_yes=True"

[[test]]
enabled = true

[[test]]
name = "startup"
enabled = false
