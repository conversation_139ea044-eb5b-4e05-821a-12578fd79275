# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot descriptor defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - shoulder_pan_joint
    - shoulder_lift_joint
    - elbow_joint
    - wrist_1_joint
    - wrist_2_joint
    - wrist_3_joint
default_q: [
    -0.0,-1.2,1.1,0.0,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - shoulder_link:
    - "center": [0.0, 0.0, 0.01]
      "radius": 0.085
    - "center": [0.003, -0.022, -0.009]
      "radius": 0.082
  - upper_arm_link:
    - "center": [-0.031, 0.0, 0.176]
      "radius": 0.084
    - "center": [-0.589, 0.0, 0.177]
      "radius": 0.068
    - "center": [-0.418, -0.0, 0.176]
      "radius": 0.064
    - "center": [-0.224, -0.0, 0.176]
      "radius": 0.064
    - "center": [-0.309, 0.002, 0.176]
      "radius": 0.063
    - "center": [-0.14, -0.001, 0.177]
      "radius": 0.064
    - "center": [-0.516, 0.0, 0.176]
      "radius": 0.064
    - "center": [0.008, -0.001, 0.184]
      "radius": 0.077
    - "center": [-0.617, -0.002, 0.167]
      "radius": 0.063
    - "center": [-0.068, -0.005, 0.179]
      "radius": 0.079
  - forearm_link:
    - "center": [-0.056, -0.0, 0.04]
      "radius": 0.067
    - "center": [-0.182, -0.0, 0.038]
      "radius": 0.065
    - "center": [-0.317, -0.0, 0.024]
      "radius": 0.062
    - "center": [-0.429, 0.0, 0.029]
      "radius": 0.059
    - "center": [-0.566, 0.0, 0.056]
      "radius": 0.057
    - "center": [-0.256, 0.0, 0.024]
      "radius": 0.064
    - "center": [-0.565, -0.001, 0.029]
      "radius": 0.057
    - "center": [-0.106, 0.0, 0.044]
      "radius": 0.067
    - "center": [-0.378, -0.0, 0.025]
      "radius": 0.061
    - "center": [-0.017, 0.007, 0.053]
      "radius": 0.057
    - "center": [-0.52, -0.001, 0.029]
      "radius": 0.058
    - "center": [-0.475, -0.0, 0.029]
      "radius": 0.059
    - "center": [-0.0, 0.005, 0.119]
      "radius": 0.06
  - wrist_1_link:
    - "center": [0.0, 0.005, -0.007]
      "radius": 0.056
    - "center": [-0.001, -0.02, 0.0]
      "radius": 0.055
  - wrist_2_link:
    - "center": [-0.0, 0.001, -0.0]
      "radius": 0.056
    - "center": [-0.0, 0.021, 0.0]
      "radius": 0.055
    - "center": [-0.004, -0.011, -0.011]
      "radius": 0.053
  - wrist_3_link:
    - "center": [-0.016, 0.002, -0.025]
      "radius": 0.034
    - "center": [0.016, -0.011, -0.024]
      "radius": 0.034
    - "center": [0.009, 0.018, -0.025]
      "radius": 0.034
