[package]
version = "2.0.20"
category = "Simulation"
title = "Isaac Sim Tests"
description = "Collection of tests for isaac sim that are not tied to a specific extension"
keywords = ["isaac", "tests"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.utils" = {}
"isaacsim.robot.wheeled_robots" = {}
"isaacsim.sensors.camera" = {}
"isaacsim.sensors.physics" = {}
"isaacsim.sensors.physx" = {}
"isaacsim.sensors.rtx" = {}
"isaacsim.storage.native" = {}
"omni.kit.asset_converter" = {}
"omni.kit.material.library" = {}
"omni.kit.pip_archive" = {} # pulls in numpy
"omni.kit.primitive.mesh" = {} # needed for mesh creation commands
"omni.kit.uiapp" = {}
"omni.kit.viewport.window" = {} # snippet tests need this
"omni.physx" = {}
"omni.physx.commands" = {}
"omni.syntheticdata" = {}
"omni.timeline" = {} # needed for simulation to happen

[[python.module]]
name = "isaacsim.test.collection" # so we can find the path to the extension

[[python.module]]
name = "isaacsim.test.collection.tests"

[[test]]
timeout = 900
dependencies = [
    "isaacsim.ros2.bridge",
    "omni.kit.window.toolbar",
]

stdoutFailPatterns.exclude = [
    # This is excluded in at least 3 kit tests.
    "*Missing call to destroyResourceBindingSignature()*",
    '*[Error] [isaacsim.sensors.physics.plugin] *** error, No valid parent*',
    '*[Error] [isaacsim.sensors.physics.plugin] Failed to create imu sensor, parent prim is not found or invalid*',
    '*[Error] [isaacsim.sensors.physics.plugin] Imu Sensor does not exist*',
    '*[Error] [isaacsim.sensors.physics.plugin] Contact Sensor does not exist*',
    '*[Error] [carb] [Plugin: omni.sensors.nv.lidar.ext.plugin] Dependency: [omni::sensors::lidar::IGenericModelOutputIOFactory v0.1] failed to be resolved.*', # feature not included in Windows
    '*[Error] [isaacsim.sensors.physx.plugin] Lidar Sensor does not exist*',
    '*[Error] [isaacsim.sensors.physx.plugin] Generic Sensor does not exist*',
    '*[Error] [omni.kit.commands.command] Failed to execute a command: ChangeProperty*',
    '*[Error] [asyncio] Task exception was never retrieved*',

]


args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]

[[test]]
name = "startup"
args = [
    "--/app/settings/fabricDefaultStageFrameHistoryCount = 3",
]
