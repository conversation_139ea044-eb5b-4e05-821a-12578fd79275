{"OgnROS2ServiceClient": {"version": 1, "icon": "icons/isaac-sim.svg", "description": ["This node is a generic service client that provides interface for a ROS service. The request/response fields of the service are parsed and are made accessible via the node based on the service specified from messagePackage, messageSubfolder, messageName. The client sends a request (commanded from the node inputs) and receives a response (accessible from the node outputs) from the server."], "metadata": {"uiName": "ROS2 Service Client"}, "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2:service", "inputs": {"execIn": {"type": "execution", "description": "The input execution port"}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0, "metadata": {"displayGroup": "parameters"}}, "nodeNamespace": {"type": "string", "description": "Name of ROS2 Node, prepends any topic published/subscribed by the node name", "default": "", "metadata": {"displayGroup": "parameters"}}, "serviceName": {"type": "string", "description": "Name of ROS2 Service", "default": "/service_name", "metadata": {"displayGroup": "parameters"}}, "messagePackage": {"type": "string", "description": "Package name (e.g.: example_interfaces for example_interfaces/srv/AddTwoInts)", "default": "", "metadata": {"displayGroup": "parameters"}}, "messageSubfolder": {"type": "string", "description": "Subfolder name (e.g.: srv for example_interfaces/srv/AddTwoInts)", "default": "srv", "metadata": {"displayGroup": "parameters"}}, "messageName": {"type": "string", "description": "Service name (e.g.: AddTwoInts for example_interfaces/srv/AddTwoInts)", "default": "", "metadata": {"displayGroup": "parameters"}}, "qosProfile": {"type": "string", "description": "QoS profile config", "default": "", "metadata": {"displayGroup": "parameters"}}}, "outputs": {"execOut": {"type": "execution", "description": "Output execution triggers when the response is received"}}}}