# SPDX-FileCopyrightText: Copyright (c) 2022-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


import carb
import omni.ext
import omni.timeline


class Extension(omni.ext.IExt):
    def on_startup(self, ext_id):
        # Enable the developer throttling settings when extension starts
        carb.settings.get_settings().set("/app/show_developer_preference_section", True)

        timeline = omni.timeline.get_timeline_interface()
        self.timeline_event_sub = timeline.get_timeline_event_stream().create_subscription_to_pop(
            self.on_stop_play, name="IsaacSimThrottlingEventHandler"
        )
        pass

    def on_stop_play(self, event: carb.events.IEvent):
        # Enable eco mode if playing sim, disable if stopped
        # Disable legacy gizmos during runtime
        _settings = carb.settings.get_settings()
        if event.type == int(omni.timeline.TimelineEventType.PLAY):
            _settings.set("/rtx/ecoMode/enabled", False)
            _settings.set("/exts/omni.kit.hydra_texture/gizmos/enabled", False)
        elif event.type == int(omni.timeline.TimelineEventType.STOP) or event.type == int(
            omni.timeline.TimelineEventType.PAUSE
        ):
            _settings.set("/rtx/ecoMode/enabled", True)
            _settings.set("/exts/omni.kit.hydra_texture/gizmos/enabled", True)
        pass

    def on_shutdown(self):
        self.timeline_event_sub = None
        pass
