[package]
version = "1.0.8"
category = "Internal"
title = "Isaac Sim DocTest"
description = "Test interactive Python docstrings examples"
keywords = ["isaac", "test", "doctest", "docstrings"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"omni.kit.test" = {}

[[python.module]]
name = "isaacsim.test.docstring"
