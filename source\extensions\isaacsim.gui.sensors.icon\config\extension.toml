[package]
version = "2.0.0"
category = "other"
title = "Sensor Icon"
description = "sensor icon in viewport"
keywords = ["sensor","icon","gui"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.utils" = {}
"omni.appwindow" = { public = true }
"omni.kit.viewport.menubar.core" = {}
"omni.kit.viewport.menubar.display" = {}
"omni.kit.viewport.registry" = {}
"omni.kit.viewport.utility" = {}
"omni.kit.widget.stage" = {}
"omni.ui" = {}
"omni.ui.scene" = {}
"omni.usd" = {}
"usdrt.scenegraph" = {}

[[python.module]]
name = "isaacsim.gui.sensors.icon"

# define the Python module for tests
[[python.module]]
name = "isaacsim.gui.sensors.icon.tests"

[settings]
# timeout for resolving sensor icon 
exts."isaacsim.gui.sensors.icon".timeout = 5

# show prim name with the sensor icon, default to false
exts."omni.kit.prim.sensor".showTitle = false

# show sensor icon in viewport, default to false
persistent.exts."isaacsim.gui.sensors.icon".visible_on_startup=false

[[test]]
enabled = true
dependencies = [
    "omni.hydra.rtx",                       # "omni.hydra.pxr", Can we run and pass with Storm ?,
    "omni.hydra.usdrt_delegate",
    "omni.kit.ui_test", 
]
args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]


stdoutFailPatterns.exclude = [
    "*<class 'omni.kit.menu.utils.scripts.utils.MenuUtilsExtension'>: extension object is still alive*", # Leak
    "*<class 'omni.kit.window.tests.Extension'>: extension object is still alive*", # Leak
    "*createDLSSContext error: unable to initialize context. Optional DLSS feature is disabled.*",
]

# disable startup test
[[test]]
name = "startup"
enabled = false
args = [
    "--/app/settings/fabricDefaultStageFrameHistoryCount = 3",
]
