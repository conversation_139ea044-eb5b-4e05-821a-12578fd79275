# Changelog
## [1.0.6] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.0.5] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.0.4] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.0.3] - 2025-03-20
### Changed
- Fix issues with duplicate extension startup from extra imports

## [1.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-10-08
### Deprecated
- Extension deprecated since <PERSON> Sim 4.5.0. Replaced by isaacsim.util.camera_inspector.

## [0.2.2] - 2024-06-17
### Fixed
- Camera Inspector erroring out on creating a new stage

## [0.2.1] - 2023-11-13
### Fixed
- Updated documentation link

## [0.2.0] - 2023-08-22
### Changed
- Resized widgets to fit in screen

### Removed
- gui_utils.py
- All functions moved into extension.py

## [0.1.2] - 2023-08-15
### Added
- Check for valid prim path during each call to update camera stats
- Fixes error when selected camera has its prim path modified

## [0.1.1] - 2023-08-12
### Changed
- Change UI to display WXYZ instead of XYZW for orientation

## [0.1.0] - 2023-07-23
### Added
- Initial version
