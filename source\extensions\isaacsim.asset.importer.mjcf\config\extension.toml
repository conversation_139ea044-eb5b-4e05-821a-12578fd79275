[package]
version = "2.5.0"
category = "Simulation"
title = "Omniverse MJCF Importer"
description = "MJCF Importer"
keywords = ["mjcf", "mujoco", "importer", "isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.robot.schema" = {}
"omni.kit.commands" = {}
"omni.kit.helper.file_utils" = {}
"omni.kit.pip_archive" = {} # pulls in pillow
"omni.kit.tool.asset_importer" = {}
"omni.kit.uiapp" = {}
"omni.kit.viewport.utility" = {}
"omni.kit.window.content_browser" = {}
"omni.kit.window.extensions" = {}
"omni.kit.window.filepicker" = {}
"omni.kit.window.property" = {}
"omni.usd.libs" = {}
"omni.usd.schema.physx" = {}

[[python.module]]
name = "isaacsim.asset.importer.mjcf"

[[python.module]]
name = "isaacsim.asset.importer.mjcf.tests"

[[native.plugin]]
path = "bin/*.plugin"
recursive = false

[[test]]
dependencies = [ "omni.hydra.usdrt_delegate"]
# this is to catch issues where our assimp is out of sync with the one that comes with
# asset importer as this can cause segfaults due to binary incompatibility.

stdoutFailPatterns.exclude = [
    "*Cannot find material with name*",
    "*Neither inertial nor geometries where specified for*",
    "*JointSpec type free not yet supported!*",
    "*is not a valid usd path*",
    "*extension object is still alive, something holds a reference on it*", # exclude warning as failure

]

args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]

[documentation]
pages = [
    "docs/Overview.md",
    "docs/CHANGELOG.md",
]
