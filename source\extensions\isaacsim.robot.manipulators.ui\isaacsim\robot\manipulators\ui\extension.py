# SPDX-FileCopyrightText: Copyright (c) 2018-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


import omni.ext
from omni.kit.menu.utils import MenuHelperExtensionFull

from .menu_graphs import ArticulationPositionWindow, ArticulationVelocityWindow, GripperWindow


class Extension(omni.ext.IExt, MenuHelperExtensionFull):
    def on_startup(self, ext_id: str):

        # Create menu using MenuHelperExtensionFull
        self.menu_startup(
            lambda: ArticulationPositionWindow(),
            "Articulation Position Controller",
            "Joint Position",
            "Tools/Robotics/OmniGraph Controllers",
        )
        self.menu_startup(
            lambda: ArticulationVelocityWindow(),
            "Articulation Velocity Controller",
            "Joint Velocity",
            "Tools/Robotics/OmniGraph Controllers",
        )
        self.menu_startup(
            lambda: GripperWindow(), "Gripper Controller", "Open Loop Gripper", "Tools/Robotics/OmniGraph Controllers"
        )

    def on_shutdown(self):
        self.menu_shutdown()
