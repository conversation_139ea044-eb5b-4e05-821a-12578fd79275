[package]
version = "0.0.7"
category = "other"
title = "Isaac Sim Robot Wizard [Beta]"
description = "Wizard to guide through robot import and configuration"
keywords = ["wizard","import"," "]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.gui.components" = {}
"isaacsim.robot.schema" = {}
"isaacsim.core.utils" = {}

[[python.module]]
name = "isaacsim.robot_setup.wizard"

[settings]
# time out for resolving the robot wizard settings
exts."isaacsim.robot_setup.wizard".timeout = 5

# launch on startup
persistent.exts."isaacsim.robot_setup.wizard".launch_on_startup = false

[[test]]
name = "startup"
enabled = true
