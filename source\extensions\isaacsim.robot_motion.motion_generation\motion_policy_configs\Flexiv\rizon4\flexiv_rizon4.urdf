<?xml version="1.0"?>
<!-- ======================================================================== -->
<!-- | Flexiv Rizon4 Arm (with approximated dynamic parameters)             | -->
<!-- ======================================================================== -->
<robot name="A02L-MP">
  <joint name="joint1" type="revolute">
    <parent link="base_link" />
    <child link="link1" />
    <origin rpy="0 0 3.141592653589793" xyz="0.0 0.0 0.155" />
    <axis xyz="0 0 1" />
    <limit effort="123" lower="-2.7925" upper="2.7925" velocity="2.0944" />
  </joint>
  <joint name="joint2" type="revolute">
    <parent link="link1" />
    <child link="link2" />
    <origin rpy="0 0 0" xyz="0.0 0.03 0.210" />
    <axis xyz="0 1 0" />
    <limit effort="123" lower="-2.2689" upper="2.2689" velocity="2.0944" />
  </joint>
  <joint name="joint3" type="revolute">
    <parent link="link2" />
    <child link="link3" />
    <origin rpy="0 0 0" xyz="0.0 0.035 0.205" />
    <axis xyz="0 0 1" />
    <limit effort="64" lower="-2.9671" upper="2.9671" velocity="2.4435" />
  </joint>
  <joint name="joint4" type="revolute">
    <parent link="link3" />
    <child link="link4" />
    <origin rpy="0 0 -3.141592653589793" xyz="-0.02 -0.03 0.19" />
    <axis xyz="0 1 0" />
    <limit effort="64" lower="-1.8675" upper="2.6878" velocity="2.4435" />
  </joint>
  <joint name="joint5" type="revolute">
    <parent link="link4" />
    <child link="link5" />
    <origin rpy="0 0 -3.141592653589793" xyz="-0.02 0.025 0.195" />
    <axis xyz="0 0 1" />
    <limit effort="39" lower="-2.9671" upper="2.9671" velocity="4.8869" />
  </joint>
  <joint name="joint6" type="revolute">
    <parent link="link5" />
    <child link="link6" />
    <origin rpy="0 0 0" xyz="0.0 0.03 0.19" />
    <axis xyz="0 1 0" />
    <limit effort="39" lower="-1.3963" upper="4.5379" velocity="4.8869" />
  </joint>
  <joint name="joint7" type="revolute">
    <parent link="link6" />
    <child link="link7" />
    <origin rpy="0 -1.5707963267948966 0" xyz="-0.055 0.070 0.11" />
    <axis xyz="0 0 1" />
    <limit effort="39" lower="-2.9671" upper="2.9671" velocity="4.8869" />
  </joint>
  <joint name="link7_to_flange" type="fixed">
    <parent link="link7" />
    <child link="flange" />
    <origin rpy="0 0 -3.141592653589793" xyz="0.0 0.0 0.081" />
  </joint>

  <link name="base_link">
    <inertial>
      <mass value="3.7" />
      <origin rpy="0 0 0" xyz="0.0 -0.0 0.09" />
      <inertia ixx="0.019" ixy="0.0" ixz="0.0" iyy="0.02" iyz="0.0" izz="0.009" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link0.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link0.stl" />
      </geometry>
    </collision>
  </link>
  <link name="link1">
    <inertial>
      <mass value="3.7" />
      <origin rpy="0 0 0" xyz="0.0 0.01 0.15" />
      <inertia ixx="0.028" ixy="0.0" ixz="0.0" iyy="0.027" iyz="0.002" izz="0.008" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link1.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link1.stl" />
      </geometry>
    </collision>
  </link>
  <link name="link2">
    <inertial>
      <mass value="2.7" />
      <origin rpy="0 0 0" xyz="0.0 0.04 0.1" />
      <inertia ixx="0.023" ixy="0.0" ixz="0.0" iyy="0.024" iyz="-0.0" izz="0.004" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link2.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link2.stl" />
      </geometry>
    </collision>
  </link>
  <link name="link3">
    <inertial>
      <mass value="2.4" />
      <origin rpy="0 0 0" xyz="-0.01 -0.0 0.13" />
      <inertia ixx="0.014" ixy="0.0" ixz="-0.001" iyy="0.014" iyz="-0.001" izz="0.004" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link3.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link3.stl" />
      </geometry>
    </collision>
  </link>
  <link name="link4">
    <inertial>
      <mass value="2.4" />
      <origin rpy="0 0 0" xyz="-0.01 0.03 0.1" />
      <inertia ixx="0.018" ixy="0.0" ixz="-0.002" iyy="0.019" iyz="-0.001" izz="0.003" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link4.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link4.stl" />
      </geometry>
    </collision>
  </link>
  <link name="link5">
    <inertial>
      <mass value="2.4" />
      <origin rpy="0 0 0" xyz="0.0 0.0 0.13" />
      <inertia ixx="0.014" ixy="0.0" ixz="0.0" iyy="0.013" iyz="0.001" izz="0.004" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link5.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link5.stl" />
      </geometry>
    </collision>
  </link>
  <link name="link6">
    <inertial>
      <mass value="2.1" />
      <origin rpy="0 0 0" xyz="-0.01 0.06 0.07" />
      <inertia ixx="0.008" ixy="-0.0" ixz="-0.001" iyy="0.009" iyz="0.002" izz="0.004" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link6.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link6.stl" />
      </geometry>
    </collision>
  </link>
  <link name="link7">
    <inertial>
      <mass value="0.8" />
      <origin rpy="0 0 0" xyz="0.0 0.0 0.03" />
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/visual/link7.stl" />
      </geometry>
      <material name="rizon_light_grey" />
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link7.stl" />
      </geometry>
    </collision>
  </link>
  <link name="flange" />
  <material name="rizon_blue">
    <color rgba="0. 0. 1. 0.5" />
  </material>
  <material name="rizon_light_grey">
    <color rgba="0.7 0.7 0.7 1" />
  </material>
  <material name="rizon_dark_grey">
    <color rgba="0.2 0.2 0.2 1" />
  </material>
  <material name="rizon_white">
    <color rgba="1 1 1 1" />
  </material>
</robot>