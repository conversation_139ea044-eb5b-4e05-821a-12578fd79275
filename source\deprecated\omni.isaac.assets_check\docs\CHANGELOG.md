# Changelog
## [0.3.13] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.3.12] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [0.3.11] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [0.3.10] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [0.3.9] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [0.3.8] - 2024-11-12
### Changed
- Moved extension

## [0.3.7] - 2024-11-01
### Changed
- Moved extension

## [0.3.6] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [0.3.5] - 2024-07-25
### Changed
- Added deprecation notice

## [0.3.4] - 2024-02-02
### Changed
- Updated path to the nucleus extension

## [0.3.3] - 2023-11-30
### Changed
- Use get_assets_root_path_async(). Fix for OM-112464.

## [0.3.2] - 2023-01-06
### Fixed
- onclick_fn warning when creating UI

## [0.3.1] - 2022-06-01
### Changed
- Update message to browser login pop-up (OM-52839)

## [0.3.0] - 2022-05-30
### Changed
- move and rename persistent.isaac.asset_root.cloud to core extension

## [0.2.1] - 2022-05-27
### Removed
- Disabled assets check and assets downloader.

## [0.2.0] - 2022-05-25
### Changed
- Renamed copyAssetsURL to cloudAssetsURL.

## [0.1.2] - 2022-04-21
### Removed
- Removed nucleusCheckOverride.

## [0.1.1] - 2022-04-18
### Fixed
- Fixed copyAssetsURL.

## [0.1.0] - 2022-01-24
### Added
- Added first version of assets_check.
