<?xml version="1.0" ?>
<!-- TM12 URDF Example -->
<robot name="tm12">

  <link name="link_0">
    <visual>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/visual/tm12-base.obj"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/collision/tm12-base_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.0"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="0.00110833289" ixy="0.0" ixz="0.0" iyy="0.00110833289" iyz="0.0" izz="0.0018"/>
    </inertial>
  </link>
  <joint name="shoulder_1_joint" type="revolute">
    <parent link="link_0"/>
    <child link="link_1"/>
    <origin rpy="0.000000 -0.000000 0.000000" xyz="0.000000 0.000000 0.165200"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="353" lower="-4.71238898038469" upper="4.71238898038469" velocity="2.0943951023931953"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_1">
    <visual>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/visual/tmr_750w_01.obj"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/collision/tmr_750w_01_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="7.6"/>
      <inertia ixx="0.020289334" ixy="0.000000" ixz="0.000000" iyy="0.020289334" iyz="0.000000" izz="0.021396270999999998"/>
    </inertial>
  </link>
  <joint name="shoulder_2_joint" type="revolute">
    <parent link="link_1"/>
    <child link="link_2"/>
    <origin rpy="-1.570796 -1.570796 0.000000" xyz="0.000000 0.000000 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="353" lower="-3.141592653589793" upper="3.141592653589793" velocity="2.0943951023931953"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_2">
    <visual>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/visual/tm12-arm1.obj"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/collision/tm12-arm1_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="14.0239"/>
      <inertia ixx="0.071505715" ixy="0.000000" ixz="0.000000" iyy="1.1758788999999998" iyz="0.000000" izz="1.2033932999999999"/>
    </inertial>
  </link>
  <joint name="elbow_joint" type="revolute">
    <parent link="link_2"/>
    <child link="link_3"/>
    <origin rpy="0.000000 -0.000000 0.000000" xyz="0.636100 0.000000 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="157" lower="-2.897246558310587" upper="2.897246558310587" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_3">
    <visual>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/visual/tm12-arm2.obj"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/collision/tm12-arm2_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="3.3577"/>
      <inertia ixx="0.009755469" ixy="0.000000" ixz="0.000000" iyy="0.16334719" iyz="0.000000" izz="0.16656678"/>
    </inertial>
  </link>
  <joint name="wrist_1_joint" type="revolute">
    <parent link="link_3"/>
    <child link="link_4"/>
    <origin rpy="0.000000 -0.000000 1.570796" xyz="0.557900 0.000000 -0.156300"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="54" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_4">
    <visual>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/visual/tmr_100w_01.obj"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/collision/tmr_100w_01_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="1.576"/>
      <inertia ixx="0.002058405" ixy="0.000000" ixz="0.000000" iyy="0.0025630790000000002" iyz="0.000000" izz="0.00264321"/>
    </inertial>
  </link>
  <joint name="wrist_2_joint" type="revolute">
    <parent link="link_4"/>
    <child link="link_5"/>
    <origin rpy="1.570796 -0.000000 0.000000" xyz="0.000000 -0.106000 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="54" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_5">
    <visual>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/visual/tmr_100w_02.obj"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/collision/tmr_100w_02_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="1.576"/>
      <inertia ixx="0.002058405" ixy="0.000000" ixz="0.000000" iyy="0.0025630790000000002" iyz="0.000000" izz="0.00264321"/>
    </inertial>
  </link>
  <joint name="wrist_3_joint" type="revolute">
    <parent link="link_5"/>
    <child link="link_6"/>
    <origin rpy="1.570796 -0.000000 0.000000" xyz="0.000000 -0.113150 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="54" lower="-4.71238898038469" upper="4.71238898038469" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_6">
    <visual>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/visual/tmr_ee.obj"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://tm_description/meshes/tm12/collision/tmr_ee_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="0.65"/>
      <inertia ixx="0.000774544" ixy="0.000000" ixz="0.000000" iyy="0.001383811" iyz="0.000000" izz="0.001559496"/>
    </inertial>
  </link>
  <joint name="flange_fixed_joint" type="fixed">
    <parent link="link_6"/>
    <child link="flange_link"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
  </joint>
  <link name="flange_link"/>
  <link name="base"/>
  <joint name="base_fixed_joint" type="fixed">
    <parent link="base"/>
    <child link="link_0"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
  </joint>
  <link name="tool0"/>
  <joint name="flange_link-tool0" type="fixed">
    <parent link="flange_link"/>
    <child link="tool0"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
  </joint>

</robot>
