[package]
version = "4.6.1"
category = "Simulation"
title = "Isaac Sim Core"
description = "The Core extension provides a set of APIs to control the Simulation State as well as the physics scene. It also provides wrappers for USD objects, physics and visual materials."
keywords = ["isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.prims" = {}
"isaacsim.core.simulation_manager" = {}
"isaacsim.core.utils" = {}
"isaacsim.core.version" = {}
"isaacsim.storage.native" = {}
"omni.hydra.usdrt_delegate" = {}
"omni.isaac.core_archive" = {}
"omni.isaac.ml_archive" = {}
"omni.kit.material.library" = {optional = true} # OmniGlass
"omni.kit.stage_templates" = {}
"omni.kit.usd.layers" = {}
"omni.physics.physx" = {}
"omni.physics.stageupdate" = {}
"omni.physx.tensors" = {}
"omni.pip.cloud" = {}
"omni.pip.compute" = {} # scipy
"omni.usd.schema.semantics" = {}
"omni.warp.core" = {}

[[python.module]]
name = "isaacsim.core.api"

[[python.module]]
name = "isaacsim.core.api.tests"

[[native.plugin]]
path = "bin/*.plugin"
recursive = false

[[test]]
timeout = 900
dependencies = [
    "isaacsim.core.cloner",
    "omni.kit.renderer.core",
]

stdoutFailPatterns.exclude = [
    "*Invalid articulation pointer for*", # Per node tests won't have valid bundles
    '*[Error] [carb] [Plugin: omni.sensors.nv.lidar.ext.plugin] Dependency: [omni::sensors::lidar::IGenericModelOutputIOFactory v0.1] failed to be resolved.*', # feature not included in Windows
]

args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]
