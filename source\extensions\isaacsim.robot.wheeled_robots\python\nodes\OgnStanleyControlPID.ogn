{"StanleyControlPID": {"version": 1, "description": "Drive to Target Steering", "language": "Python", "categories": {"isaacWheeledRobots": "robot path planning inside <PERSON>"}, "metadata": {"uiName": "Stanley Control PID"}, "$comment": "", "inputs": {"execIn": {"type": "execution", "description": "The input execution"}, "currentPosition": {"type": "vectord[3]", "description": "Current position of the robot (recommended to use Get Prim Local to World Transform node)"}, "currentOrientation": {"type": "quatd[4]", "description": "Current rotation of the robot as a quaternion (recommended to use Get Prim Local to World Transform node)"}, "currentSpeed": {"type": "vectord[3]", "description": "Current linear velocity of the robot"}, "maxVelocity": {"type": "double", "description": "Maximum linear velocity of the robot", "default": 1.5}, "reachedGoal": {"type": "bool[]", "description": "Position and orientation thresholds at target", "default": [false, false]}, "pathArrays": {"type": "double[]", "description": "The path v, x, y, and yaw arrays"}, "target": {"type": "double[3]", "description": "Target position and orientation", "default": [0, 0, 0]}, "targetChanged": {"type": "bool", "description": "Target position/orientation has changed", "default": false}, "wheelBase": {"type": "double", "description": "Distance between the centers of the front and rear wheels", "default": 0.4132}, "thresholds": {"type": "double[2]", "description": "Position and orientation thresholds at target", "default": [0.1, 0.1]}, "drawPath": {"type": "bool", "description": "Draw the provided path curve onto the stage", "default": false}, "step": {"type": "double", "description": "Step", "default": 0.16666666667}, "gains": {"type": "double[3]", "description": "control, velocity and steering gains", "default": [0.5, 0.1, 0.0872665]}}, "outputs": {"execOut": {"type": "execution", "description": "The output execution"}, "linearVelocity": {"type": "double", "description": "Current forward speed for robot drive"}, "angularVelocity": {"type": "double", "description": "Current angular speed for robot drive"}}}}