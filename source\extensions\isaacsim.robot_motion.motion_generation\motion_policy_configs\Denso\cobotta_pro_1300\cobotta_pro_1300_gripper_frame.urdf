<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from urdf/cobotta_pro_1300.xacro    | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="cobotta_pro_1300">
  <link name="world"/>
  <joint name="joint_w" type="fixed">
    <parent link="world"/>
    <child link="base_link"/>
  </joint>
  <link name="base_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/visual/base_link.dae" scale="1 1 1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/collision/base_link.dae" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
  </link>
  <link name="J1">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/visual/J1.dae" scale="1 1 1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/collision/J1.dae" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
  </link>
  <joint name="joint_1" type="revolute">
    <parent link="base_link"/>
    <child link="J1"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
    <axis xyz="-0.000000 -0.000000 1.000000"/>
    <limit effort="1" lower="-4.71238898038469" upper="4.71238898038469" velocity="1"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <transmission name="trans_1">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="joint_1">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_1">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="J2">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/visual/J2.dae" scale="1 1 1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/collision/J2.dae" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
  </link>
  <joint name="joint_2" type="revolute">
    <parent link="J1"/>
    <child link="J2"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.220000"/>
    <axis xyz="-0.000000 1.000000 -0.000000"/>
    <limit effort="1" lower="-2.61799387799149" upper="2.61799387799149" velocity="1"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <transmission name="trans_2">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="joint_2">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_2">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="J3">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/visual/J3.dae" scale="1 1 1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/collision/J3.dae" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
  </link>
  <joint name="joint_3" type="revolute">
    <parent link="J2"/>
    <child link="J3"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.710000"/>
    <axis xyz="-0.000000 1.000000 -0.000000"/>
    <limit effort="1" lower="-2.61799387799149" upper="2.61799387799149" velocity="1"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <transmission name="trans_3">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="joint_3">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_3">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="J4">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/visual/J4.dae" scale="1 1 1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/collision/J4.dae" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
  </link>
  <joint name="joint_4" type="revolute">
    <parent link="J3"/>
    <child link="J4"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 -0.050000 -0.930000"/>
    <axis xyz="-0.000000 -0.000000 1.000000"/>
    <limit effort="1" lower="-4.71238898038469" upper="4.71238898038469" velocity="1"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <transmission name="trans_4">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="joint_4">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_4">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="J5">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/visual/J5.dae" scale="1 1 1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/collision/J5.dae" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
  </link>
  <joint name="joint_5" type="revolute">
    <parent link="J4"/>
    <child link="J5"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.050000 1.520000"/>
    <axis xyz="-0.000000 1.000000 -0.000000"/>
    <limit effort="1" lower="-2.61799387799149" upper="2.61799387799149" velocity="1"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <transmission name="trans_5">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="joint_5">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_5">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="J6">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/visual/J6.dae" scale="1 1 1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cobotta_pro_1300_visualization/meshes/collision/J6.dae" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
  </link>
  <joint name="joint_6" type="revolute">
    <parent link="J5"/>
    <child link="J6"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.100000 0.160000"/>
    <axis xyz="-0.000000 -0.000000 1.000000"/>
    <limit effort="1" lower="-6.28318530717959" upper="6.28318530717959" velocity="1"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <transmission name="trans_6">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="joint_6">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_6">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="onrobot_rg6_base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.7"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/visual/base_link.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/collision/base_link.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="left_outer_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/visual/outer_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/collision/outer_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="left_inner_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/visual/inner_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/collision/inner_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="left_inner_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/visual/inner_finger.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/collision/inner_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="right_outer_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/visual/outer_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/collision/outer_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="right_inner_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/visual/inner_knuckle.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.8 0.8 0.8 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/collision/inner_knuckle.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="right_inner_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0E-03" ixy="1.0E-06" ixz="1.0E-06" iyy="1.0E-03" iyz="1.0E-06" izz="1.0E-03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/visual/inner_finger.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://onrobot_rg6_visualization/meshes/collision/inner_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="finger_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.024112 0.136813"/>
    <parent link="onrobot_rg6_base_link"/>
    <child link="left_outer_knuckle"/>
    <axis xyz="-1 0 0"/>
    <limit effort="1000" lower="-0.628319" upper="0.628319" velocity="2.0"/>
  </joint>
  <joint name="left_inner_knuckle_joint" type="revolute">
    <origin rpy="0 0 0.0" xyz="0 -0.01272 0.159500"/>
    <parent link="onrobot_rg6_base_link"/>
    <child link="left_inner_knuckle"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.628319" upper="0.628319" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="-1" offset="0"/>
  </joint>
  <joint name="left_inner_finger_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.047334999999999995 0.064495"/>
    <parent link="left_outer_knuckle"/>
    <child link="left_inner_finger"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.872665" upper="0.872665" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="1" offset="0"/>
  </joint>
  <joint name="right_outer_knuckle_joint" type="revolute">
    <origin rpy="0 0 3.141592653589793" xyz="0 0.024112 0.136813"/>
    <parent link="onrobot_rg6_base_link"/>
    <child link="right_outer_knuckle"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.628319" upper="0.628319" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="-1" offset="0"/>
  </joint>
  <joint name="right_inner_knuckle_joint" type="revolute">
    <origin rpy="0 0 -3.141592653589793" xyz="0 0.01272 0.159500"/>
    <parent link="onrobot_rg6_base_link"/>
    <child link="right_inner_knuckle"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.628319" upper="0.628319" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="-1" offset="0"/>
  </joint>
  <joint name="right_inner_finger_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.047334999999999995 0.064495"/>
    <parent link="right_outer_knuckle"/>
    <child link="right_inner_finger"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.872665" upper="0.872665" velocity="2.0"/>
    <mimic joint="finger_joint" multiplier="1" offset="0"/>
  </joint>
  <transmission name="finger_joint_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="finger_joint">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="finger_joint_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="joint_tcp_fixed" type="fixed">
    <parent link="J6"/>
    <child link="onrobot_rg6_base_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>

  <link name="gripper_center"/>
  <joint name="gripper_center_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.0 0.0 .24"/>
    <parent link="onrobot_rg6_base_link"/>
    <child link="gripper_center"/>
  </joint>
</robot>

