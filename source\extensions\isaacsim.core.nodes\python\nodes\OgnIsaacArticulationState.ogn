{"IsaacArticulationState": {"version": 1, "description": "Articulated robot state. The node takes either joint names or joint indices, and outputs the joint positions and velocities, as well as the measured joint efforts, forces, and torques.", "language": "Python", "categories": {"isaacSim": "robot state inside <PERSON>"}, "metadata": {"uiName": "Articulation State"}, "$comment": "The node takes either joint names or joint indices, and outputs the joint positions and velocities, as well as the measured joint efforts, forces, and torques.", "inputs": {"execIn": {"type": "execution", "description": "The input execution"}, "targetPrim": {"type": "target", "description": "The target robot prim with robot articulation root. Ensure robotPath is empty for this to be considered.", "optional": true}, "robotPath": {"type": "string", "description": "Path to the robot articulation root. If this is populated, targetPrim is ignored."}, "jointNames": {"type": "token[]", "description": "Queried joint names, use either Joint Names or Joint Indices, if neither is given, default to all joints", "$comment": "Use either Joint Names or Joint Indices, if neither is given, default to all joints"}, "jointIndices": {"type": "int[]", "description": "Queried joint indices, use either Joint Names or Joint Indices, if neither is given, default to all joints", "$comment": "Use either Joint Names or Joint Indices, if neither is given, default to all joints"}}, "outputs": {"jointPositions": {"type": "double[]", "description": ["Joint positions", "Units:", "    linear (m)", "    angular (rad)"]}, "jointVelocities": {"type": "double[]", "description": ["Joint velocities", "Units:", "    linear (m/s)", "    angular (rad/s)"]}, "measuredJointEfforts": {"type": "double[]", "description": ["Measured joint efforts", "Force Units:  ", "    linear (kg*m/s^2) i.e. a force", "    angular (kg*m^2/s^2) i.e. a torque", "Acceleration Units:", "    linear (m/s^2), i.e. a linear acceleration", "    angular (rad/s^2) i.e. an angular acceleration"]}, "measuredJointForces": {"type": "double[3][]", "description": ["Measured joint reaction forces", "Force Units:  ", "    linear (kg*m/s^2) i.e. a force", "    angular (kg*m^2/s^2) i.e. a torque", "Acceleration Units:", "    linear (m/s^2), i.e. a linear acceleration", "    angular (rad/s^2) i.e. an angular acceleration"]}, "measuredJointTorques": {"type": "double[3][]", "description": ["Measured joint reaction torques", "Force Units:  ", "    linear (kg*m/s^2) i.e. a force", "    angular (kg*m^2/s^2) i.e. a torque", "Acceleration Units:", "    linear (m/s^2), i.e. a linear acceleration", "    angular (rad/s^2) i.e. an angular acceleration"]}, "jointNames": {"type": "token[]", "description": "Joint names"}}}}