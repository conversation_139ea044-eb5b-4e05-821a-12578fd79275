[package]
version = "1.0.7"
category = "Utility"
title = "TF Viewer"
description = "Show the tf transform tree in the viewport"
keywords = ["isaac", "ROS2", "tf"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.ros2.tf_viewer."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.ros2.tf_viewer" = {}

[[python.module]]
name = "omni.isaac.tf_viewer.tests"

[[test]]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
dependencies = [
    "omni.isaac.core",
    "omni.isaac.nucleus",
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
