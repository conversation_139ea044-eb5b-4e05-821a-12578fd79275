[package]
version = "1.1.8"
category = "Simulation"
title = "Benchmark"
description = "This extension provides benchmarking utilities"
keywords = ["isaac", "benchmark", "analyze"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.benchmark.services" = {}
"isaacsim.core.api" = {}
"isaacsim.core.nodes" = {}
"isaacsim.robot.wheeled_robots" = {}
"isaacsim.sensors.camera" = {}
"isaacsim.sensors.physx" = {}
"isaacsim.sensors.rtx" = {}
"omni.graph" = {}
"omni.graph.tools" = {}
"omni.graph.ui" = {}
"omni.hydra.rtx"= {}
"omni.isaac.core_archive" = {} # pulls in nvsmi
"omni.kit.material.library"= {}
"omni.kit.primitive.mesh"= {}
"omni.kit.viewport.utility"= {}
"omni.kit.viewport.window"= {}
"omni.physx" = {}

[[python.module]]
name = "isaacsim.benchmark.examples"

[settings]
# Whether to add a randomly generated string as a prefix to the output filename to distinguish runs.
exts."isaacsim.benchmark.examples".num_app_updates = 1

[[test]]
timeout = 900
# these are not cached
dependencies = [
]
args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]

stdoutFailPatterns.exclude = [
    "*NGX EvaluateFeature failed*", # Ada multi gpu doesn't support this yet
    "*Failed to evaluate DLSS feature*", # Ada multi gpu doesn't support this yet
    '*[Error] [carb] [Plugin: omni.sensors.nv.lidar.ext.plugin] Dependency: [omni::sensors::lidar::IGenericModelOutputIOFactory v0.1] failed to be resolved.*', # feature not included in Windows
]

[[test]]
name = "startup"
args = [
    "--/app/settings/fabricDefaultStageFrameHistoryCount = 3",
]
