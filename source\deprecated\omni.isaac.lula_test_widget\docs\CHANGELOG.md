# Changelog
## [1.0.6] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.0.5] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [1.0.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.0.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-09-27
### Deprecated
- Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot_motion.lula_test_widget

## [0.2.2] - 2024-03-15
### Fixed
- Fixed logic around selecting Articulation on STOP/PLAY given new behavior in Core get_prim_object_type() function.

## [0.2.1] - 2023-11-13
### Fixed
- Updated documentation link

## [0.2.0] - 2023-08-15
### Changed
- Updated Controllers due to changes to motion generation ArticulationTrajectory

## [0.1.0] - 2023-01-06
### Added

- Initial version of Lula Test Widget
