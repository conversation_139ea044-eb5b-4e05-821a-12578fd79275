[package]
description = "Full Isaac Sim Action and Event Data Generation App"
title = "Isaac Sim Action and Event Data Generation"
version = "0.1.4"
keywords = ["experience", "app", "usd", "action and event data generation"]
template_name = "action_and_event_data_generation_full"

[dependencies]
"isaacsim.exp.action_and_event_data_generation.base" = {}

# Additional dependencies
"isaacsim.action_and_event_data_generation.setup" = { version = "0.0.8", exact = true, order = 1000 }

"omni.kit.uiapp" = {}

# Kit Viewport Window
"omni.kit.viewport.actions" = {}
"omni.kit.viewport.bundle" = {}
"omni.kit.viewport.rtx" = {}
"omni.kit.viewport_widgets_manager" = {}
"omni.kit.viewport.window" = {}
"omni.kit.viewport.menubar.settings" = {}
"omni.kit.viewport.menubar.display" = {}
"omni.kit.viewport.menubar.render" = {}
"omni.kit.viewport.menubar.camera" = {}
"omni.kit.viewport.menubar.lighting" = {}

# Viewport Manipulator
"omni.kit.manipulator.prim" = {}
"omni.kit.manipulator.viewport" = {}
"omni.kit.manipulator.camera" = {}
"omni.kit.manipulator.selection" = {}

# Kit Browser
"omni.kit.browser.asset" = {}
"omni.kit.browser.material" = {}

# Kit Properties
"omni.kit.property.bundle" = {}
"omni.kit.property.collection" = {}
"omni.kit.property.layer" = {}

# Kit Window Tools
"omni.kit.preferences.animation" = {} # Allow Animation Preference window
"omni.kit.window.commands" = {}
"omni.kit.window.cursor" = {}
"omni.kit.window.extensions" = {}
"omni.kit.window.file" = {}
"omni.kit.window.filepicker" = {}
"omni.kit.window.material" = {}
"omni.kit.window.material_graph" = {}
"omni.kit.window.preferences" = {}
"omni.kit.window.quicksearch" = {}
"omni.kit.window.script_editor" = {}
"omni.kit.window.stats" = { order = 1000 }
"omni.kit.window.title" = {}
"omni.kit.window.usd_paths" = {}
"omni.kit.window.toolbar" = {}

# Kit Stage Tools
"omni.kit.stage.copypaste" = {}
"omni.kit.stage.mdl_converter" = {}
"omni.kit.stage_column.payload" = {}
"omni.kit.stage_column.variant" = {}
"omni.kit.stage_templates" = {}
"omni.kit.stagerecorder.bundle" = {}
"omni.kit.manipulator.transform" = {}

# Kit Tools
"omni.kit.tool.collect" = {} # Allow collecting USDs
"omni.kit.tool.remove_unused.controller" = {}
"omni.kit.tool.remove_unused.core" = {}
"omni.kit.usd.collect" = {}  # Allow collecting USDs
"omni.kit.usda_edit" = {} # Allow to edit USD as USDA
"omni.kit.tool.asset_exporter" = {}

# Kit Widgets
"omni.kit.widget.cache_indicator" = {}
"omni.kit.widget.collection" = {}
"omni.kit.widget.extended_searchfield" = {}
"omni.kit.widget.filebrowser" = {}
"omni.kit.widget.layers" = {}
"omni.kit.widget.live" = {}
"omni.kit.widget.timeline" = {}
"omni.kit.widget.versioning" = {}
"omni.kit.widgets.custom" = {}

# Replicator Extensions
#############################################
"isaacsim.replicator.synthetic_recorder" = {}
"semantics.schema.editor" = {}

# Action and Event Data Generation Extensions
#############################################
"isaacsim.replicator.agent.ui" = { version = "0.7.6", exact = true }
"isaacsim.replicator.incident" = { version = "0.1.14", exact = true }

# Animation Extensions
#############################################
"omni.anim.navigation.bundle" = {}
"omni.anim.graph.bundle" = {}
"omni.anim.retarget.bundle" = {}
"omni.anim.curve_editor" = {}
"omni.anim.curve.bundle" = {} # For "Set Key" option in right-click menu
"omni.anim.window.timeline" = {}

# Other useful Extensions
#############################################
"omni.kit.quicklayout" = {}
"isaacsim.util.debug_draw" = {}
"omni.graph.window.action" = {}
"omni.graph.visualization.nodes" = {}
"omni.hydra.engine.stats" = {}
"omni.physx.graph" = {}
"omni.scene.optimizer.bundle" = { version = "107.3.9", exact = true }
"isaacsim.asset.gen.conveyor" = {}


[settings.app]
name = "Action and Event Data Generation"
version = "0.1.4"
file.ignoreUnsavedOnExit = false
window.title = "Action and Event Data Generation"
gatherRenderResults = true # True to prevent artifacts in multiple viewport configurations, can be set to false for better performance in some cases

# Register extension folder from this repo in kit
# Your application is using Extensions from your applications
# Here they are setup into a single folder "exts" but you can have you own structure if you need
# see we also dd the Extension from the Base application using their extscache
[settings.app.exts.folders]
'++' = [
    "${app}",
	"${app}/../apps",
    "${app}/../exts",
    "${app}/../extscache",
]

[settings.app.extensions]
# Don't create extension folders
mkdirExtFolders = false

# Download extensions into extscache folder
registryCacheFull = "${app}/../extscache"
# registryEnabled = false

[settings.exts."omni.kit.window.content_browser"]
enable_checkpoints = true

