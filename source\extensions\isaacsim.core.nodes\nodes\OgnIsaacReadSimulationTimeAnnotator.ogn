{"IsaacReadSimulationTimeAnnotator": {"version": 1, "icon": "icons/isaac-sim.svg", "description": "Holds values related to simulation timestamps", "uiName": "<PERSON> Read Simulation Time Annotator", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"execIn": {"type": "execution", "description": "The input execution port"}, "resetOnStop": {"type": "bool", "description": "If True the simulation time will reset when stop is pressed, False means time increases monotonically", "uiName": "Reset On Stop", "default": false}, "referenceTimeNumerator": {"type": "int64", "description": "Reference time represented as a rational number : numerator"}, "referenceTimeDenominator": {"type": "uint64", "description": "Reference time represented as a rational number : denominator"}}, "outputs": {"execOut": {"type": "execution", "description": "The output execution port"}, "simulationTime": {"type": "double", "description": "Current Simulation Time in Seconds", "uiName": "Simulation Time"}}}}