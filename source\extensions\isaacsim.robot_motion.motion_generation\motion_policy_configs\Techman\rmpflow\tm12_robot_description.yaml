# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - shoulder_1_joint
    - shoulder_2_joint
    - elbow_joint
    - wrist_1_joint
    - wrist_2_joint
    - wrist_3_joint
default_q: [
    0.0,0.5,0.9,0.0,1.6,1.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - link_2:
    - "center": [0.182, -0.0, -0.181]
      "radius": 0.06
    - "center": [0.515, -0.0, -0.18]
      "radius": 0.06
    - "center": [0.23, -0.0, -0.181]
      "radius": 0.06
    - "center": [0.278, -0.0, -0.181]
      "radius": 0.06
    - "center": [0.325, -0.0, -0.181]
      "radius": 0.06
    - "center": [0.373, -0.0, -0.181]
      "radius": 0.06
    - "center": [0.42, -0.0, -0.181]
      "radius": 0.06
    - "center": [0.468, -0.0, -0.18]
      "radius": 0.06
    - "center": [-0.0, -0.0, -0.229]
      "radius": 0.09
    - "center": [-0.0, -0.0, -0.136]
      "radius": 0.09
    - "center": [0.098, -0.0, -0.187]
      "radius": 0.08
    - "center": [0.583, -0.0, -0.182]
      "radius": 0.07
    - "center": [0.637, 0.004, -0.221]
      "radius": 0.065
    - "center": [0.637, -0.0, -0.131]
      "radius": 0.065
    - "center": [0.637, 0.002, -0.176]
      "radius": 0.065
  - link_3:
    - "center": [0.044, 0.0, -0.051]
      "radius": 0.05
    - "center": [0.516, 0.0, -0.055]
      "radius": 0.05
    - "center": [0.087, 0.0, -0.052]
      "radius": 0.05
    - "center": [0.13, 0.0, -0.052]
      "radius": 0.05
    - "center": [0.173, 0.0, -0.052]
      "radius": 0.05
    - "center": [0.216, 0.0, -0.053]
      "radius": 0.05
    - "center": [0.259, 0.0, -0.053]
      "radius": 0.05
    - "center": [0.302, 0.0, -0.054]
      "radius": 0.05
    - "center": [0.345, 0.0, -0.054]
      "radius": 0.05
    - "center": [0.406, 0.0, -0.054]
      "radius": 0.05
    - "center": [0.446, 0.0, -0.055]
      "radius": 0.05
    - "center": [0.483, 0.0, -0.055]
      "radius": 0.05
    - "center": [0.376, 0.0, -0.054]
      "radius": 0.05
    - "center": [0.004, 0.0, -0.071]
      "radius": 0.06
    - "center": [0.0, -0.005, -0.093]
      "radius": 0.06
    - "center": [0.558, 0.0, -0.082]
      "radius": 0.05
    - "center": [0.557, 0.0, -0.022]
      "radius": 0.05
    - "center": [0.558, 0.0, -0.052]
      "radius": 0.05
  - link_4:
    - "center": [-0.001, 0.018, 0.001]
      "radius": 0.057
    - "center": [0.001, -0.032, 0.006]
      "radius": 0.056
  - link_5:
    - "center": [0.0, 0.025, 0.0]
      "radius": 0.05
    - "center": [0.0, -0.031, 0.0]
      "radius": 0.05
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.05
  - link_6:
    - "center": [0.0, 0.0, -0.02]
      "radius": 0.053
    - "center": [0.0, 0.071, -0.03]
      "radius": 0.042
    - "center": [0.0, 0.072, 0.026]
      "radius": 0.042
    - "center": [0.0, 0.072, -0.002]
      "radius": 0.042
  - link_1:
    - "center": [0.0, -0.033, 0.0]
      "radius": 0.09
    - "center": [0.0, 0.0, 0.042]
      "radius": 0.09
    - "center": [-0.0, -0.006, -0.055]
      "radius": 0.09
