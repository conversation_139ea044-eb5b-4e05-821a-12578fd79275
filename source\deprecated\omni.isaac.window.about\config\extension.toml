[package]
version = "2.0.7"
category = "Internal"
title = "Isaac Sim About Window"
description="Show application/build information."
keywords = ["isaac", "about"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.app.about."

[dependencies]
"isaacsim.app.about" = {}
"isaacsim.core.deprecation_manager" = {}

[[python.module]]
name = "omni.isaac.window.about"

[[python.module]]
name = "omni.isaac.window.about.tests"

[[test]]
stdoutFailPatterns.exclude = [
    "*[Error] [carb.glinterop.plugin] GLInteropContext::init: carb::windowing is not available*",
]
