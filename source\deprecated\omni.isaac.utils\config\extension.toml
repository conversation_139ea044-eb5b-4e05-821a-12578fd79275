[package]
version = "2.0.6"
category = "Simulation"
title = "Isaac Sim Utilities"
description = "Isaac Sim Utility Extensions"
keywords = ["isaac", "utils"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.core.utils."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.utils" = {}

[[python.module]]
name = "omni.isaac.utils"

[[python.module]]
name = "omni.isaac.utils.tests"

[[test]]
dependencies = [
   "omni.isaac.nucleus"
]
