<?xml version='1.0' encoding='utf-8'?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="panda">
  <xacro:macro name="panda_arm" params="arm_id:='panda' description_pkg:='franka_description' connected_to:='' xyz:='0 0 0' rpy:='0 0 0'">
    <xacro:unless value="${not connected_to}">
      <joint name="${arm_id}_joint_${connected_to}" type="fixed">
        <parent link="${connected_to}"/>
        <child link="${arm_id}_link0"/>
        <origin rpy="${rpy}" xyz="${xyz}"/>
      </joint>
    </xacro:unless>
    <link name="${arm_id}_link0">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link0.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link0.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0"/>
        <mass value="2.8142028896"/>
        <inertia ixx="0.0129886979" ixy="0.0" ixz="0.0" iyy="0.0165355284" iyz="0" izz="0.0203311636"/>
      </inertial>
    </link>
    <link name="${arm_id}_link1">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link1.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link1.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="2.3599995791"/>
        <inertia ixx="0.0186863903" ixy="0.0" ixz="0.0" iyy="0.0143789874" iyz="0" izz="0.00906812"/>
      </inertial>

    </link>
    <joint name="${arm_id}_joint1" type="revolute">
      <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
      <origin rpy="0 0 0" xyz="0 0 0.333"/>
      <parent link="${arm_id}_link0"/>
      <child link="${arm_id}_link1"/>
      <axis xyz="0 0 1"/>
      <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
    </joint>
    <link name="${arm_id}_link2">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link2.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link2.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="2.379518833" />
        <inertia ixx="0.0190388734" ixy="0.0" ixz="0.0" iyy="0.0091429124" iyz="0" izz="0.014697537"/>
      </inertial>

    </link>
    <joint name="${arm_id}_joint2" type="revolute">
      <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-1.7628" soft_upper_limit="1.7628"/>
      <origin rpy="${-pi/2} 0 0" xyz="0 0 0"/>
      <parent link="${arm_id}_link1"/>
      <child link="${arm_id}_link2"/>
      <axis xyz="0 0 1"/>
      <limit effort="87" lower="-1.7628" upper="1.7628" velocity="2.1750"/>
    </joint>
    <link name="${arm_id}_link3">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link3.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link3.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="2.6498823337"/>
        <inertia ixx="0.0129300178" ixy="0.0" ixz="0.0" iyy="0.0150242121" iyz="0" izz="0.0142734598"/>
      </inertial>

    </link>
    <joint name="${arm_id}_joint3" type="revolute">
      <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
      <origin rpy="${pi/2} 0 0" xyz="0 -0.316 0"/>
      <parent link="${arm_id}_link2"/>
      <child link="${arm_id}_link3"/>
      <axis xyz="0 0 1"/>
      <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
    </joint>
    <link name="${arm_id}_link4">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link4.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link4.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="2.6948018744"/>
        <inertia ixx="0.0133874611" ixy="0.0" ixz="0.0" iyy="0.014514325" iyz="0" izz="0.0155175551"/>
      </inertial>
    </link>
    <joint name="${arm_id}_joint4" type="revolute">
      <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.0718" soft_upper_limit="-0.0698"/>
      <origin rpy="${pi/2} 0 0" xyz="0.0825 0 0"/>
      <parent link="${arm_id}_link3"/>
      <child link="${arm_id}_link4"/>
      <axis xyz="0 0 1"/>
      <limit effort="87" lower="-3.0718" upper="-0.0698" velocity="2.1750"/>
    </joint>
    <link name="${arm_id}_link5">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link5.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link5.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="2.9812816864"/>
        <inertia ixx="0.0325565705" ixy="0.0" ixz="0.0" iyy="0.0270660472" iyz="0" izz="0.0115023375"/>
      </inertial>
    </link>
    <joint name="${arm_id}_joint5" type="revolute">
      <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
      <origin rpy="${-pi/2} 0 0" xyz="-0.0825 0.384 0"/>
      <parent link="${arm_id}_link4"/>
      <child link="${arm_id}_link5"/>
      <axis xyz="0 0 1"/>
      <limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>
    </joint>
    <link name="${arm_id}_link6">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link6.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link6.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="1.1285806309"/>
        <inertia ixx="0.0026052565" ixy="0.0" ixz="0.0" iyy="0.0039897229" iyz="0" izz="0.0047048591"/>
      </inertial>
    </link>
    <joint name="${arm_id}_joint6" type="revolute">
      <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-0.0175" soft_upper_limit="3.7525"/>
      <origin rpy="${pi/2} 0 0" xyz="0 0 0"/>
      <parent link="${arm_id}_link5"/>
      <child link="${arm_id}_link6"/>
      <axis xyz="0 0 1"/>
      <limit effort="12" lower="-0.0175" upper="3.7525" velocity="2.6100"/>
    </joint>
    <link name="${arm_id}_link7">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/link7.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/collision/link7.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="0.4052912465"/>
        <inertia ixx="0.0006316592" ixy="0.0" ixz="0.0" iyy="0.0006319639" iyz="0" izz="0.0010607721"/>
      </inertial>
    </link>
    <joint name="${arm_id}_joint7" type="revolute">
      <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
      <origin rpy="${pi/2} 0 0" xyz="0.088 0 0"/>
      <parent link="${arm_id}_link6"/>
      <child link="${arm_id}_link7"/>
      <axis xyz="0 0 1"/>
      <limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>
    </joint>
    <link name="${arm_id}_link8"/>
    <joint name="${arm_id}_joint8" type="fixed">
      <origin rpy="0 0 0" xyz="0 0 0.107"/>
      <parent link="${arm_id}_link7"/>
      <child link="${arm_id}_link8"/>
      <axis xyz="0 0 0"/>
    </joint>
  </xacro:macro>
</robot>
