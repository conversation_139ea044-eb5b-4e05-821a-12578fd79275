{"_comment2": "parameters obtained from https://bucket-download.slamtec.com/0a0d6c01df6ac91016de7e1f558e262474a4be43/SLAMTEC_rplidar_datasheet_S2E_v1.3_en.pdf", "class": "sensor", "type": "lidar", "name": "RPLIDAR S2E", "_comment0": "SLAMTEC RPLIDAR S2E configured for standard at 10Hz", "driveWorksId": "GENERIC", "profile": {"scanType": "rotary", "intensityProcessing": "normalization", "rotationDirection": "CW", "rayType": "IDEALIZED", "nearRangeM": 0.05, "minDistBetweenEchos": 0.05, "farRangeM": 30.0, "rangeResolutionM": 0.013, "rangeAccuracyM": 0.03, "avgPowerW": 0.004, "minReflectance": 0.1, "minReflectanceRange": 10.0, "wavelengthNm": 905.0, "pulseTimeNs": 5, "azimuthErrorMean": 0.0, "azimuthErrorStd": 0.0, "elevationErrorMean": 0.0, "elevationErrorStd": 0.0, "maxReturns": 1, "scanRateBaseHz": 10.0, "reportRateBaseHz": 32000, "numberOfEmitters": 1, "_comment1": "sampling rate is 32k, and at 10Hz and 0.12 resolution", "emitterStateCount": 1, "emitterStates": [{"azimuthDeg": [0], "elevationDeg": [0], "fireTimeNs": [0]}], "intensityMappingType": "LINEAR"}}