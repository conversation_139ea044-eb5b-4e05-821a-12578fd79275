[package]
description = "Base Isaac Sim Action and Event Data Generation App"
title = "Isaac Sim Action and Event Data Generation Base"
version = "0.1.4"
keywords = ["experience", "app", "usd"]
template_name = "spatial_sdg_base"

[dependencies]
# All the core app components and settings will inherit from the Isaac Sim Base Kit
"isaacsim.exp.base" = {}

# Create Kit UI Based applications
"omni.kit.renderer.core" = {}
"omni.kit.browser.sample" = {}	# needed by omni.warp

# Action and Event Data Generation extension additional dependencies
"omni.flowusd" = {}
"omni.replicator.core" = {}
"omni.anim.timeline" = {} # needed by sdg scheduler
"omni.kit.scripting" = {}

# Action and Event Data Generation Headless Extensions
#############################################
"omni.metropolis.utils" = { version = "0.1.13", exact = true }
"omni.anim.people" = { version = "0.7.5", exact = true }
"isaacsim.anim.robot" = { version = "0.0.8", exact = true }
"isaacsim.sensors.rtx.placement" = { version = "0.6.7", exact = true }
"isaacsim.replicator.agent.core" = { version = "0.7.12", exact = true }
"isaacsim.replicator.caption.core" = { version = "0.0.28", exact = true }
"isaacsim.replicator.object" = { version = "0.4.7", exact = true }

[settings]
# to pass tests on Gitlab CI - shader compilation progress bar
exts."omni.kit.viewport.ready".activity_progress.enabled = false
app.player.useFixedTimeStepping = true

# Register extension folder from this repo in kit
# Your application is using Extensions from your applications
# Here they are setup into a single folder "exts" but you can have you own structure if you need
# see we also dd the Extension from the Base application using their extscache
[app.exts]
folders = ["${app}/../exts", "${app}/../extscache", "${app}/../apps"]

[app.extensions]
# Don't create extension folders
mkdirExtFolders = false

# Download extensions into extscache folder
registryCacheFull = "${app}/../extscache"
# registryEnabled = false

[settings.app.exts.folders]
'++' = [
    "${app}",
    "${app}/../exts",
    "${app}/../extscache",
]

