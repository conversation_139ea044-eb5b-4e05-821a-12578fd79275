[package]
version = "4.0.8"
category = "Simulation"
title = "<PERSON>"
description = "Extension that provides lula python interface"
keywords = ["isaac", "lula", "rmp"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true
writeTarget.platform = true # pip prebundle makes this extension os specific
writeTarget.python = true

[dependencies]
"omni.kit.pip_archive" = {}

[[python.module]]
path = "pip_prebundle"

[[python.module]]
name = "isaacsim.robot_motion.lula"

[[python.module]]
name = "isaacsim.robot_motion.lula.tests"

[[test]]
dependencies = [
    "omni.kit.test"
]
