{"Random3f": {"version": 1, "description": "This node outputs the poses of assets with semantic labels", "language": "Python", "metadata": {"uiName": "Random 3f"}, "inputs": {"minimum": {"type": "float[3]", "description": "minimum range for randomization"}, "maximum": {"type": "float[3]", "description": "minimum range for randomization"}}, "outputs": {"output": {"type": "float[3]", "description": "Random float 3"}}}}