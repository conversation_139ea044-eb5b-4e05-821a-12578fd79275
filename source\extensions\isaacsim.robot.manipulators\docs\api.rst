API
===

Python API
----------

.. Summary

.. currentmodule:: isaacsim.robot.manipulators

.. rubric:: manipulators
.. autosummary::
    :nosignatures:

    ~manipulators.SingleManipulator

.. rubric:: controllers
.. autosummary::
    :nosignatures:

    ~controllers.PickPlaceController
    ~controllers.StackingController

.. rubric:: grippers
.. autosummary::
    :nosignatures:

    ~grippers.Gripper
    ~grippers.ParallelGripper
    ~grippers.SurfaceGripper

|

.. API

Manipulators
^^^^^^^^^^^^

.. autoclass:: isaacsim.robot.manipulators.manipulators.SingleManipulator
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

|

Controllers
^^^^^^^^^^^

.. autoclass:: isaacsim.robot.manipulators.controllers.PickPlaceController
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.robot.manipulators.controllers.StackingController
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

|

Grippers
^^^^^^^^

.. autoclass:: isaacsim.robot.manipulators.grippers.Gripper
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.robot.manipulators.grippers.ParallelGripper
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.robot.manipulators.grippers.SurfaceGripper
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
