# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

root_prim: robots
generated:
  - name: transporter_a
    path: transporter_a
    position:
      base: [0.55, 1.05, 0.09]
      noise:
        type: uniform
        params:
          low: [-0.55, -0.35, 0]
          high: [0.55, 0.35, 0]
    orientation:
      base: [0, 0, 0]
    semantic: "robot"
    usd_config:
      root: /<PERSON>/Robots/Idealworks/
      search_depth: 0
      filter: "iw_hub.usd"
    spawn_proba: 0.5
  - name: transporter_b
    path: transporter_b
    position:
      base: [0.55, -1.05, 0.09]
      noise:
        type: uniform
        params:
          low: [-0.55, -0.35, 0]
          high: [0.55, 0.35, 0]
    orientation:
      base: [0, 0, 180]
    semantic: "robot"
    usd_config:
      root: /Isaac/Robots/Idealworks/
      search_depth: 0
      filter: "iw_hub.usd"
    spawn_proba: 0.5
