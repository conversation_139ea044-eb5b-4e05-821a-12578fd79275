[package]
# Semantic Versioning is used: https://semver.org/
version = "1.3.2"
category = "Internal"
title = "Isaac Loop Runner"
description = "Custom Loop Runner for Isaac Sim"
keywords = ["isaac", "loop"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[[python.module]]
name = "omni.kit.loop"

[[python.module]]
name = "omni.kit.loop.tests"

[[native.plugin]]
path = "bin/*.plugin"
recursive = false

[settings]
# Don't sync threads to the present thread
app.runLoopsGlobal.syncToPresent = false
# Set to true to enable rate limiting for the main run loop
app.runLoops.main.rateLimitEnabled = true
# Rate limit frequency in Hz for the main run loop
app.runLoops.main.rateLimitFrequency = 120
# Set to true to use a busy loop for the main run loop
app.runLoops.main.rateLimitUseBusyLoop = false
# Set to true to enable rate limiting for the present run loop
app.runLoops.present.rateLimitEnabled = true
# Rate limit frequency in Hz for the present run loop
app.runLoops.present.rateLimitFrequency = 60
# Set to true to use a busy loop for the present run loop
app.runLoops.present.rateLimitUseBusyLoop = false
# Set to true to enable rate limiting for the rendering run loop
app.runLoops.rendering_0.rateLimitEnabled = true
# Rate limit frequency in Hz for the rendering run loop
app.runLoops.rendering_0.rateLimitFrequency = 120
# Set to true to use a busy loop for the rendering run loop
app.runLoops.rendering_0.rateLimitUseBusyLoop = false

[[test]]
dependencies = [
    "omni.hydra.usdrt_delegate",
    "omni.usd",
]
args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
    ### Extension specific args
    "--/rtx/ecoMode/enabled=True"
]
