[package]
version = "1.3.15"
category = "Rendering"
title = "Isaac Sim Asset Browser"
description = "The Isaac Sim Asset Browser extension provides an user interface for loading isaac sim assets and files."
keywords = ["isaac", "browser", "asset"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.svg"
feature = true
# writeTarget.kit = true

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.utils" = {}
"omni.kit.browser.folder.core" = {}
"omni.kit.clipboard" = { optional=true }
"omni.kit.commands" = {}
"omni.kit.menu.stage" = { optional=true }
"omni.kit.menu.utils" = {}
"omni.kit.tool.collect" = { optional=true }
"omni.kit.viewport.utility" = {}
"omni.pip.cloud" = {}
"omni.usd" = {}

[[python.module]]
name = "isaacsim.asset.browser"

[settings]
# Root folder URLs to list and monitor.
exts."isaacsim.asset.browser".folders = [
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/Robots",
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/Environments",
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/IsaacLab",
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/Materials",
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People",
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/Props",
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/Samples",
    "https://omniverse-content-staging.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/Sensors",
]
# List of asset categories that can be dragged from the browser to the viewport window.
exts."isaacsim.asset.browser".instanceable = []
# Number of seconds to wait for the folder to be listed/read from.
exts."isaacsim.asset.browser".data.timeout = 10
# Whether the browser window is visible when the extension starts up.
exts."isaacsim.asset.browser".visible_after_startup = false
# Filter for USD Types only
exts."isaacsim.asset.browser".data.filter_file_suffixes = [
    ".usd",
    ".usda",
    ".usdc",
    ".usdz",
    ]
# Use only assets with thumbnail
exts."isaacsim.asset.browser".data.hide_file_without_thumbnails = true

[[trigger]]
menu.name = "Window/Browsers/Isaac"
menu.window = "Isaac Sim Assets"

[[test]]
dependencies = [
    "omni.hydra.usdrt_delegate",
    "omni.kit.browser.asset",
    "omni.kit.menu.stage",
    "omni.kit.tool.collect",
    "omni.kit.ui_test",
]

args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]

stdoutFailPatterns.exclude = [
    "*unload wait time exceeded limit of*",
]

[documentation]
pages = [
    "docs/Overview.md",
    "docs/CHANGELOG.md",
]
