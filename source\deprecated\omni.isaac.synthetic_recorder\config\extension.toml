[package]
version = "2.0.6"
category = "Simulation"
title = "Isaac Sim Synthetic Data Recorder"
description = "Tool to record synthetic data"
keywords = ["isaac", "synthetic", "recorder"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.replicator.synthetic_recorder."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.replicator.synthetic_recorder" = {}

[[python.module]]
name = "omni.isaac.synthetic_recorder"

[[test]]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
