# Changelog
## [1.1.8] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.1.7] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [1.1.6] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.1.5] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.1.4] - 2025-03-24
### Changed
- Migrate to Events 2.0

## [1.1.3] - 2025-01-27
### Changed
- Updated docs link

## [1.1.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.1.1] - 2025-01-17
### Changed
- Temporarily changed docs links

## [1.1.0] - 2024-10-29
### Changed
- moved menu entry from "Isaac Examples" to "Window->Examples"

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2023-10-14
### Changed
- Extension renamed to isaacsim.examples.ui.

## [0.1.4] - 2023-10-13
### Fixed
- Updated documentation link

## [0.1.3] - 2023-01-06
### Fixed
- onclick_fn warning when creating UI

## [0.1.2] - 2022-11-22
### Fixed
-Limit issues with int field

## [0.1.1] - 2022-01-18
### Added
- Fixes layout issues

## [0.1.0] - 2021-07-10
### Added
- Initial version of Isaac Sim UI Example
