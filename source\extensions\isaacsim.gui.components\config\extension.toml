[package]
version = "1.1.6"
category = "Simulation"
title = "Isaac Sim UI Utilities"
description = "Core UI Elements for Isaac Sim"
keywords = ["isaac", "ui",]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"omni.graph" = {}
"omni.graph.action" = {}
"omni.graph.visualization.nodes" = {}
"omni.kit.menu.utils" = {}
"omni.kit.pip_archive" = {} # pulls in pyperclip
"omni.kit.property.usd" = {}
"omni.kit.ui_test" = {}
"omni.kit.uiapp" = {}
"omni.kit.window.extensions" = {}
"omni.kit.window.filepicker" = {}
"omni.kit.window.property" = {}
"omni.physx" = {}

[[python.module]]
name = "isaacsim.gui.components"

[[python.module]]
name = "isaacsim.gui.components.tests"

[[test]]

dependencies = [
    "isaacsim.core.api",
    "isaacsim.core.utils",
    "isaacsim.storage.native",
    "omni.hydra.rtx",                       # "omni.hydra.pxr", Can we run and pass with Storm ?,
    "omni.kit.viewport.window",
]
args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]
