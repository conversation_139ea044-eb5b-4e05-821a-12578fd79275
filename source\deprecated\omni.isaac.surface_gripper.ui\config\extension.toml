[package]
version = "2.0.5"
category = "Simulation"
title = "Isaac Sim Surface Gripper UI Components"
description = "UI components for the Surface Gripper extension"
keywords = ["isaac", "physics", "end-effector","ui"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot.surface_gripper.ui."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.robot.surface_gripper.ui" = {}

[[python.module]]
name = "omni.isaac.surface_gripper.ui"

[[test]]
stdoutFailPatterns.exclude = [
    "*[Error] [carb.glinterop.plugin] GLInteropContext::init: carb::windowing is not available*",
]
