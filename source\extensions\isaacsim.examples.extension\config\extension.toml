[package]
version = "2.0.12"
category = "Simulation"
title = "Generate Extension"
description = "UI tool for generating extension templates for different common workflows"
keywords = []
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.core.deprecation_manager" = {}
"isaacsim.gui.components" = {}
"isaacsim.storage.native" = {}
"omni.kit.uiapp" = {}

[[python.module]]
name = "isaacsim.examples.extension"
