[package]
version = "4.6.6"
category = "Simulation"
title = "Isaac Sim Samples"
description = "Sample extensions for Isaac Sim"
keywords = ["isaac", "samples", "manipulation"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.core.prims" = {}
"isaacsim.cortex.behaviors" = {}
"isaacsim.cortex.framework" = {}
"isaacsim.examples.browser" = {}
"isaacsim.examples.extension" = {}
"isaacsim.gui.components" = {}
"isaacsim.robot.manipulators" = {}
"isaacsim.robot.manipulators.examples" = {}
"isaacsim.robot.policy.examples" = {}
"isaacsim.robot.wheeled_robots" = {}
"isaacsim.robot_motion.motion_generation" = {}
"isaacsim.storage.native" = {}
"omni.graph.action" = {}
"omni.graph.core" = {}
"omni.graph.nodes" = {}
"omni.isaac.dynamic_control" = {}
"omni.kit.uiapp" = {}
"omni.physx" = {}

[[python.module]]
name = "isaacsim.examples.interactive.tests"

[[python.module]]
name = "isaacsim.examples.interactive.kaya_gamepad"

[[python.module]]
name = "isaacsim.examples.interactive.omnigraph_keyboard"

[[python.module]]
name = "isaacsim.examples.interactive.follow_target"

[[python.module]]
name = "isaacsim.examples.interactive.path_planning"

[[python.module]]
name = "isaacsim.examples.interactive.simple_stack"

[[python.module]]
name = "isaacsim.examples.interactive.bin_filling"

[[python.module]]
name = "isaacsim.examples.interactive.robo_factory"

[[python.module]]
name = "isaacsim.examples.interactive.robo_party"

[[python.module]]
name = "isaacsim.examples.interactive.hello_world"

[[python.module]]
name = "isaacsim.examples.interactive.replay_follow_target"

[[python.module]]
name = "isaacsim.examples.interactive.surface_gripper"

[[python.module]]
name = "isaacsim.examples.interactive.quadruped"

[[python.module]]
name = "isaacsim.examples.interactive.user_examples"

[[python.module]]
name = "isaacsim.examples.interactive.ur10_palletizing"

[[python.module]]
name = "isaacsim.examples.interactive.franka_cortex"

[[python.module]]
name = "isaacsim.examples.interactive.humanoid"

[[python.module]]
name = "isaacsim.examples.interactive.getting_started"

[[python.module]]
name = "isaacsim.examples.interactive.franka"

[[test]]
timeout = 900

stdoutFailPatterns.exclude = [
    '*[Error] [carb] [Plugin: omni.sensors.nv.lidar.ext.plugin] Dependency: [omni::sensors::lidar::IGenericModelOutputIOFactory v0.1] failed to be resolved.*', # feature not included in Windows
]

args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
    ### Extension specific args
    "--/app/window/dpiScaleOverride=1.0",
    "--/app/window/scaleToMonitor=false",
]

[[test]]
name = "startup"
args = [
    "--/app/settings/fabricDefaultStageFrameHistoryCount = 3",
]
