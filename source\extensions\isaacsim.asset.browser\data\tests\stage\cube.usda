#usda 1.0
(
    customLayerData = {
        dictionary cameraSettings = {
            dictionary Front = {
                double3 position = (0, 0, 50000)
                double radius = 500
            }
            dictionary Perspective = {
                double3 position = (280.9495917554552, 142.62804622084252, 279.4779054048457)
                double3 target = (-0.000002279287684814335, -0.0000013084406873531407, 0.0000029590362942144566)
            }
            dictionary Right = {
                double3 position = (-50000, 0, -1.1102230246251565e-11)
                double radius = 500
            }
            dictionary Top = {
                double3 position = (-4.329780281177466e-12, 50000, 1.1102230246251565e-11)
                double radius = 500
            }
            string boundCamera = "/OmniverseKit_Persp"
        }
        dictionary omni_layer = {
            string authoring_layer = "./cube2.usda"
            dictionary locked = {
            }
            dictionary muteness = {
            }
        }
        dictionary renderSettings = {
            float3 "rtx:debugView:pixelDebug:textColor" = (0, 1e18, 0)
            float3 "rtx:fog:fogColor" = (0.75, 0.75, 0.75)
            float3 "rtx:index:regionOfInterestMax" = (0, 0, 0)
            float3 "rtx:index:regionOfInterestMin" = (0, 0, 0)
            float3 "rtx:iray:environment_dome_ground_position" = (0, 0, 0)
            float3 "rtx:iray:environment_dome_ground_reflectivity" = (0, 0, 0)
            float3 "rtx:iray:environment_dome_rotation_axis" = (3.4028235e38, 3.4028235e38, 3.4028235e38)
            float3 "rtx:post:backgroundZeroAlpha:backgroundDefaultColor" = (0, 0, 0)
            float3 "rtx:post:colorcorr:contrast" = (1, 1, 1)
            float3 "rtx:post:colorcorr:gain" = (1, 1, 1)
            float3 "rtx:post:colorcorr:gamma" = (1, 1, 1)
            float3 "rtx:post:colorcorr:offset" = (0, 0, 0)
            float3 "rtx:post:colorcorr:saturation" = (1, 1, 1)
            float3 "rtx:post:colorgrad:blackpoint" = (0, 0, 0)
            float3 "rtx:post:colorgrad:contrast" = (1, 1, 1)
            float3 "rtx:post:colorgrad:gain" = (1, 1, 1)
            float3 "rtx:post:colorgrad:gamma" = (1, 1, 1)
            float3 "rtx:post:colorgrad:lift" = (0, 0, 0)
            float3 "rtx:post:colorgrad:multiply" = (1, 1, 1)
            float3 "rtx:post:colorgrad:offset" = (0, 0, 0)
            float3 "rtx:post:colorgrad:whitepoint" = (1, 1, 1)
            float3 "rtx:post:lensDistortion:lensFocalLengthArray" = (10, 30, 50)
            float3 "rtx:post:lensFlares:anisoFlareFalloffX" = (450, 475, 500)
            float3 "rtx:post:lensFlares:anisoFlareFalloffY" = (10, 10, 10)
            float3 "rtx:post:lensFlares:cutoffPoint" = (2, 2, 2)
            float3 "rtx:post:lensFlares:haloFlareFalloff" = (10, 10, 10)
            float3 "rtx:post:lensFlares:haloFlareRadius" = (75, 75, 75)
            float3 "rtx:post:lensFlares:isotropicFlareFalloff" = (50, 50, 50)
            float3 "rtx:post:tonemap:whitepoint" = (1, 1, 1)
            float3 "rtx:raytracing:indexdirect:svoBrickSize" = (32, 32, 32)
            float3 "rtx:raytracing:inscattering:singleScatteringAlbedo" = (0.9, 0.9, 0.9)
            float3 "rtx:raytracing:inscattering:transmittanceColor" = (0.5, 0.5, 0.5)
            float3 "rtx:sceneDb:ambientLightColor" = (0.1, 0.1, 0.1)
        }
    }
    defaultPrim = "World"
    endTimeCode = 100
    metersPerUnit = 0.01
    startTimeCode = 0
    timeCodesPerSecond = 24
    upAxis = "Y"
)

def Xform "World"
{
    def DistantLight "defaultLight" (
        prepend apiSchemas = ["ShapingAPI"]
    )
    {
        float angle = 1
        float intensity = 3000
        float shaping:cone:angle = 180
        float shaping:cone:softness
        float shaping:focus
        color3f shaping:focusTint
        asset shaping:ies:file
        double3 xformOp:rotateXYZ = (315, 0, 0)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:rotateXYZ", "xformOp:scale"]
    }

    def Mesh "Cube"
    {
        int[] faceVertexCounts = [4, 4, 4, 4, 4, 4]
        int[] faceVertexIndices = [0, 1, 3, 2, 0, 4, 5, 1, 1, 5, 6, 3, 2, 3, 6, 7, 0, 2, 7, 4, 4, 7, 6, 5]
        normal3f[] normals = [(0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, 0, -1), (0, 0, -1), (0, 0, -1), (0, 0, -1), (1, 0, 0), (1, 0, 0), (1, 0, 0), (1, 0, 0), (0, 0, 1), (0, 0, 1), (0, 0, 1), (0, 0, 1), (-1, 0, 0), (-1, 0, 0), (-1, 0, 0), (-1, 0, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0)] (
            interpolation = "faceVarying"
        )
        point3f[] points = [(-50, -50, -50), (50, -50, -50), (-50, -50, 50), (50, -50, 50), (-50, 50, -50), (50, 50, -50), (50, 50, 50), (-50, 50, 50)]
        float2[] primvars:st = [(1, 0), (0, 0), (0, 1), (1, 1), (1, 0), (1, 1), (0, 1), (0, 0), (1, 0), (0, 0), (0, 1), (1, 1), (1, 0), (0, 0), (0, 1), (1, 1), (1, 0), (1, 1), (0, 1), (0, 0), (1, 0), (1, 1), (0, 1), (0, 0)] (
            interpolation = "faceVarying"
        )
        uniform token subdivisionScheme = "none"
        double3 xformOp:rotateXYZ = (0, 0, 0)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:rotateXYZ", "xformOp:scale"]
    }
}

