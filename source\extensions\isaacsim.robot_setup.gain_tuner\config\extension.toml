[package]
version = "3.0.4"
category = "Simulation"
title = "Gain Tuner"
description = "<PERSON><PERSON> Tuner for Articulation PD Gains"
keywords = ["isaac", "physics", "analyze", "tune"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.experimental.prims" = {}
"isaacsim.gui.components" = {}
"isaacsim.robot.schema" = {}
"omni.kit.uiapp" = {}

[[python.module]]
name = "isaacsim.robot_setup.gain_tuner"
