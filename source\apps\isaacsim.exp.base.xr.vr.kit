[package]
description = "Main Omniverse Isaac Sim Application with XR VR"
execFile = "isaac-sim.xr.vr"
title = "Isaac Sim XR VR"
version = "5.0.0"
keywords = ["experience", "app", "usd", "isaacsim", "xr"] # That makes it browsable in UI with "experience" filter


[dependencies]
"isaacsim.exp.base" = {}
"omni.kit.xr.profile.vr" = {}

[settings]
app.name = "Isaac-Sim XR VR"
app.version = "5.0.0"
app.vulkan = true                       # Explicitly enable Vulkan (on by default on Linux, off by default on Windows)
app.enableDeveloperWarnings = false     # disable developer warnings to reduce log noise

[settings.app.exts.folders]
'++' = [
    "${app}",
    "${app}/../exts",
    "${app}/../extscache",
    "${app}/../extsUser",
    "${app}/../extsDeprecated",
]

[settings.app.window]
hideUi = false
title = "Isaac Sim XR VR"

[settings.exts."omni.kit.registry.nucleus"]
registries = [
	{ name = "kit/default", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/107/shared" },
	{ name = "kit/sdk", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/sdk/${kit_version_short}/${kit_git_hash}" },
	{ name = "kit/community", url = "https://dw290v42wisod.cloudfront.net/exts/kit/community" },
]
