{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.\n", "# SPDX-License-Identifier: Apache-2.0\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# http://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n"]}, {"cell_type": "code", "execution_count": null, "id": "420e8940", "metadata": {}, "outputs": [], "source": ["from isaacsim import SimulationApp\n", "\n", "# Set the path below to your desired nucleus server\n", "# Make sure you installed a local nucleus server before this\n", "simulation_app = SimulationApp()"]}, {"cell_type": "code", "execution_count": null, "id": "2955b2e2", "metadata": {}, "outputs": [], "source": ["from isaacsim.core.api import World\n", "from isaacsim.core.api.objects import DynamicCuboid\n", "import numpy as np\n", "\n", "world = World(stage_units_in_meters=1.0)\n", "world.scene.add_default_ground_plane()\n", "# A render/ step or an update call is needed to reflect the changes to the opened USD in Isaac Sim GUI\n", "#  Note: avoid pressing play/ pause or stop in the GUI in this workflow.\n", "world.render()\n"]}, {"cell_type": "code", "execution_count": null, "id": "702a40af", "metadata": {}, "outputs": [], "source": ["fancy_cube = world.scene.add(\n", "    DynamicCuboid(\n", "        prim_path=\"/World/random_cube\",\n", "        name=\"fancy_cube\",\n", "        position=np.array([0, 0, 1.000]),\n", "        scale=np.array([0.5015, 0.505, 0.5015]),\n", "        size=1.0,\n", "        color=np.array([0, 0, 1.0]),\n", "    )\n", ")\n", "world.render()\n"]}, {"cell_type": "code", "execution_count": null, "id": "10a83e53", "metadata": {}, "outputs": [], "source": ["world.reset()\n", "for i in range(500):\n", "    position, orientation = fancy_cube.get_world_pose()\n", "    linear_velocity = fancy_cube.get_linear_velocity()\n", "    print(\"Cube position is : \" + str(position))\n", "    print(\"<PERSON><PERSON>'s orientation is : \" + str(orientation))\n", "    print(\"Cube's linear velocity is : \" + str(linear_velocity))\n", "    # we have control over stepping physics and rendering in this workflow\n", "    # things run in sync\n", "    world.step(render=True)  # execute one physics step and one rendering step\n"]}, {"cell_type": "code", "execution_count": null, "id": "02f57ca0", "metadata": {}, "outputs": [], "source": ["# Cleanup application\n", "simulation_app.close()"]}], "metadata": {"kernelspec": {"display_name": "Isaac Sim Python 3", "language": "python", "name": "isaac_sim_python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}}, "nbformat": 4, "nbformat_minor": 5}