# Changelog
## [1.3.15] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [1.3.14] - 2025-05-27
### Changed
- <PERSON>rowser not visible after startup

## [1.3.13] - 2025-05-22
### Changed
- Update copyright and license to apache v2.0

## [1.3.12] - 2025-05-20
### Changed
- Update assets path to staging

## [1.3.11] - 2025-05-10
### Changed
- Enable FSD in test settings

## [1.3.10] - 2025-04-09
### Changed
- Update all test args to be consistent

## [1.3.9] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.3.8] - 2025-03-31
### Changed
- Added preferred dock location for the browser window for layout purposes

## [1.3.7] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.3.6] - 2025-03-04
### Changed
- Update to kit 107.1 and fix build issues

## [1.3.5] - 2025-02-13
### Changed
- focus on the browser window when it is opened

## [1.3.4] - 2025-01-26
### Changed
- Updated browser cache

## [1.3.3] - 2025-01-23
### Fixed
- option for non-usd files to be downloaded instead of open on stage

## [1.3.2] - 2025-01-22
### Fixed
- Ingore unit test error in logs

## [1.3.1] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.3.0] - 2025-01-17
### Changed
- Put default filter for files, set it as an app setting

## [1.2.0] - 2025-01-09
### Changed
- removed default filter for files, so that the browser displays all file types

### Added
- Download option for file types

## [1.1.9] - 2025-01-08
### Changed
- Preloaded assets browser cache data into the extension cache folder

### Added
- Added additional texture files to search filter

## [1.1.8] - 2024-12-30
### Fixed
- Broken icon path when using with non isaac sim apps

## [1.1.7] - 2024-12-16
### Fixed
- Fixed the disappearing asset titles

## [1.1.6] - 2024-12-10
### Fixed
- Fixed the search functionality to make all assets searchable

## [1.1.5] - 2024-12-03
### Changed
- Double click opens the file instead of loading it as payload/reference

## [1.1.4] - 2024-11-13
### Changed
- separate buttons for loading asset as reference and for opening the original usd

## [1.1.3] - 2024-11-12
### Fixed
- fix for browser option panel interference

## [1.1.2] - 2024-11-01
### Added
- Registered action of opening asset browser

## [1.1.1] - 2024-11-01
### Changed
- Rename browser

## [1.1.0] - 2024-10-29
### Changed
- Switch to UI backend that supports Option Panel. Variant selection available for assets that have it.

## [1.0.2] - 2024-10-28
### Changed
- Remove test imports from runtime

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-09-23
### Changed

- Extension renamed to isaacsim.asset.browser

## [0.5.1] - 2024-08-05
### Changed

- Update asset paths to 4.2

## [0.5.0] - 2024-06-30
### Changed

- Update asset paths to 4.1

## [0.4.6] - 2024-05-20
### Added

- Add IsaacLab folder

## [0.4.5] - 2024-04-15
### Fixed

- Unit test

## [0.4.4] - 2024-01-31
### Changed

- Update asset paths to 4.0

## [0.4.3] - 2023-12-12
### Changed

- Disable test when windowing is not present

## [0.4.2] - 2023-11-29
### Changed

- Updated golden image

## [0.4.1] - 2023-11-15
### Changed

- Fix missing default icon

## [0.4.0] - 2023-11-13
### Changed

- Update based on omni.kit.browser.asset-1.3.7

## [0.3.2] - 2023-11-09
### Changed

- Update asset paths to 2023.1.1

## [0.3.1] - 2023-08-03
### Changed

- Add Materials folder

## [0.3.0] - 2023-03-31
### Changed

- Update asset paths to 2023.1.0

## [0.2.4] - 2023-02-14
### Changed

- Add Sensors folder

## [0.2.3] - 2023-01-06
### Changed

- Update asset paths to 2022.2.1

## [0.2.2] - 2022-12-12
### Changed

- Update asset paths to 2022.2.0

## [0.2.1] - 2022-12-10
### Added
- Added People folder

## [0.2.0] - 2022-09-02
### Changed
- Use new viewport iterfaces

## [0.1.1] - 2022-08-31
### Changed
- Update paths to 2022.2

## [0.1.0] - 2022-04-27
### Added
- First Release
