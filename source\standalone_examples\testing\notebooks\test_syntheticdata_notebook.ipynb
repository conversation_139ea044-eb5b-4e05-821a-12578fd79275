{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.\n", "# SPDX-License-Identifier: Apache-2.0\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# http://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n"]}, {"cell_type": "code", "execution_count": null, "id": "15c98eca", "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "from isaacsim import SimulationApp\n", "\n", "simulation_app = SimulationApp(launch_config={\"headless\": True})\n", "import omni\n", "\n", "simulation_app.update()\n", "omni.usd.get_context().new_stage()\n", "simulation_app.update()\n"]}, {"cell_type": "code", "execution_count": null, "id": "8743b6c2", "metadata": {}, "outputs": [], "source": ["from isaacsim.sensors.camera import Camera\n", "from isaacsim.core.api.objects import VisualCuboid, DynamicCuboid\n", "from omni.kit.viewport.utility import get_active_viewport\n", "\n", "viewport_api = get_active_viewport()\n", "render_product_path = viewport_api.get_render_product_path()\n", "camera = Camera(\n", "    prim_path=\"/World/camera\",\n", "    position=np.array([0.0, 0.0, 25.0]),\n", "    resolution=(1280, 720),\n", "    render_product_path = render_product_path\n", ")\n", "simulation_app.update()\n", "camera.initialize()\n", "simulation_app.update()\n", "camera.add_distance_to_image_plane_to_frame()\n", "camera.add_bounding_box_2d_tight_to_frame()\n", "camera.add_bounding_box_2d_loose_to_frame()\n", "camera.add_instance_segmentation_to_frame()\n", "camera.add_semantic_segmentation_to_frame()\n", "camera.add_bounding_box_3d_to_frame()\n", "simulation_app.update()\n", "\n", "\n", "VisualCuboid(\n", "    prim_path=\"/new_cube_1\",\n", "    name=\"visual_cube\",\n", "    position=np.array([0, 0, 0.5]),\n", "    scale=np.array([1, 1, 1]),\n", "    color=np.array([255, 255, 255]),\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "57ae724f", "metadata": {}, "outputs": [], "source": ["omni.timeline.get_timeline_interface().play()\n", "\n", "for _ in range(10):\n", "    simulation_app.update()\n", "\n", "rgb = camera.get_rgba()\n", "\n", "print(rgb.size)\n", "if rgb.size != 1280 * 720 * 4:\n", "    raise ValueError(f\"RGB buffer has size of {rgb.size} which is not {1280*720*4}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "1be0ac67", "metadata": {}, "outputs": [], "source": ["import omni.graph.core as og\n", "import omni\n", "\n", "from omni.syntheticdata import sensors\n", "\n", "simulation_app.update()\n", "omni.timeline.get_timeline_interface().play()\n", "viewport_api = get_active_viewport()\n", "import omni.syntheticdata._syntheticdata as sd\n", "\n", "sensors.enable_sensors(viewport_api, [sd.SensorType.DistanceToImagePlane])\n", "simulation_app.update()\n", "graph = og.ObjectLookup.graph(\"/Render/PostProcess/SDGPipeline\")\n", "raw_node = og.ObjectLookup.node(\n", "    \"/Render/PostProcess/SDGPipeline/PostProcessDispatcher\"\n", ")\n", "swh_attr = og.Controller.attribute(\"outputs:referenceTimeNumerator\", raw_node)\n", "first = og.DataView.get(swh_attr)\n", "simulation_app.update()\n", "second = og.DataView.get(swh_attr)\n", "\n", "if first + 1 != second:\n", "    raise ValueError(f\"swh frame numbers {first}, {second} should be one apart\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "f4687a03", "metadata": {}, "outputs": [], "source": ["# Cleanup application\n", "simulation_app.close()\n"]}], "metadata": {"kernelspec": {"display_name": "Isaac Sim Python 3", "language": "python", "name": "isaac_sim_python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}