{"QuinticPathPlanner": {"version": 1, "description": "Quintic Path Planner For Wheeled robots", "language": "Python", "categories": {"isaacWheeledRobots": "robot path planning inside <PERSON>"}, "metadata": {"uiName": "Quintic Path Planner"}, "$comment": "Use odometry from a robot and a target position/prim to calculate a route from the robot's starting position to the target position.", "inputs": {"execIn": {"type": "execution", "description": "The input execution"}, "currentPosition": {"type": "vectord[3]", "description": "Current position of the robot (recommended to use Get Prim Local to World Transform node)"}, "currentOrientation": {"type": "quatd[4]", "description": "Current rotation of the robot as a quaternion (recommended to use Get Prim Local to World Transform node)"}, "targetPrim": {"type": "target", "optional": true, "description": "USD prim reference to the goal position/orientation prim"}, "targetPosition": {"type": "vectord[3]", "description": "Target position (used if no targetPrim provided)"}, "targetOrientation": {"type": "quatd[4]", "description": "Target orientation (used if no targetPrim provided)"}, "initialVelocity": {"type": "double", "description": "Initial velocity", "default": 0.5}, "initialAccel": {"type": "double", "description": "Initial acceleration", "default": 0.02}, "goalVelocity": {"type": "double", "description": "Goal velocity", "default": 0.5}, "goalAccel": {"type": "double", "description": "Goal acceleration", "default": 0.02}, "maxAccel": {"type": "double", "description": "Max acceleration", "default": 1.5}, "maxJerk": {"type": "double", "description": "Max jerk", "default": 0.3}, "step": {"type": "double", "description": "Step", "default": 0.16666666667}}, "outputs": {"execOut": {"type": "execution", "description": "The output execution"}, "pathArrays": {"type": "double[]", "description": "The path v, x, y, and yaw arrays"}, "target": {"type": "double[3]", "description": "Target position and orientation"}, "targetChanged": {"type": "bool", "description": "Target position/orientation has changed"}}}}