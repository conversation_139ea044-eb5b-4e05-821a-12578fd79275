<project toolsVersion="5.6">
  <!-- Only edit this file to pull kit depedencies. -->
  <!-- Put all extension-specific dependencies in `ext-deps.packman.xml`. -->
  <!-- This file contains shared Kit SDK dependencies used by most kit extensions. -->

  <!-- Import Kit SDk target-deps xml file to steal some deps from it: -->
  <import path="../_build/${platform_target}/${config}/kit/dev/all-deps.packman.xml">
    <filter include="pybind11" />
    <filter include="python" />
    <filter include="fmt" />
    <filter include="nvtx" />
    <filter include="gsl" />
    <filter include="carb_sdk_plugins" />
    <filter include="usd-${config}" />
    <filter include="cuda" />
    <filter include="omni_client_library" />
    <filter include="usd-release" />
    <filter include="premake" linkPath="../_build/host-deps/premake" />
  </import>

  <!-- Import Physics plugins deps -->
  <import path="../_build/target-deps/omni_physics/${config}/deps/target-deps.packman.xml">
    <filter include="physx" />
  </import>

  <import path="../_build/target-deps/omni_physics/${config}/deps/schema-deps.packman.xml">
    <filter include="usd_ext_physics_${config}" />
  </import>


  <!-- Pull those deps of the same version as in Kit SDK. Override linkPath to point correctly, other properties can also be override, including version. -->
  <dependency name="carb_sdk_plugins" linkPath="../_build/target-deps/carb_sdk_plugins" />
  <dependency name="usd-${config}" linkPath="../_build/target-deps/usd/${config}" />
  <dependency name="cuda" linkPath="../_build/target-deps/cuda" />
  <dependency name="omni_client_library" linkPath="../_build/target-deps/omni_client_library" />

  <dependency name="pybind11" linkPath="../_build/target-deps/pybind11" />
  <dependency name="fmt" linkPath="../_build/target-deps/fmt" />
  <dependency name="python" linkPath="../_build/target-deps/python" />
  <dependency name="nvtx" linkPath="../_build/target-deps/nvtx" />

  <dependency name="physx" linkPath="../_build/target-deps/physx" />
  <dependency name="usd_ext_physics_${config}" linkPath="../_build/target-deps/usd_ext_physics/${config}" />
  <dependency name="gsl" linkPath="../_build/target-deps/gsl" />

  <import path="../_build/${platform_target}/${config}/kit/dev/deps/linbuild.packman.xml">
    <filter include="linbuild" linkPath="../_build/host-deps/linbuild" tags="non-redist" />
  </import>
</project>
