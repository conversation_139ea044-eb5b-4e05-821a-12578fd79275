[package]
version = "0.3.13"
category = "Setup"
title = "Isaac Sim Assets Check"
description = "This Extension checks if the assets are updated."
keywords = ["isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
toggleable = false
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.asset.browser."

[dependencies]
"isaacsim.asset.browser" = {}
"isaacsim.core.api" = {}
"omni.ui" = { order = -100 }
"omni.usd" = {}

[[python.module]]
name = "omni.isaac.assets_check"
