# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

seed: 123456
step_size: 0.05
max_iterations: 50000
max_sampling: 10000
distance_metric_weights: [3.0, 2.0, 2.0, 1.5, 1.5, 1.0, 1.0]
task_space_frame_name: "panda_rightfingertip"
task_space_limits: [[0.0, 0.7], [-0.6, 0.6], [0.0, 0.8]]
cuda_tree_params:
  max_num_nodes: 10000
  max_buffer_size: 30
  num_nodes_cpu_gpu_crossover: 3000
c_space_planning_params:
  exploration_fraction: 0.5
task_space_planning_params:
  translation_target_zone_tolerance: 0.05
  orientation_target_zone_tolerance: 0.09
  translation_target_final_tolerance: 1e-4
  orientation_target_final_tolerance: 0.005
  translation_gradient_weight: 1.0
  orientation_gradient_weight: 0.125
  nn_translation_distance_weight: 1.0
  nn_orientation_distance_weight: 0.125
  task_space_exploitation_fraction: 0.4
  task_space_exploration_fraction: 0.1
  max_extension_substeps_away_from_target: 6
  max_extension_substeps_near_target: 50
  extension_substep_target_region_scale_factor: 2.0
  unexploited_nodes_culling_scalar: 1.0
  gradient_substep_size: 0.025
