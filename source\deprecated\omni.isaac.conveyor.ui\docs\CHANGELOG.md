# Changelog
## [2.0.9] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.0.8] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [2.0.7] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.0.6] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.0.5] - 2025-03-25
### Changed
- Add import tests for deprecated extensions

## [2.0.4] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.3] - 2025-01-08
### Changed
- Fix extension renaming

## [2.0.2] - 2024-12-11
### Changed
- Fix extension renaming

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

##  [2.0.0] - 2024-09-27

### Changed
- Extension deprecated since <PERSON> Si<PERSON> 4.5.0. Replaced by isaacsim.asset.gen.conveyor.ui

## [1.1.0] - 2024-06-13
### Fixed
- Fixed an edge case when the first conveyor piece is added - to select the second connection point if it's a multi-connection (so it always goes forward)

### Changed
- Moved the assets source to a configurable setting
- Moved the config path to a configurable setting
- Moved the Conveyor Track Builder to UI.

## [1.0.1] - 2024-05-06
### Changed
- Updated asset path

## [1.0.0] - 2024-03-20
- First Version
