# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

adjacencies:
- id: empty_straight
  neighbors:
  - neighbor_id: empty
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: double_shelf_a
    neighbor_rotation: 0
    self_rotation: 2
  - neighbor_id: double_shelf_a
    neighbor_rotation: 3
    self_rotation: 0
  - neighbor_id: single_shelf
    neighbor_rotation: 1
    self_rotation: 2
  - neighbor_id: double_shelf_a
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: single_shelf
    neighbor_rotation: 0
    self_rotation: 2
  - neighbor_id: single_shelf
    neighbor_rotation: 3
    self_rotation: 0
  - neighbor_id: building_center
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: building_end
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: double_shelf_a
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: building_center
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: building_end
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: single_shelf
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: empty
    neighbor_rotation: 3
    self_rotation: 0
  - neighbor_id: single_shelf
    neighbor_rotation: 2
    self_rotation: 0
  - neighbor_id: building_center
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: building_end
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: double_shelf_a
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: single_shelf
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: single_shelf
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: building_center
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: building_end
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: double_shelf_b
    neighbor_rotation: 1
    self_rotation: 2
  - neighbor_id: single_shelf
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: double_shelf_b
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: double_shelf_b
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: empty
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: double_shelf_b
    neighbor_rotation: 3
    self_rotation: 0
  - neighbor_id: double_shelf_b
    neighbor_rotation: 2
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: double_shelf_b
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty
    neighbor_rotation: 1
    self_rotation: 2
  - neighbor_id: empty_cross
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: empty_cross
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: empty_cross
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: double_shelf_a
    neighbor_rotation: 1
    self_rotation: 2
- id: empty_cross
  neighbors:
  - neighbor_id: empty
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: empty
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: building_center
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: building_end
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: building_center
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: building_end
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: building_center
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: building_end
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: empty_straight
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: building_center
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: building_end
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 3
    self_rotation: 3
- id: double_shelf_a
  neighbors:
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 1
  - neighbor_id: empty
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: building_center
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: building_end
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: double_shelf_b
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 3
  - neighbor_id: empty
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: building_center
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: building_end
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: double_shelf_b
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 2
- id: double_shelf_b
  neighbors:
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 1
  - neighbor_id: empty
    neighbor_rotation: 0
    self_rotation: 2
  - neighbor_id: empty
    neighbor_rotation: 2
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 3
  - neighbor_id: double_shelf_a
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: double_shelf_a
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 1
- id: single_shelf
  neighbors:
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 1
  - neighbor_id: empty
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 3
  - neighbor_id: empty
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 2
- id: building_center
  neighbors:
  - neighbor_id: double_shelf_a
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: double_shelf_a
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: empty_cross
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: empty_cross
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: empty_straight
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 3
    self_rotation: 3
- id: building_end
  neighbors:
  - neighbor_id: double_shelf_a
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: double_shelf_a
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: empty_cross
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: empty_cross
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: empty_straight
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 3
    self_rotation: 3
- id: empty
  neighbors:
  - neighbor_id: empty
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: empty
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 1
  - neighbor_id: double_shelf_b
    neighbor_rotation: 2
    self_rotation: 0
  - neighbor_id: double_shelf_b
    neighbor_rotation: 0
    self_rotation: 2
  - neighbor_id: double_shelf_a
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: empty_straight
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_straight
    neighbor_rotation: 0
    self_rotation: 3
  - neighbor_id: single_shelf
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: double_shelf_a
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: empty_cross
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: single_shelf
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: empty_cross
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: empty
    neighbor_rotation: 2
    self_rotation: 2
tiles:
- id: empty_straight
  weights:
  - 1
  - 1
  - 1
  - 1
- id: empty_cross
  weights:
  - 1
  - 1
  - 1
  - 1
- id: double_shelf_a
  weights:
  - 1
  - 1
  - 1
  - 1
- id: double_shelf_b
  weights:
  - 1
  - 1
  - 1
  - 1
- id: single_shelf
  weights:
  - 1
  - 1
  - 1
  - 1
- id: building_center
  weights:
  - 1
  - 1
  - 1
  - 1
- id: building_end
  weights:
  - 1
  - 1
  - 1
  - 1
- id: empty
  weights:
  - 1
  - 1
  - 1
  - 1
