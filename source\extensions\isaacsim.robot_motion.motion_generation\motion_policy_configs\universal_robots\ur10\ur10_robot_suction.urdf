<?xml version="1.0" ?>
<!--
The original version of this URDF was autogenerated by x<PERSON><PERSON> from ur10_robot.urdf.xacro,
found at https://github.com/ros-industrial/universal_robot/blob/1.2.7/ur_description/urdf/

  Author: <PERSON>
  Contributers: <PERSON>, <PERSON><PERSON>, <PERSON>

Modifications:

2021-09-22 Removed "gazebo" section.
2021-09-22 Mesh files are now referenced via relative local file paths.
2021-09-22 Visual meshes have been converted from DAE format to STL, using MeshLab v1.3.2.
-->
<robot name="ur10">
  <!-- measured from model -->
  <link name="base_link">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/base.stl"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/base.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="4.0"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.0061063308908" ixy="0.0" ixz="0.0" iyy="0.0061063308908" iyz="0.0" izz="0.01125"/>
    </inertial>
  </link>
  <joint name="shoulder_pan_joint" type="revolute">
    <parent link="base_link"/>
    <child link="shoulder_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.1273"/>
    <axis xyz="0 0 1"/>
    <limit effort="330.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="2.16"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="shoulder_link">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/shoulder.stl"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/shoulder.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="7.778"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.03147431257693659" ixy="0.0" ixz="0.0" iyy="0.03147431257693659" iyz="0.0" izz="0.021875624999999996"/>
    </inertial>
  </link>
  <joint name="shoulder_lift_joint" type="revolute">
    <parent link="shoulder_link"/>
    <child link="upper_arm_link"/>
    <origin rpy="0.0 1.5707963267948966 0.0" xyz="0.0 0.220941 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="330.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="2.16"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="upper_arm_link">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/upperarm.stl"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/upperarm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="12.93"/>
      <origin rpy="0 0 0" xyz="0.0 -0.045 0.306"/>
      <inertia ixx="0.42175380379841093" ixy="0.0" ixz="0.0" iyy="0.42175380379841093" iyz="0.0" izz="0.03636562499999999"/>
    </inertial>
  </link>
  <joint name="elbow_joint" type="revolute">
    <parent link="upper_arm_link"/>
    <child link="forearm_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 -0.1719 0.612"/>
    <axis xyz="0 1 0"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.15"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="forearm_link">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/forearm.stl"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/forearm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="3.87"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.28615"/>
      <inertia ixx="0.11106969409710458" ixy="0.0" ixz="0.0" iyy="0.11106969409710458" iyz="0.0" izz="0.010884375"/>
    </inertial>
  </link>
  <joint name="wrist_1_joint" type="revolute">
    <parent link="forearm_link"/>
    <child link="wrist_1_link"/>
    <origin rpy="0.0 1.5707963267948966 0.0" xyz="0.0 0.0 0.5723"/>
    <axis xyz="0 1 0"/>
    <limit effort="54.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.2"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="wrist_1_link">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/wrist1.stl"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/wrist1.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.96"/>
      <origin rpy="0 0 0" xyz="0.0 0.1149 0.0"/>
      <inertia ixx="0.005108247956699999" ixy="0.0" ixz="0.0" iyy="0.005108247956699999" iyz="0.0" izz="0.005512499999999999"/>
    </inertial>
  </link>
  <joint name="wrist_2_joint" type="revolute">
    <parent link="wrist_1_link"/>
    <child link="wrist_2_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.1149 0.0"/>
    <axis xyz="0 0 1"/>
    <limit effort="54.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.2"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="wrist_2_link">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/wrist2.stl"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/wrist2.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.96"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.1157"/>
      <inertia ixx="0.005108247956699999" ixy="0.0" ixz="0.0" iyy="0.005108247956699999" iyz="0.0" izz="0.005512499999999999"/>
    </inertial>
  </link>
  <joint name="wrist_3_joint" type="revolute">
    <parent link="wrist_2_link"/>
    <child link="wrist_3_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.1157"/>
    <axis xyz="0 1 0"/>
    <limit effort="54.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.2"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="wrist_3_link">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/wrist3.stl"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/wrist3.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.202"/>
      <origin rpy="1.5707963267948966 0 0" xyz="0.0 0.07695 0.0"/>
      <inertia ixx="0.00011792166116465" ixy="0.0" ixz="0.0" iyy="0.00011792166116465" iyz="0.0" izz="0.00020452500000000002"/>
    </inertial>
  </link>
  <joint name="ee_fixed_joint" type="fixed">
    <parent link="wrist_3_link"/>
    <child link="ee_link"/>
    <origin rpy="1.5707963267948966 0.0 1.5707963267948966" xyz="0.0 0.0922 0.0"/>
  </joint>
  <link name="ee_link">
    <collision>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.01 0 0"/>
    </collision>
  </link>
  <joint name="ee_suction_joint" type="fixed">
    <parent link="ee_link"/>
    <child link="ee_suction_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.161709 0.0 0.0"/>
  </joint>
  <link name="ee_suction_link">
    <collision>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.01 0 0"/>
    </collision>
  </link>
  <transmission name="shoulder_pan_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="shoulder_pan_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="shoulder_pan_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="shoulder_lift_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="shoulder_lift_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="shoulder_lift_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="elbow_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="elbow_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="elbow_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="wrist_1_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_1_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="wrist_1_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="wrist_2_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_2_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="wrist_2_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="wrist_3_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_3_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="wrist_3_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <gazebo reference="shoulder_link">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="upper_arm_link">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="forearm_link">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="wrist_1_link">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="wrist_3_link">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="wrist_2_link">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="ee_link">
    <selfCollide>true</selfCollide>
  </gazebo>
  <!-- ROS base_link to UR 'Base' Coordinates transform -->
  <link name="base"/>
  <joint name="base_link-base_fixed_joint" type="fixed">
    <!-- NOTE: this rotation is only needed as long as base_link itself is
                 not corrected wrt the real robot (ie: rotated over 180
                 degrees)
      -->
    <origin rpy="0 0 -3.141592653589793" xyz="0 0 0"/>
    <parent link="base_link"/>
    <child link="base"/>
  </joint>
  <!-- Frame coincident with all-zeros TCP on UR controller -->
  <link name="tool0"/>
  <joint name="wrist_3_link-tool0_fixed_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0 0" xyz="0 0.0922 0"/>
    <parent link="wrist_3_link"/>
    <child link="tool0"/>
  </joint>
  <link name="world"/>
  <joint name="world_joint" type="fixed">
    <parent link="world"/>
    <child link="base_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0"/>
  </joint>
</robot>
