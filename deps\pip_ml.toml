########################################################################################################################
# Example:
########################################################################################################################

#
# [[dependency]]                           # Toml way to define array of objects.
# python = "../_build/target-deps/python"  # Path to python used to install. Absolute or relative to this config.
# packages = ["numpy==1.23.5"]                     # Array of packages to install, they all will be passed directly to pip.
# target = "../_build/target-deps/numpy"   # Folder to install into.   Absolute or relative to this config.
# platforms = ["windows-x86_64"]           # Platform(s) to filter, default is ["*"].
# download_only = true                     # Do pip download instead of install, gets a whl file that can be installed later.

# !!!
# IMPORTANT: If you change this config cleanup target folder. Script check that it already exists and does nothing.


# Options. 
# Various options are available that are passed through pip during the installation.

# download_only: Only download the pip package, do not install it.
# install_dependencies: When set to False, dependencies of a pip package will not be downloaded or installed
# append_to_install_folder: When set to true and the target directory already exists, these packages will be appended to the existing folder.
# build_isolated: Sets `--isolated` for pip install when installing. This means that environment variables and user settings will be ignored.
# add_python_to_lib_path: Will set --global-options to include Python's lib and include folders when running the pip install. 
#    Useful when a python pip package has a C extension that needs to bind to Python. Setting this to true will set build_isolated to False.

########################################################################################################################
# Dependencies:
########################################################################################################################

# For newer version of pip we bundle only wheel files, to be installed at runtime upon first usage. 
# Disabled because we get this with the base omni.kit.pip_archive extension
# [[dependency]]
# python = "../_build/target-deps/python"
# packages = ["pip==21.2.4"]
# target = "../_build/target-deps/pip_archive"
# platforms = ["*-x86_64"]
# download_only = true

# For many other pip packages used by default we install them in `pip_prebundle` folder to make available without need to install immediately.

# NOTE - For any new packages added to this list, please make sure that the license is compatible with Omniverse (i.e. avoid GPL/LGPL) and
# that a SWIPAT bug report is filed for legal to validate if there is no existing ticket.
# The SWIPAT template is here: http://nvbugs/2732212
# Dependencies for aiohttp should not be installed by default as we do not want to pull in chardet due to its license. Use cchardet instead

# Target kit 107.x
[[dependency]]
python = "../_build/target-deps/python"
packages = [
    # packages
    "torch==2.7.0+cu128",            # SWIPAT: https://nvbugs/3682155
    "torchvision==0.22.0+cu128",     # SWIPAT: https://nvbugs/3433812
    "torchaudio==2.7.0+cu128",       # SWIPAT: https://nvbugs/5326960

    # dependencies
    "filelock==3.13.1",              # SWIPAT: https://nvbugs/5303100
    "fsspec==2024.6.1",             # SWIPAT: https://nvbugs/5303035
    "mpmath==1.3.0",                # SWIPAT: https://nvbugs/5303037
    "networkx==3.3",                # SWIPAT: https://nvbugs/5303034
    "sympy==1.13.3",                # SWIPAT: https://nvbugs/5303081
]
extra_args = ["--extra-index-url", "https://download.pytorch.org/whl/cu128"]
target = "../_build/target-deps/isaac_ml_prebundle"
platforms = ["*-x86_64"]
download_only = false
append_to_install_folder = true
gather_licenses_path = "../_build/PACKAGE-LICENSES/PIP-prebundled-LICENSES.txt"
install_dependencies = false


# Windows torch does not need these so split them out. 
[[dependency]]
python = "../_build/target-deps/python"
packages = [
    "nvidia-cublas-cu12==*********",     # SWIPAT filed under: NA
    "nvidia-cuda-cupti-cu12==12.8.57",   # SWIPAT filed under: NA
    "nvidia-cuda-nvrtc-cu12==12.8.61",   # SWIPAT filed under: NA
    "nvidia-cuda-runtime-cu12==12.8.57", # SWIPAT filed under: NA
    "nvidia-cudnn-cu12==********",       # SWIPAT filed under: NA
    "nvidia-cufft-cu12==*********",      # SWIPAT filed under: NA
    "nvidia-cufile-cu12==*********",     # SWIPAT filed under: NA
    "nvidia-curand-cu12==*********",     # SWIPAT filed under: NA
    "nvidia-cusolver-cu12==*********",   # SWIPAT filed under: NA
    "nvidia-cusparse-cu12==*********",   # SWIPAT filed under: NA
    "nvidia-cusparselt-cu12==0.6.3",     # SWIPAT filed under: NA
    "nvidia-nccl-cu12==2.26.2",          # SWIPAT filed under: NA
    "nvidia-nvjitlink-cu12==12.8.61",    # SWIPAT filed under: NA
    "nvidia-nvtx-cu12==12.8.55",         # SWIPAT filed under: NA
]
extra_args = ["--extra-index-url", "https://download.pytorch.org/whl/cu128"]
target = "../_build/target-deps/isaac_ml_prebundle"
platforms = ["linux-x86_64"]
download_only = false
append_to_install_folder = true
gather_licenses_path = "../_build/PACKAGE-LICENSES/PIP-prebundled-LICENSES.txt"
install_dependencies = false
