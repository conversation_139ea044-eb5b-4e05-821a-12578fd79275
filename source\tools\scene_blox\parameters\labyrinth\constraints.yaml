# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Force corridor on (0, 0)
- type: restrict_type
  identifiers: ["corridor"]
  area:
    rows: [[0, 0]]
    cols: [[0, 0]]
- type: restrict_rotation
  identifier: ["corridor"]
  rotations: [0]
  area:
    rows: [[0, 0]]
    cols: [[0, 0]]
# Force corridor on (-1, -1)
- type: restrict_type
  identifiers: ["corridor"]
  area:
    rows: [[-1, -1]]
    cols: [[-1, -1]]
- type: restrict_rotation
  identifier: ["corridor"]
  rotations: [0]
  area:
    rows: [[-1, -1]]
    cols: [[-1, -1]]
# No more than 4 dead ends
- type: restrict_count
  identifiers: ["dead_end"]
  max_count: [4]
  area:
    rows: [[0, -1]]
    cols: [[0, -1]]
# Prevent dead ends and crosses on the borders
- type: exclude_type
  identifiers: ["dead_end", "cross"]
  area:
    rows: [[0, -1], [0, -1], [0, 0], [-1, -1]]
    cols: [[0, 0], [-1, -1], [0, -1], [0, -1]]
# Prevent outgoing corridors
- type: restrict_rotation
  identifier: ["corridor"]
  rotations: [1, 3]
  area:
    rows: [[0, 0]]
    cols: [[1, -2]]
- type: restrict_rotation
  identifier: ["corridor"]
  rotations: [1, 3]
  area:
    rows: [[-1, -1]]
    cols: [[0, -2]]
- type: restrict_rotation
  identifier: ["corridor"]
  rotations: [0, 2]
  area:
    rows: [[0, -1]]
    cols: [[0, 0]]
- type: restrict_rotation
  identifier: ["corridor"]
  rotations: [0, 2]
  area:
    rows: [[0, -1]]
    cols: [[-1, -1]]
# Prevent outgoing corners
- type: restrict_rotation
  identifier: ["corner"]
  rotations: [2, 3]
  area:
    rows: [[0, -1]]
    cols: [[0, 0]]
- type: restrict_rotation
  identifier: ["corner"]
  rotations: [0, 1]
  area:
    rows: [[0, -1]]
    cols: [[-1, -1]]
- type: restrict_rotation
  identifier: ["corner"]
  rotations: [1, 2]
  area:
    rows: [[0, 0]]
    cols: [[0, -1]]
- type: restrict_rotation
  identifier: ["corner"]
  rotations: [0, 3]
  area:
    rows: [[-1, -1]]
    cols: [[0, -1]]
