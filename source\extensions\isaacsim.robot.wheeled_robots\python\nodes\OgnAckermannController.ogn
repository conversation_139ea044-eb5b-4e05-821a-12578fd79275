{"AckermannController": {"version": 2, "description": "<PERSON><PERSON>mann Controller", "language": "Python", "categories": {"isaacWheeledRobots": "<PERSON><PERSON><PERSON> controller for wheels in Isaac <PERSON>"}, "metadata": {"uiName": "<PERSON><PERSON>mann Controller"}, "$comment": "", "inputs": {"execIn": {"type": "execution", "description": "The input execution"}, "acceleration": {"type": "double", "description": "Magnitude of acceleration for the robot in m/s^2. Set to 0.0 to instantly set velocity to desired forward speed", "default": 0.0}, "maxAcceleration": {"type": "double", "description": "Maximum magnitude of acceleration for the robot in m/s^2. Parameter is ignored if set to 0.0", "default": 0.0}, "speed": {"type": "double", "description": "Desired forward speed in m/s", "default": 0.0}, "steeringAngle": {"type": "double", "description": "Desired virtual angle in radians. Corresponds to the yaw of a virtual wheel located at the center of the front axle. By default it is positive for turning left and negative for turning right for front wheel drive.", "default": 0.0}, "maxWheelRotation": {"type": "double", "description": "Magnitude of maximum angle of rotation for the front wheels in radians. Parameter is ignored if set to 0.0", "default": 6.28}, "steeringAngleVelocity": {"type": "double", "description": "Magnitude of desired rate of change for steering angle in rad/s. Set to 0.0 to instantly snap wheels to desired steering angle", "default": 0.0}, "maxSteeringAngleVelocity": {"type": "double", "description": "Maximum magnitude of desired rate of change for steering angle in rad/s. Parameter is ignored if set to 0.0", "default": 0.0}, "wheelBase": {"type": "double", "description": "Distance between the front and rear axles of the robot in meters", "default": 0.0}, "trackWidth": {"type": "double", "description": "Distance between the left and right rear wheels of the robot in meters", "default": 0.0}, "frontWheelRadius": {"type": "double", "description": "Radius of the front wheels of the robot in meters", "default": 0.0}, "backWheelRadius": {"type": "double", "description": "Radius of the back wheels of the robot in meters", "default": 0.0}, "maxWheelVelocity": {"type": "double", "description": "Maximum magnitude of angular velocity of the robot wheel in rad/s. Parameter is ignored if set to 0.0", "default": 0.0}, "invertSteering": {"type": "bool", "description": "Flips the sign of the steering angle, Set to true for rear wheel steering", "default": false}, "dt": {"type": "double", "description": "Delta time for the simulation step"}}, "outputs": {"execOut": {"type": "execution", "description": "The output execution"}, "wheelAngles": {"type": "double[]", "description": "Angles of the left and right turning wheels in radians. In specific order: \nleft_wheel_angle, \nright_wheel_angle"}, "wheelRotationVelocity": {"type": "double[]", "description": "Angular velocity for the turning wheels in rad/s. In specific order: \nfront left wheel,\nfront right wheel,\nback left wheel,\nback right wheel"}}}}