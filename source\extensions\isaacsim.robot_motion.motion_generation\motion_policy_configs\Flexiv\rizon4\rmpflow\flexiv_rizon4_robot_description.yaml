# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot descriptor defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - joint1
    - joint2
    - joint3
    - joint4
    - joint5
    - joint6
    - joint7
default_q: [
    0.0,-0.5,0.0002,0.5,-0.0,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - link1:
    - "center": [-0.002, -0.002, 0.071]
      "radius": 0.069
    - "center": [-0.004, -0.011, 0.173]
      "radius": 0.062
    - "center": [0.003, -0.015, 0.22]
      "radius": 0.058
    - "center": [-0.002, -0.006, 0.113]
      "radius": 0.067
  - link2:
    - "center": [0.001, 0.036, 0.126]
      "radius": 0.061
    - "center": [0.005, 0.041, 0.031]
      "radius": 0.058
    - "center": [-0.007, 0.042, -0.008]
      "radius": 0.056
    - "center": [-0.004, 0.035, 0.151]
      "radius": 0.059
  - link3:
    - "center": [-0.005, 0.002, 0.06]
      "radius": 0.059
    - "center": [-0.012, 0.008, 0.144]
      "radius": 0.055
    - "center": [-0.018, 0.012, 0.197]
      "radius": 0.052
    - "center": [-0.01, 0.005, 0.105]
      "radius": 0.057
  - link4:
    - "center": [-0.018, 0.026, 0.139]
      "radius": 0.06
    - "center": [-0.003, 0.034, 0.039]
      "radius": 0.056
    - "center": [-0.006, 0.038, 0.001]
      "radius": 0.054
    - "center": [-0.013, 0.029, 0.096]
      "radius": 0.058
  - link5:
    - "center": [0.001, -0.003, 0.069]
      "radius": 0.058
    - "center": [0.003, -0.01, 0.169]
      "radius": 0.054
    - "center": [-0.003, -0.006, 0.112]
      "radius": 0.057
    - "center": [-0.0, -0.012, 0.195]
      "radius": 0.052
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.06
  - link6:
    - "center": [0.014, 0.069, 0.107]
      "radius": 0.06
    - "center": [0.002, 0.049, 0.035]
      "radius": 0.059
    - "center": [-0.001, 0.043, -0.005]
      "radius": 0.053
    - "center": [-0.001, 0.067, 0.106]
      "radius": 0.059
  - link7:
    - "center": [-0.005, -0.006, 0.041]
      "radius": 0.05
    - "center": [0.009, 0.002, 0.041]
      "radius": 0.05
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.05
