[package]
version = "1.4.6"
category = "Setup"
title = "Isaac Sim Setup"
description = "This Extension does the setup of the Isaac Sim App"
keywords = ["isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
toggleable = false
writeTarget.kit = true

[dependencies]
"isaacsim.core.version" = {}
"omni.client" = {}
"omni.kit.menu.common" = {}
"omni.kit.quicklayout" = {}
"omni.kit.stage_templates" = {}
"omni.kit.viewport.utility" = {}
"omni.kit.window.property" = {}
"omni.kit.window.title" = {}
"omni.ui" = {}
"omni.usd" = {}

[[python.module]]
name = "isaacsim.app.setup"

[settings]
# Creates a new stage when this extension is enabled so there is something visible in the viewport
isaac.startup.create_new_stage = true 

[settings."filter:platform"."windows-x86_64"]
# Set this to specify the ROS bridge extension to use for windows, blank for no ROS bridge
isaac.startup.ros_bridge_extension = ""

[settings."filter:platform"."linux-x86_64"]
# Set this to specify the ROS bridge extension to use for linux, blank for no ROS bridge
isaac.startup.ros_bridge_extension = ""

[[test]]
dependencies = [
    "isaacsim.asset.browser",            # isaac sim asset browser,
    "isaacsim.asset.gen.omap.ui",            # extension with occupancy map,
    "isaacsim.examples.browser",         # robotics examples,
    "isaacsim.replicator.synthetic_recorder",    # extension with synthetic_recorder,
    "omni.graph.window.action",          # window for action graph,
    "omni.kit.viewport.window",                 # viewport,
    "omni.kit.widget.layers",        # layer window,
    "omni.kit.window.console",           # console,
    "omni.kit.window.content_browser",     # content browser,
    "omni.kit.window.script_editor",     # window with script editor,
    "omni.kit.window.stage",       # Stage,
    "omni.kit.window.toolbar",     # main tool bar,
    "omni.pip.cloud",
    "omni.rtx.settings.core",
    "omni.rtx.settings.core",      # Render Settings window,
    "semantics.schema.editor",            # the extension containing semantics schema editor,
]

args = [
    "--/app/asyncRendering=0",
    "--/app/asyncRenderingLowLatency=0",
    "--/app/fastShutdown=1",
    "--/app/file/ignoreUnsavedOnExit=1",
    "--/app/hydraEngine/waitIdle=0",
    "--/app/renderer/skipWhileMinimized=0",
    "--/app/renderer/sleepMsOnFocus=0",
    "--/app/renderer/sleepMsOutOfFocus=0",
    "--/app/settings/fabricDefaultStageFrameHistoryCount=3",
    "--/app/settings/persistent=0",
    "--/app/viewport/createCameraModelRep=0",
    "--/crashreporter/skipOldDumpUpload=1",
    "--/exts/omni.usd/locking/onClose=0",
    "--/omni/kit/plugin/syncUsdLoads=1",
    "--/omni/replicator/asyncRendering=0",
    '--/persistent/app/stage/upAxis="Z"',
    "--/persistent/app/viewport/defaults/tickRate=120",
    "--/persistent/app/viewport/displayOptions=31951",
    "--/persistent/omni/replicator/captureOnPlay=1",
    "--/persistent/omnigraph/updateToUsd=0",
    "--/persistent/physics/visualizationDisplayJoints=0",
    "--/persistent/renderer/startupMessageDisplayed=1",
    "--/persistent/simulation/defaultMetersPerUnit=1.0",
    "--/persistent/simulation/minFrameRate=15",
    "--/renderer/multiGpu/autoEnable=0",
    "--/renderer/multiGpu/enabled=0",
    "--/rtx-transient/dlssg/enabled=0",
    "--/'rtx-transient'/resourcemanager/enableTextureStreaming=1",
    "--/rtx/descriptorSets=360000",
    "--/rtx/hydra/enableSemanticSchema=1",
    "--/rtx/hydra/materialSyncLoads=1",
    "--/rtx/materialDb/syncLoads=1",
    "--/rtx/newDenoiser/enabled=1",
    "--/rtx/reservedDescriptors=900000",
    "--vulkan",
    "--/app/useFabricSceneDelegate=true",
    "--/app/player/useFixedTimeStepping=false",
    "--/app/runLoops/main/rateLimitEnabled=false",
    "--/app/runLoops/main/manualModeEnabled=true",
]

stdoutFailPatterns.exclude = [
    "*[isaacsim.ros2.bridge.plugin]*",
    "*[isaacsim.ros2.bridge.scripts.extension]*",
    "*[Error] [carb.glinterop.plugin] GLInteropContext::init: carb::windowing is not available*",
]
