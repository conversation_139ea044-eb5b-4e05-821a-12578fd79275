# SPDX-FileCopyrightText: Copyright (c) 2018-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import gc

import omni

from .. import _surface_gripper

EXTENSION_NAME = "Surface Gripper"


class Extension(omni.ext.IExt):
    def on_startup(self, ext_id: str):
        self._sg = _surface_gripper.acquire_surface_gripper_interface()

    def on_shutdown(self):
        _surface_gripper.release_surface_gripper_interface(self._sg)

        gc.collect()
