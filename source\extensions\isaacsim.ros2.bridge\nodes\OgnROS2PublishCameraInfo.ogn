{"ROS2PublishCameraInfo": {"version": 2, "icon": "icons/isaac-sim.svg", "description": ["This node publishes camera info as a ROS2 CameraInfo message"], "metadata": {"uiName": "ROS2 Publish Camera Info"}, "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2:publisher", "inputs": {"execIn": {"type": "execution", "description": "The input execution port"}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0}, "nodeNamespace": {"type": "string", "description": "Namespace of ROS2 Node, prepends any published/subscribed topic by the node namespace", "default": ""}, "frameId": {"type": "string", "description": "FrameId for ROS2 message", "default": "sim_camera"}, "topicName": {"type": "string", "description": "Name of ROS2 Topic", "default": "camera_info"}, "qosProfile": {"type": "string", "description": "QoS profile config", "default": ""}, "queueSize": {"type": "uint64", "description": "The number of messages to queue up before throwing some away, in case messages are collected faster than they can be sent. Only honored if 'history' QoS policy was set to 'keep last'. This setting can be overwritten by qosProfile input.", "default": 10}, "timeStamp": {"type": "double", "description": "ROS2 Timestamp in seconds", "uiName": "Timestamp", "default": 0.0}, "width": {"type": "uint", "description": "Width for output image"}, "height": {"type": "uint", "description": "Height for output image"}, "projectionType": {"type": "token", "description": "projection type"}, "k": {"type": "double[]", "description": "3x3 Intrinsic camera matrix for the raw (distorted) images. Projects 3D points in the camera coordinate frame to 2D pixel coordinates using the focal lengths and principal point."}, "r": {"type": "double[]", "description": "3x3 Rectification matrix (stereo cameras only). A rotation matrix aligning the camera coordinate system to the ideal stereo image plane so that epipolar lines in both stereo images are parallel."}, "p": {"type": "double[]", "description": "3x4 Rectification matrix (stereo cameras only). A rotation matrix aligning the camera coordinate system to the ideal stereo image plane so that epipolar lines in both stereo images are parallel."}, "physicalDistortionModel": {"type": "token", "description": "physical distortion model used for approximation, if blank projectionType is used"}, "physicalDistortionCoefficients": {"type": "float[]", "description": "physical distortion model used for approximation, physicalDistortionModel must be set to use these coefficients"}}}}