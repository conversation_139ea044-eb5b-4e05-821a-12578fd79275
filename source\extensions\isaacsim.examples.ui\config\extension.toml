[package]
version = "1.1.8"
category = "Simulation"
title = "Isaac Sim UI Example"
description = "Example with all the Core UI Elements in Isaac Sim"
keywords = ["isaac", "example", "ui",]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.examples.browser" = {}
"isaacsim.gui.components" = {}
"omni.kit.uiapp" = {}

[[python.module]]
name = "isaacsim.examples.ui"

[[python.module]]
name = "isaacsim.examples.ui.tests"
