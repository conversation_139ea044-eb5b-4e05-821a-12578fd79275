# Changelog
## [1.6.2] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.6.1] - 2025-05-12
### Changed
- Update imageio==2.37.0, scipy==1.15.3, pyyaml==6.0.2, opencv-python-headless==*********

## [1.6.0] - 2025-05-12
### Changed
- Added trimesh==4.5.1, rtree==1.3.0

## [1.5.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.5.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.5.2] - 2025-03-04
### Changed
- Update to kit 107.1 and fix build issues

## [1.5.1] - 2025-01-30
### Changed
- Updated to latest release 4.5 changes

## [1.5.0] - 2025-01-15
### Changed
- Update to Kit 107.x, Python 3.11

## [1.4.5] - 2025-01-14
### Changed
- switch to opencv-python-headless==********

## [1.4.4] - 2025-01-13
### Changed
- Update extension description and add extension specific test settings

## [1.4.3] - 2025-01-11
### Changed
- Make this extension kit version specific

## [1.4.2] - 2024-12-01
### Changed
- Make this extension python version specific

## [1.4.1] - 2024-10-28
### Changed
- Remove test imports from runtime

## [1.4.0] - 2024-07-05
### Changed
- switch to opencv-python==********

## [1.3.1] - 2024-05-16
### Fixed
- Missing windows dlls

## [1.3.0] - 2024-05-06
### Added
- opencv-python-headless==********

## [1.2.0] - 2023-08-03
### Changed
- Update pyyaml to 6.0.1

### Removed
- Removed boto3 and s3transfer into omni.pip.cloud

## [1.1.1] - 2023-07-05
### Changed
- Removed unused omni.kit.pipapi dependency

## [1.1.0] - 2023-05-15
### Changed
- Added s3transfer
- update boto3 to 1.26.63
- update s3transfer to 0.6.1

## [1.0.1] - 2023-05-10
### Changed
- Remove kit-sdk target restriction

## [1.0.0] - 2023-05-04
### Added
- Initial version of Compute Pip Archive
