# Changelog
## [2.1.7] - 2025-06-04
### Fixed
- Grids are available while paused, if Grid Mode is ON

## [2.1.6] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [2.1.5] - 2025-05-27
### Fixed
- Fixed typo in extension title

## [2.1.4] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.1.3] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [2.1.2] - 2025-05-10
### Changed
- Enable FSD in test settings

## [2.1.1] - 2025-05-03
### Added
- Add missing omni.timeline dependency

## [2.1.0] - 2025-05-02
### Added
- Add setting to disable legacy gizmos during runtime

### Changed
- Eco mode is always enabled on stop

## [2.0.5] - 2025-04-09
### Changed
- Update all test args to be consistent

## [2.0.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.0.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-04-10
### Changed
- Extension renamed to isaacsim.core.throttling

## [1.1.0] - 2024-04-10
### Added
- Added on stop play callback to enable/disable eco mode. Pressing play will disable, pressing stop will enable.

## [1.0.0] - 2022-11-27
### Added
- Added first version of extension.
