{"IsaacRealTimeFactor": {"version": 1, "icon": "icons/isaac-sim.svg", "description": ["This node calculates the real time factor (RTF)"], "metadata": {"uiName": "Isaac Real Time Factor"}, "categoryDefinitions": "config/CategoryDefinition.json", "categories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": {"rtf": {"type": "float", "description": "RTF value calculated per frame", "uiName": "Real Time Factor"}}}}