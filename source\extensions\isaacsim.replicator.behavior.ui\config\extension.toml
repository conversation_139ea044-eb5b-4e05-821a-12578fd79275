[package]
version = "1.0.5"
category = "SyntheticData"
title = "Isaac Sim Replicator Behavior Scripts UI"
description = "UI components for the Replicator Behavior Scripts exntension. It provides the UI Widget for the Property Panel for configuring the exposed variables of the scripts."
keywords = ["isaac", "behavior scripts", "replicator", "ui"]
changelog = "docs/CHANGELOG.md"
readme  = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.replicator.behavior" = {}
"omni.kit.property.usd" = {}
"omni.kit.window.property" = {}
"omni.ui" = {}

[[python.module]]
name = "isaacsim.replicator.behavior.ui"

[[test]]
dependencies = [
    "omni.kit.test"
]
