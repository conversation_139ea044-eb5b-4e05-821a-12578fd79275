# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON>la will only use these joints to control the robot position.
cspace:
    - shoulder_pan_joint
    - shoulder_lift_joint
    - elbow_joint
    - wrist_1_joint
    - wrist_2_joint
    - wrist_3_joint
default_q: [
    0.0,-1.0,0.9,0.0,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - wrist_1_link:
    - "center": [-0.0, 0.027, -0.002]
      "radius": 0.041
    - "center": [-0.003, -0.032, 0.001]
      "radius": 0.037
    - "center": [-0.002, -0.003, -0.0]
      "radius": 0.039
    - "center": [-0.0, 0.0, -0.058]
      "radius": 0.045
  - wrist_2_link:
    - "center": [-0.0, 0.0, -0.015]
      "radius": 0.041
    - "center": [0.0, 0.018, 0.001]
      "radius": 0.039
    - "center": [0.0, -0.033, -0.001]
      "radius": 0.04
  - wrist_3_link:
    - "center": [-0.001, 0.002, -0.025]
      "radius": 0.038
  - shoulder_link:
    - "center": [-0.006, -0.012, 0.027]
      "radius": 0.059
    - "center": [0.011, 0.007, -0.048]
      "radius": 0.055
    - "center": [0.018, -0.031, -0.001]
      "radius": 0.05
  - upper_arm_link:
    - "center": [-0.183, 0.0, 0.15]
      "radius": 0.069
    - "center": [-0.344, 0.0, 0.126]
      "radius": 0.069
    - "center": [-0.03, 0.0, 0.146]
      "radius": 0.069
    - "center": [-0.425, 0.0, 0.142]
      "radius": 0.069
    - "center": [-0.27, -0.001, 0.151]
      "radius": 0.069
    - "center": [-0.11, 0.0, 0.137]
      "radius": 0.069
    - "center": [0.001, -0.0, 0.135]
      "radius": 0.068
    - "center": [-0.226, -0.001, 0.123]
      "radius": 0.068
    - "center": [-0.426, -0.001, 0.118]
      "radius": 0.067
    - "center": [-0.359, 0.005, 0.155]
      "radius": 0.064
    - "center": [-0.307, 0.0, 0.121]
      "radius": 0.069
    - "center": [-0.156, -0.0, 0.129]
      "radius": 0.069
    - "center": [-0.123, -0.001, 0.151]
      "radius": 0.068
    - "center": [-0.064, 0.005, 0.125]
      "radius": 0.064
  - forearm_link:
    - "center": [-0.005, 0.001, 0.048]
      "radius": 0.059
    - "center": [-0.317, 0.0, -0.001]
      "radius": 0.053
    - "center": [-0.386, -0.0, 0.021]
      "radius": 0.049
    - "center": [-0.01, 0.001, 0.018]
      "radius": 0.052
    - "center": [-0.268, 0.0, -0.001]
      "radius": 0.054
    - "center": [-0.034, -0.0, 0.014]
      "radius": 0.058
    - "center": [-0.393, 0.001, -0.019]
      "radius": 0.047
    - "center": [-0.326, -0.009, 0.028]
      "radius": 0.042
    - "center": [-0.342, 0.0, -0.008]
      "radius": 0.051
    - "center": [0.031, -0.009, 0.037]
      "radius": 0.033
    - "center": [-0.222, 0.0, 0.002]
      "radius": 0.055
    - "center": [-0.176, 0.0, 0.005]
      "radius": 0.055
    - "center": [-0.129, 0.0, 0.008]
      "radius": 0.056
    - "center": [-0.082, 0.0, 0.011]
      "radius": 0.057
