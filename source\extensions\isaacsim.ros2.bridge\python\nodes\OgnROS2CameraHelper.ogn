{"ROS2CameraHelper": {"version": 2, "icon": "icons/isaac-sim.svg", "description": "This node handles automation of the camera sensor pipeline", "language": "Python", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2", "metadata": {"uiName": "ROS2 Camera Helper"}, "inputs": {"execIn": {"type": "execution", "description": "Triggering this causes the sensor pipeline to be generated"}, "enabled": {"type": "bool", "description": "True to enable the camera helper, False to disable", "default": true}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0}, "nodeNamespace": {"type": "string", "description": "Namespace of ROS2 Node, prepends any published/subscribed topic by the node namespace", "default": ""}, "frameId": {"type": "string", "description": "FrameId for ROS2 message, the nodeNamespace will not be prefixed to the frame id", "default": "sim_camera"}, "topicName": {"type": "string", "description": "Topic name for sensor data", "default": "rgb"}, "qosProfile": {"type": "string", "description": "QoS profile config", "default": ""}, "queueSize": {"type": "uint64", "description": "The number of messages to queue up before throwing some away, in case messages are collected faster than they can be sent. Only honored if 'history' QoS policy was set to 'keep last'. This setting can be overwritten by qosProfile input.", "default": 10}, "viewport": {"type": "token", "description": "DEPRECATED, use renderProductPath. Name of the desired viewport to publish"}, "renderProductPath": {"type": "token", "description": "Path of the render product used for capturing data"}, "type": {"type": "token", "description": "type", "metadata": {"allowedTokens": {"rgb": "rgb", "depth": "depth", "depth_pcl": "depth_pcl", "instance_segmentation": "instance_segmentation", "semantic_segmentation": "semantic_segmentation", "bbox_2d_tight": "bbox_2d_tight", "bbox_2d_loose": "bbox_2d_loose", "bbox_3d": "bbox_3d"}}, "default": "rgb"}, "enableSemanticLabels": {"type": "bool", "description": "Enable publishing of semantic labels, applies only to instance_segmentation, semantic_segmentation, bbox_2d_tight, bbox_2d_loose, bbox_3d"}, "semanticLabelsTopicName": {"type": "string", "description": "Topic name used for publishing semantic labels, applies only to instance_segmentation, semantic_segmentation, bbox_2d_tight, bbox_2d_loose, bbox_3d", "default": "semantic_labels"}, "stereoOffset": {"type": "float[2]", "description": "Stereo offset is the baseline between cameras in x and y component of the image plane in meters. (Tx, Ty is calculated using x and y component of StereoOffset value. i.e., Tx=fx*stereoOffset.X, Ty=fy*stereoOffset.Y). Used when publishing to the camera info topic", "default": [0, 0]}, "resetSimulationTimeOnStop": {"type": "bool", "description": "If True the simulation time will reset when stop is pressed, False means time increases monotonically. This setting is ignored if useSystemTime is enabled.", "uiName": "Reset Simulation Time On Stop", "default": false}, "useSystemTime": {"type": "bool", "description": "If True, system timestamp will be included in messages. If False, simulation timestamp will be included in messages", "default": false}, "frameSkipCount": {"type": "uint", "description": "Specifies the number of simulation frames to skip between each message publish. (e.g. Set to 0 to publish each frame. Set 1 to publish every other frame)", "default": 0}}}}