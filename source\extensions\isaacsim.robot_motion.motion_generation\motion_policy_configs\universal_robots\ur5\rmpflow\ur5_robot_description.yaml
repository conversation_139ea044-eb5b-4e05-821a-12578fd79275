# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON>la will only use these joints to control the robot position.
cspace:
    - shoulder_pan_joint
    - shoulder_lift_joint
    - elbow_joint
    - wrist_1_joint
    - wrist_2_joint
    - wrist_3_joint
default_q: [
    0.0,-1.0,0.9,0.0,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - wrist_1_link:
    - "center": [-0.0, 0.027, -0.002]
      "radius": 0.041
    - "center": [-0.003, -0.032, 0.001]
      "radius": 0.037
    - "center": [-0.002, -0.003, -0.0]
      "radius": 0.039
  - wrist_2_link:
    - "center": [-0.0, 0.0, -0.015]
      "radius": 0.041
    - "center": [0.0, 0.018, 0.001]
      "radius": 0.039
    - "center": [0.0, -0.033, -0.001]
      "radius": 0.04
  - wrist_3_link:
    - "center": [0.0, 0.002, -0.025]
      "radius": 0.035
  - shoulder_link:
    - "center": [-0.006, -0.012, 0.027]
      "radius": 0.059
    - "center": [0.011, 0.007, -0.048]
      "radius": 0.055
    - "center": [0.018, -0.031, -0.001]
      "radius": 0.05
  - upper_arm_link:
    - "center": [-0.41, -0.001, 0.121]
      "radius": 0.06
    - "center": [-0.201, 0.0, 0.136]
      "radius": 0.059
    - "center": [-0.016, 0.0, 0.121]
      "radius": 0.06
    - "center": [-0.306, -0.0, 0.135]
      "radius": 0.059
    - "center": [-0.122, -0.0, 0.135]
      "radius": 0.059
    - "center": [-0.006, 0.004, 0.162]
      "radius": 0.052
    - "center": [-0.272, -0.0, 0.136]
      "radius": 0.059
    - "center": [-0.429, 0.006, 0.173]
      "radius": 0.052
    - "center": [-0.388, -0.015, 0.15]
      "radius": 0.043
    - "center": [-0.028, -0.02, 0.142]
      "radius": 0.047
    - "center": [-0.152, 0.0, 0.136]
      "radius": 0.059
    - "center": [-0.387, 0.025, 0.145]
      "radius": 0.042
    - "center": [-0.236, 0.0, 0.136]
      "radius": 0.059
    - "center": [-0.35, 0.013, 0.14]
      "radius": 0.05
    - "center": [-0.062, 0.002, 0.149]
      "radius": 0.055
  - forearm_link:
    - "center": [-0.021, 0.0, 0.026]
      "radius": 0.053
    - "center": [-0.177, 0.0, 0.016]
      "radius": 0.047
    - "center": [-0.27, -0.0, 0.017]
      "radius": 0.047
    - "center": [-0.392, 0.003, 0.039]
      "radius": 0.044
    - "center": [-0.114, 0.002, 0.019]
      "radius": 0.044
    - "center": [-0.31, 0.0, 0.017]
      "radius": 0.046
    - "center": [0.02, -0.001, 0.039]
      "radius": 0.042
    - "center": [-0.202, 0.001, 0.017]
      "radius": 0.046
    - "center": [-0.392, 0.003, -0.006]
      "radius": 0.04
    - "center": [-0.035, 0.0, 0.018]
      "radius": 0.049
    - "center": [0.008, 0.02, 0.039]
      "radius": 0.041
    - "center": [0.01, -0.029, 0.045]
      "radius": 0.035
    - "center": [-0.134, -0.001, 0.017]
      "radius": 0.046
    - "center": [-0.252, 0.0, 0.016]
      "radius": 0.047
    - "center": [-0.075, 0.001, 0.019]
      "radius": 0.046
    - "center": [-0.348, 0.002, 0.022]
      "radius": 0.045
