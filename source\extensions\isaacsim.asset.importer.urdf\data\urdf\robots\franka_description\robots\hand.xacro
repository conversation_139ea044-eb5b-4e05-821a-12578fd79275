<?xml version="1.0" encoding="utf-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="hand">
  <xacro:macro name="hand" params="connected_to:='' ns:='' rpy:='0 0 0' xyz:='0 0 0' ">
    <xacro:unless value="${connected_to == ''}">
      <joint name="${ns}_hand_joint" type="fixed">
        <parent link="${connected_to}"/>
        <child link="${ns}_hand"/>
        <origin xyz="${xyz}" rpy="${rpy}"/>
      </joint>
    </xacro:unless>
    <link name="${ns}_hand">
      <visual>
        <geometry>
          <mesh filename="package://franka_description/meshes/visual/hand.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://franka_description/meshes/collision/hand.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="0.5583304799"/>
        <inertia ixx="0.0023394448" ixy="0.0" ixz="0.0" iyy="0.0005782786" iyz="0" izz="0.0021310296"/>
      </inertial>
    </link>
    <link name="${ns}_leftfinger">
      <visual>
        <geometry>
          <mesh filename="package://franka_description/meshes/visual/finger.dae"/>
        </geometry>
      </visual>
      <collision>
        <geometry>
          <mesh filename="package://franka_description/meshes/collision/finger.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="0.0140552232"/>
        <inertia ixx="4.20413082650939E-06" ixy="0.0" ixz="0.0" iyy="3.90263687466755E-06" iyz="0" izz="1.33474964199095E-06"/>
      </inertial>
    </link>
    <link name="${ns}_rightfinger">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <mesh filename="package://franka_description/meshes/visual/finger.dae"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <mesh filename="package://franka_description/meshes/collision/finger.stl"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <mass value="0.0140552232"/>
        <inertia ixx="4.20413082650939E-06" ixy="0.0" ixz="0.0" iyy="3.90263687466755E-06" iyz="0" izz="1.33474964199095E-06"/>
      </inertial>
    </link>
    <joint name="${ns}_finger_joint1" type="prismatic">
      <parent link="${ns}_hand"/>
      <child link="${ns}_leftfinger"/>
      <origin xyz="0 0 0.0584" rpy="0 0 0"/>
      <axis xyz="0 1 0"/>
      <limit effort="20" lower="0.0" upper="0.04" velocity="0.2"/>
    </joint>
    <joint name="${ns}_finger_joint2" type="prismatic">
      <parent link="${ns}_hand"/>
      <child link="${ns}_rightfinger"/>
      <origin xyz="0 0 0.0584" rpy="0 0 0"/>
      <axis xyz="0 -1 0"/>
      <limit effort="20" lower="0.0" upper="0.04" velocity="0.2"/>
      <mimic joint="${ns}_finger_joint1" />
    </joint>
  </xacro:macro>
</robot>
