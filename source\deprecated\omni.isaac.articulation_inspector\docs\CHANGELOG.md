# Changelog
## [0.1.16] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.1.15] - 2025-05-07
### Changed
- switch to omni.physics interface

## [0.1.14] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [0.1.13] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [0.1.12] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [0.1.11] - 2024-11-22
### Fixed
- menu location

## [0.1.10] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [0.1.9] - 2024-04-24
### Deprecated
- Add Deprecation Warning

## [0.1.8] - 2024-03-14
### Fixed
- Fixed Articulation Selection DropDown menu due to change in behavior of Core function get_prim_object_type().

## [0.1.7] - 2024-03-07
### Changed
- Removed the usage of the deprecated dynamic_control extension

## [0.1.6] - 2023-11-13
### Fixed
- Updated documentation link

## [0.1.5] - 2023-08-08
### Fixed
- Switch from ui.Window to ScrollingWindow wrapper for extension because scrolling was broken

## [0.1.4] - 2023-08-08
### Fixed
- Error on selecting Articulation because get_joint_efforts() is deprecated.

## [0.1.3] - 2023-01-06
### Fixed
- onclick_fn warning when creating UI

## [0.1.2] - 2022-02-09
### Removed
- Joint Animation UI
- Moved Gain Tuning UI to separate extension

## [0.1.1] - 2022-01-25
### Added
- New Selection UI and bug fixes

## [0.1.0] - 2021-12-10
### Added
- Initial version
