{"OgnIntervalFiltering": {"version": 1, "language": "python", "categories": {"Replicator": "Write Attribute"}, "description": "Outputs indices if their frame count is a multiple of the interval", "metadata": {"uiName": "Interval Filter"}, "inputs": {"interval": {"description": ["randomization will take place once every `interval` frames"], "type": "int"}, "frameCounts": {"description": ["the current frame number for every environment"], "type": "int[]"}, "indices": {"description": ["a list of indices to use in case of ignoring interval"], "type": "int[]"}, "ignoreInterval": {"description": ["if true, will just pass indices to downstream node"], "type": "bool", "default": false}, "execIn": {"description": ["exec"], "type": "execution"}}, "outputs": {"execOut": {"description": ["exec"], "type": "execution"}, "indices": {"description": ["the indices that are at the interval and need to be randomized"], "type": "int[]"}, "on_reset": {"description": ["indicates whether an on_reset context triggered the execution"], "type": "bool"}}}}