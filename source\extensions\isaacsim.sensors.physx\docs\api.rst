API
===

Python API
----------

.. Summary

.. currentmodule:: isaacsim.sensors.physx

.. rubric:: *Commands*
.. autosummary::
    :nosignatures:

    RangeSensorCreatePrim
    RangeSensorCreateLidar
    RangeSensorCreateGeneric
    IsaacSensorCreateLightBeamSensor

.. rubric:: *Sensors*
.. autosummary::
    :nosignatures:

    ProximitySensor
    RotatingLidarPhysX

|

.. API

Commands
^^^^^^^^

.. autoclass:: isaacsim.sensors.physx.RangeSensorCreatePrim
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
    :exclude-members: do, undo

.. autoclass:: isaacsim.sensors.physx.RangeSensorCreateLidar
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
    :exclude-members: do, undo

.. autoclass:: isaacsim.sensors.physx.RangeSensorCreateGeneric
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
    :exclude-members: do, undo

.. autoclass:: isaacsim.sensors.physx.IsaacSensorCreateLightBeamSensor
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
    :exclude-members: do, undo

|

Sensors
^^^^^^^

.. autoclass:: isaacsim.sensors.physx.ProximitySensor
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.sensors.physx.RotatingLidarPhysX
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
