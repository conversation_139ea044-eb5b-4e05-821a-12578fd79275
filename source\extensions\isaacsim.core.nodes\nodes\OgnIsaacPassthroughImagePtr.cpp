// SPDX-FileCopyrightText: Copyright (c) 2020-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
// SPDX-License-Identifier: Apache-2.0
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "OgnIsaacPassthroughImagePtrDatabase.h"

#include <cmath>
#include <string>

namespace isaacsim
{
namespace core
{
namespace nodes
{

/**
 * @brief a node that passes trough pointer without changing it
 *
 */
class OgnIsaacPassthroughImagePtr
{

public:
    static bool compute(OgnIsaacPassthroughImagePtrDatabase& db)
    {
        db.outputs.dataPtr() = db.inputs.dataPtr();
        db.outputs.cudaDeviceIndex() = db.inputs.cudaDeviceIndex();
        db.outputs.width() = db.inputs.width();
        db.outputs.height() = db.inputs.height();
        db.outputs.bufferSize() = db.inputs.bufferSize();
        db.outputs.format() = db.inputs.format();
        db.outputs.execOut() = kExecutionAttributeStateEnabled;
        return true;
    }

private:
};
REGISTER_OGN_NODE()
}
}
}
