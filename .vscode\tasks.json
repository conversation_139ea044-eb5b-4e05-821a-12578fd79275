{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "./build.sh --debug-only",
            "windows": {
                "command": ".\\build.bat --debug-only"
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": ["relative", "${workspaceRoot}/_compiler/vs2017/carb"],
                "pattern": {
                    "regexp": "^(.*):(\\d+): \\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "severity": 3,
                    "message": 4
                }
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "always",
                "panel": "dedicated",
                "clear": true
            }
        },
        {
            "label": "setup_python_env",
            "type": "shell",
            "linux": {
                "command": "export CARB_APP_PATH=${workspaceFolder}/_build/linux-x86_64/release/kit && export ISAAC_PATH=${workspaceFolder}/_build/linux-x86_64/release && export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:${workspaceFolder}/_build/linux-x86_64/release/exts/omni.isaac.ros2_bridge/humble/lib && export EXP_PATH=${workspaceFolder}/_build/linux-x86_64/release/apps && source ${workspaceFolder}/_build/linux-x86_64/release/setup_python_env.sh && printenv >${workspaceFolder}/.vscode/.python_samples.env"
            }
        }
    ]
}
