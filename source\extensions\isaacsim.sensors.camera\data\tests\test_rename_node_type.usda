#usda 1.0
(
    customLayerData = {
        dictionary cameraSettings = {
            dictionary Front = {
                double3 position = (5, 0, 0)
                double radius = 5
            }
            dictionary Perspective = {
                double3 position = (5, 5, 5)
                double3 target = (-3.978038431995401e-8, -3.978038254359717e-8, 7.956076863990802e-8)
            }
            dictionary Right = {
                double3 position = (0, -5, 0)
                double radius = 5
            }
            dictionary Top = {
                double3 position = (0, 0, 5)
                double radius = 5
            }
            string boundCamera = "/OmniverseKit_Persp"
        }
        dictionary omni_layer = {
            dictionary muteness = {
            }
        }
        dictionary renderSettings = {
        }
    }
    defaultPrim = "World"
    endTimeCode = 100
    metersPerUnit = 1
    startTimeCode = 0
    timeCodesPerSecond = 24
    upAxis = "Z"
)

over "Render" (
    hide_in_stage_window = true
)
{
}

def Xform "World"
{
    def OmniGraph "ActionGraph"
    {
        token evaluationMode = "Automatic"
        token evaluator:type = "execution"
        token fabricCacheBacking = "Shared"
        int2 fileFormatVersion = (1, 5)
        token pipelineStage = "pipelineStageSimulation"

        def OmniGraphNode "isaac_read_imu_node" (
            prepend apiSchemas = ["NodeGraphNodeAPI"]
        )
        {
            custom uint inputs:execIn
            custom rel inputs:imuPrim
            token node:type = "omni.isaac.isaac_sensor.IsaacReadIMU"
            int node:typeVersion = 1
            custom vector3d outputs:angVel = (0, 0, 0)
            custom uint outputs:execOut (
                customData = {
                    bool isExecution = 1
                }
            )
            custom vector3d outputs:linAcc = (0, 0, 0)
            custom quatd outputs:orientation = (1, 0, 0, 0)
            uniform token ui:nodegraph:node:expansionState = "open"
            uniform float2 ui:nodegraph:node:pos = (235, 145)
        }

        def OmniGraphNode "isaac_read_contact_sensor" (
            prepend apiSchemas = ["NodeGraphNodeAPI"]
        )
        {
            custom rel inputs:csPrim
            custom uint inputs:execIn
            token node:type = "omni.isaac.isaac_sensor.IsaacReadContactSensor"
            int node:typeVersion = 1
            custom uint outputs:execOut (
                customData = {
                    bool isExecution = 1
                }
            )
            custom bool outputs:inContact = 0
            custom float outputs:value = 0
            uniform token ui:nodegraph:node:expansionState = "open"
            uniform float2 ui:nodegraph:node:pos = (231, -21)
        }
    }
}

def Scope "Environment"
{
    def DistantLight "defaultLight" (
        prepend apiSchemas = ["ShapingAPI"]
    )
    {
        float angle = 1
        float intensity = 3000
        float shaping:cone:angle = 180
        float shaping:cone:softness
        float shaping:focus
        color3f shaping:focusTint
        asset shaping:ies:file
        quatd xformOp:orient = (0.6532814824381883, 0.2705980500730985, 0.27059805007309845, 0.6532814824381882)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }
}

