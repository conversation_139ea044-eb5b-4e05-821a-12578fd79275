#usda 1.0
(
    customLayerData = {
        dictionary cameraSettings = {
            dictionary Front = {
                double3 position = (0, 0, 50000)
                double radius = 500
            }
            dictionary Perspective = {
                double3 position = (674.8760548846408, 674.8760507106591, 674.8760548846409)
                double3 target = (-0.000003978038307650422, 0.000007956077070048195, -0.000003978038080276747)
            }
            dictionary Right = {
                double3 position = (-50000, 0, 0)
                double radius = 500
            }
            dictionary Top = {
                double3 position = (0, 50000, 0)
                double radius = 500
            }
            string boundCamera = "/OmniverseKit_Persp"
        }
        dictionary omni_layer = {
            string authoring_layer = "./cylinder.usd"
            dictionary locked = {
            }
            dictionary muteness = {
            }
        }
        dictionary renderSettings = {
            float3 "rtx:debugView:pixelDebug:textColor" = (0, 1e18, 0)
            float3 "rtx:fog:fogColor" = (0.75, 0.75, 0.75)
            float3 "rtx:index:regionOfInterestMax" = (0, 0, 0)
            float3 "rtx:index:regionOfInterestMin" = (0, 0, 0)
            float3 "rtx:iray:environment_dome_ground_position" = (0, 0, 0)
            float3 "rtx:iray:environment_dome_ground_reflectivity" = (0, 0, 0)
            float3 "rtx:iray:environment_dome_rotation_axis" = (3.4028235e38, 3.4028235e38, 3.4028235e38)
            float3 "rtx:post:backgroundZeroAlpha:backgroundDefaultColor" = (0, 0, 0)
            float3 "rtx:post:colorcorr:contrast" = (1, 1, 1)
            float3 "rtx:post:colorcorr:gain" = (1, 1, 1)
            float3 "rtx:post:colorcorr:gamma" = (1, 1, 1)
            float3 "rtx:post:colorcorr:offset" = (0, 0, 0)
            float3 "rtx:post:colorcorr:saturation" = (1, 1, 1)
            float3 "rtx:post:colorgrad:blackpoint" = (0, 0, 0)
            float3 "rtx:post:colorgrad:contrast" = (1, 1, 1)
            float3 "rtx:post:colorgrad:gain" = (1, 1, 1)
            float3 "rtx:post:colorgrad:gamma" = (1, 1, 1)
            float3 "rtx:post:colorgrad:lift" = (0, 0, 0)
            float3 "rtx:post:colorgrad:multiply" = (1, 1, 1)
            float3 "rtx:post:colorgrad:offset" = (0, 0, 0)
            float3 "rtx:post:colorgrad:whitepoint" = (1, 1, 1)
            float3 "rtx:post:lensDistortion:lensFocalLengthArray" = (10, 30, 50)
            float3 "rtx:post:lensFlares:anisoFlareFalloffX" = (450, 475, 500)
            float3 "rtx:post:lensFlares:anisoFlareFalloffY" = (10, 10, 10)
            float3 "rtx:post:lensFlares:cutoffPoint" = (2, 2, 2)
            float3 "rtx:post:lensFlares:haloFlareFalloff" = (10, 10, 10)
            float3 "rtx:post:lensFlares:haloFlareRadius" = (75, 75, 75)
            float3 "rtx:post:lensFlares:isotropicFlareFalloff" = (50, 50, 50)
            float3 "rtx:post:tonemap:whitepoint" = (1, 1, 1)
            float3 "rtx:raytracing:indexdirect:svoBrickSize" = (32, 32, 32)
            float3 "rtx:raytracing:inscattering:singleScatteringAlbedo" = (0.9, 0.9, 0.9)
            float3 "rtx:raytracing:inscattering:transmittanceColor" = (0.5, 0.5, 0.5)
            float3 "rtx:sceneDb:ambientLightColor" = (0.1, 0.1, 0.1)
        }
    }
    defaultPrim = "World"
    endTimeCode = 100
    metersPerUnit = 0.01
    startTimeCode = 0
    timeCodesPerSecond = 60
    upAxis = "Y"
)

def Xform "World"
{
    def Mesh "Cylinder"
    {
        float3[] extent = [(-50, -50, -50), (50, 50, 50)]
        int[] faceVertexCounts = [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
        int[] faceVertexIndices = [0, 32, 33, 1, 1, 33, 34, 2, 2, 34, 35, 3, 3, 35, 36, 4, 4, 36, 37, 5, 5, 37, 38, 6, 6, 38, 39, 7, 7, 39, 40, 8, 8, 40, 41, 9, 9, 41, 42, 10, 10, 42, 43, 11, 11, 43, 44, 12, 12, 44, 45, 13, 13, 45, 46, 14, 14, 46, 47, 15, 15, 47, 48, 16, 16, 48, 49, 17, 17, 49, 50, 18, 18, 50, 51, 19, 19, 51, 52, 20, 20, 52, 53, 21, 21, 53, 54, 22, 22, 54, 55, 23, 23, 55, 56, 24, 24, 56, 57, 25, 25, 57, 58, 26, 26, 58, 59, 27, 27, 59, 60, 28, 28, 60, 61, 29, 29, 61, 62, 30, 30, 62, 63, 31, 31, 63, 32, 0, 0, 1, 64, 1, 2, 64, 2, 3, 64, 3, 4, 64, 4, 5, 64, 5, 6, 64, 6, 7, 64, 7, 8, 64, 8, 9, 64, 9, 10, 64, 10, 11, 64, 11, 12, 64, 12, 13, 64, 13, 14, 64, 14, 15, 64, 15, 16, 64, 16, 17, 64, 17, 18, 64, 18, 19, 64, 19, 20, 64, 20, 21, 64, 21, 22, 64, 22, 23, 64, 23, 24, 64, 24, 25, 64, 25, 26, 64, 26, 27, 64, 27, 28, 64, 28, 29, 64, 29, 30, 64, 30, 31, 64, 31, 0, 64, 32, 65, 33, 33, 65, 34, 34, 65, 35, 35, 65, 36, 36, 65, 37, 37, 65, 38, 38, 65, 39, 39, 65, 40, 40, 65, 41, 41, 65, 42, 42, 65, 43, 43, 65, 44, 44, 65, 45, 45, 65, 46, 46, 65, 47, 47, 65, 48, 48, 65, 49, 49, 65, 50, 50, 65, 51, 51, 65, 52, 52, 65, 53, 53, 65, 54, 54, 65, 55, 55, 65, 56, 56, 65, 57, 57, 65, 58, 58, 65, 59, 59, 65, 60, 60, 65, 61, 61, 65, 62, 62, 65, 63, 63, 65, 32]
        normal3f[] normals = [(50, 0, 0), (50, 0, 0), (49.03926, 0, 9.754517), (49.03926, 0, 9.754517), (49.03926, 0, 9.754517), (49.03926, 0, 9.754517), (46.193974, 0, 19.13417), (46.193974, 0, 19.13417), (46.193974, 0, 19.13417), (46.193974, 0, 19.13417), (41.57348, 0, 27.778513), (41.57348, 0, 27.778513), (41.57348, 0, 27.778513), (41.57348, 0, 27.778513), (35.35534, 0, 35.35534), (35.35534, 0, 35.35534), (35.35534, 0, 35.35534), (35.35534, 0, 35.35534), (27.778513, 0, 41.57348), (27.778513, 0, 41.57348), (27.778513, 0, 41.57348), (27.778513, 0, 41.57348), (19.13417, 0, 46.193974), (19.13417, 0, 46.193974), (19.13417, 0, 46.193974), (19.13417, 0, 46.193974), (9.754517, 0, 49.03926), (9.754517, 0, 49.03926), (9.754517, 0, 49.03926), (9.754517, 0, 49.03926), (3.061617e-15, 0, 50), (3.061617e-15, 0, 50), (3.061617e-15, 0, 50), (3.061617e-15, 0, 50), (-9.754517, 0, 49.03926), (-9.754517, 0, 49.03926), (-9.754517, 0, 49.03926), (-9.754517, 0, 49.03926), (-19.13417, 0, 46.193974), (-19.13417, 0, 46.193974), (-19.13417, 0, 46.193974), (-19.13417, 0, 46.193974), (-27.778513, 0, 41.57348), (-27.778513, 0, 41.57348), (-27.778513, 0, 41.57348), (-27.778513, 0, 41.57348), (-35.35534, 0, 35.35534), (-35.35534, 0, 35.35534), (-35.35534, 0, 35.35534), (-35.35534, 0, 35.35534), (-41.57348, 0, 27.778513), (-41.57348, 0, 27.778513), (-41.57348, 0, 27.778513), (-41.57348, 0, 27.778513), (-46.193974, 0, 19.13417), (-46.193974, 0, 19.13417), (-46.193974, 0, 19.13417), (-46.193974, 0, 19.13417), (-49.03926, 0, 9.754517), (-49.03926, 0, 9.754517), (-49.03926, 0, 9.754517), (-49.03926, 0, 9.754517), (-50, 0, 6.123234e-15), (-50, 0, 6.123234e-15), (-50, 0, 6.123234e-15), (-50, 0, 6.123234e-15), (-49.03926, 0, -9.754517), (-49.03926, 0, -9.754517), (-49.03926, 0, -9.754517), (-49.03926, 0, -9.754517), (-46.193974, 0, -19.13417), (-46.193974, 0, -19.13417), (-46.193974, 0, -19.13417), (-46.193974, 0, -19.13417), (-41.57348, 0, -27.778513), (-41.57348, 0, -27.778513), (-41.57348, 0, -27.778513), (-41.57348, 0, -27.778513), (-35.35534, 0, -35.35534), (-35.35534, 0, -35.35534), (-35.35534, 0, -35.35534), (-35.35534, 0, -35.35534), (-27.778513, 0, -41.57348), (-27.778513, 0, -41.57348), (-27.778513, 0, -41.57348), (-27.778513, 0, -41.57348), (-19.13417, 0, -46.193974), (-19.13417, 0, -46.193974), (-19.13417, 0, -46.193974), (-19.13417, 0, -46.193974), (-9.754517, 0, -49.03926), (-9.754517, 0, -49.03926), (-9.754517, 0, -49.03926), (-9.754517, 0, -49.03926), (-9.184851e-15, 0, -50), (-9.184851e-15, 0, -50), (-9.184851e-15, 0, -50), (-9.184851e-15, 0, -50), (9.754517, 0, -49.03926), (9.754517, 0, -49.03926), (9.754517, 0, -49.03926), (9.754517, 0, -49.03926), (19.13417, 0, -46.193974), (19.13417, 0, -46.193974), (19.13417, 0, -46.193974), (19.13417, 0, -46.193974), (27.778513, 0, -41.57348), (27.778513, 0, -41.57348), (27.778513, 0, -41.57348), (27.778513, 0, -41.57348), (35.35534, 0, -35.35534), (35.35534, 0, -35.35534), (35.35534, 0, -35.35534), (35.35534, 0, -35.35534), (41.57348, 0, -27.778513), (41.57348, 0, -27.778513), (41.57348, 0, -27.778513), (41.57348, 0, -27.778513), (46.193974, 0, -19.13417), (46.193974, 0, -19.13417), (46.193974, 0, -19.13417), (46.193974, 0, -19.13417), (49.03926, 0, -9.754517), (49.03926, 0, -9.754517), (49.03926, 0, -9.754517), (49.03926, 0, -9.754517), (50, 0, 0), (50, 0, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0)] (
            interpolation = "faceVarying"
        )
        point3f[] points = [(50, -50, 0), (49.03926, -50, 9.754517), (46.193974, -50, 19.13417), (41.57348, -50, 27.778513), (35.35534, -50, 35.35534), (27.778513, -50, 41.57348), (19.13417, -50, 46.193974), (9.754517, -50, 49.03926), (3.061617e-15, -50, 50), (-9.754517, -50, 49.03926), (-19.13417, -50, 46.193974), (-27.778513, -50, 41.57348), (-35.35534, -50, 35.35534), (-41.57348, -50, 27.778513), (-46.193974, -50, 19.13417), (-49.03926, -50, 9.754517), (-50, -50, 6.123234e-15), (-49.03926, -50, -9.754517), (-46.193974, -50, -19.13417), (-41.57348, -50, -27.778513), (-35.35534, -50, -35.35534), (-27.778513, -50, -41.57348), (-19.13417, -50, -46.193974), (-9.754517, -50, -49.03926), (-9.184851e-15, -50, -50), (9.754517, -50, -49.03926), (19.13417, -50, -46.193974), (27.778513, -50, -41.57348), (35.35534, -50, -35.35534), (41.57348, -50, -27.778513), (46.193974, -50, -19.13417), (49.03926, -50, -9.754517), (50, 50, 0), (49.03926, 50, 9.754517), (46.193974, 50, 19.13417), (41.57348, 50, 27.778513), (35.35534, 50, 35.35534), (27.778513, 50, 41.57348), (19.13417, 50, 46.193974), (9.754517, 50, 49.03926), (3.061617e-15, 50, 50), (-9.754517, 50, 49.03926), (-19.13417, 50, 46.193974), (-27.778513, 50, 41.57348), (-35.35534, 50, 35.35534), (-41.57348, 50, 27.778513), (-46.193974, 50, 19.13417), (-49.03926, 50, 9.754517), (-50, 50, 6.123234e-15), (-49.03926, 50, -9.754517), (-46.193974, 50, -19.13417), (-41.57348, 50, -27.778513), (-35.35534, 50, -35.35534), (-27.778513, 50, -41.57348), (-19.13417, 50, -46.193974), (-9.754517, 50, -49.03926), (-9.184851e-15, 50, -50), (9.754517, 50, -49.03926), (19.13417, 50, -46.193974), (27.778513, 50, -41.57348), (35.35534, 50, -35.35534), (41.57348, 50, -27.778513), (46.193974, 50, -19.13417), (49.03926, 50, -9.754517), (0, -50, 0), (0, 50, 0)]
        texCoord2f[] primvars:st = [(1, 0), (1, 1), (0.96875, 1), (0.96875, 0), (0.96875, 0), (0.96875, 1), (0.9375, 1), (0.9375, 0), (0.9375, 0), (0.9375, 1), (0.90625, 1), (0.90625, 0), (0.90625, 0), (0.90625, 1), (0.875, 1), (0.875, 0), (0.875, 0), (0.875, 1), (0.84375, 1), (0.84375, 0), (0.84375, 0), (0.84375, 1), (0.8125, 1), (0.8125, 0), (0.8125, 0), (0.8125, 1), (0.78125, 1), (0.78125, 0), (0.78125, 0), (0.78125, 1), (0.75, 1), (0.75, 0), (0.75, 0), (0.75, 1), (0.71875, 1), (0.71875, 0), (0.71875, 0), (0.71875, 1), (0.6875, 1), (0.6875, 0), (0.6875, 0), (0.6875, 1), (0.65625, 1), (0.65625, 0), (0.65625, 0), (0.65625, 1), (0.625, 1), (0.625, 0), (0.625, 0), (0.625, 1), (0.59375, 1), (0.59375, 0), (0.59375, 0), (0.59375, 1), (0.5625, 1), (0.5625, 0), (0.5625, 0), (0.5625, 1), (0.53125, 1), (0.53125, 0), (0.53125, 0), (0.53125, 1), (0.5, 1), (0.5, 0), (0.5, 0), (0.5, 1), (0.46875, 1), (0.46875, 0), (0.46875, 0), (0.46875, 1), (0.4375, 1), (0.4375, 0), (0.4375, 0), (0.4375, 1), (0.40625, 1), (0.40625, 0), (0.40625, 0), (0.40625, 1), (0.375, 1), (0.375, 0), (0.375, 0), (0.375, 1), (0.34375, 1), (0.34375, 0), (0.34375, 0), (0.34375, 1), (0.3125, 1), (0.3125, 0), (0.3125, 0), (0.3125, 1), (0.28125, 1), (0.28125, 0), (0.28125, 0), (0.28125, 1), (0.25, 1), (0.25, 0), (0.25, 0), (0.25, 1), (0.21875, 1), (0.21875, 0), (0.21875, 0), (0.21875, 1), (0.1875, 1), (0.1875, 0), (0.1875, 0), (0.1875, 1), (0.15625, 1), (0.15625, 0), (0.15625, 0), (0.15625, 1), (0.125, 1), (0.125, 0), (0.125, 0), (0.125, 1), (0.09375, 1), (0.09375, 0), (0.09375, 0), (0.09375, 1), (0.0625, 1), (0.0625, 0), (0.0625, 0), (0.0625, 1), (0.03125, 1), (0.03125, 0), (0.03125, 0), (0.03125, 1), (0, 1), (0, 0), (1, 0.5), (0.99039257, 0.59754515), (0.5, 0.5), (0.99039257, 0.59754515), (0.9619397, 0.6913417), (0.5, 0.5), (0.9619397, 0.6913417), (0.91573477, 0.7777851), (0.5, 0.5), (0.91573477, 0.7777851), (0.8535534, 0.8535534), (0.5, 0.5), (0.8535534, 0.8535534), (0.7777851, 0.91573477), (0.5, 0.5), (0.7777851, 0.91573477), (0.6913417, 0.9619397), (0.5, 0.5), (0.6913417, 0.9619397), (0.59754515, 0.99039257), (0.5, 0.5), (0.59754515, 0.99039257), (0.5, 1), (0.5, 0.5), (0.5, 1), (0.40245485, 0.99039257), (0.5, 0.5), (0.40245485, 0.99039257), (0.3086583, 0.9619397), (0.5, 0.5), (0.3086583, 0.9619397), (0.22221488, 0.91573477), (0.5, 0.5), (0.22221488, 0.91573477), (0.14644659, 0.8535534), (0.5, 0.5), (0.14644659, 0.8535534), (0.08426523, 0.7777851), (0.5, 0.5), (0.08426523, 0.7777851), (0.038060308, 0.6913417), (0.5, 0.5), (0.038060308, 0.6913417), (0.009607434, 0.59754515), (0.5, 0.5), (0.009607434, 0.59754515), (0, 0.5), (0.5, 0.5), (0, 0.5), (0.009607434, 0.40245485), (0.5, 0.5), (0.009607434, 0.40245485), (0.038060308, 0.3086583), (0.5, 0.5), (0.038060308, 0.3086583), (0.08426523, 0.22221488), (0.5, 0.5), (0.08426523, 0.22221488), (0.14644659, 0.14644659), (0.5, 0.5), (0.14644659, 0.14644659), (0.22221488, 0.08426523), (0.5, 0.5), (0.22221488, 0.08426523), (0.3086583, 0.038060308), (0.5, 0.5), (0.3086583, 0.038060308), (0.40245485, 0.009607434), (0.5, 0.5), (0.40245485, 0.009607434), (0.5, 0), (0.5, 0.5), (0.5, 0), (0.59754515, 0.009607434), (0.5, 0.5), (0.59754515, 0.009607434), (0.6913417, 0.038060308), (0.5, 0.5), (0.6913417, 0.038060308), (0.7777851, 0.08426523), (0.5, 0.5), (0.7777851, 0.08426523), (0.8535534, 0.14644659), (0.5, 0.5), (0.8535534, 0.14644659), (0.91573477, 0.22221488), (0.5, 0.5), (0.91573477, 0.22221488), (0.9619397, 0.3086583), (0.5, 0.5), (0.9619397, 0.3086583), (0.99039257, 0.40245485), (0.5, 0.5), (0.99039257, 0.40245485), (1, 0.5), (0.5, 0.5), (1, 0.5), (0.5, 0.5), (0.99039257, 0.40245482), (0.99039257, 0.40245482), (0.5, 0.5), (0.9619397, 0.3086583), (0.9619397, 0.3086583), (0.5, 0.5), (0.91573477, 0.22221488), (0.91573477, 0.22221488), (0.5, 0.5), (0.8535534, 0.14644662), (0.8535534, 0.14644662), (0.5, 0.5), (0.7777851, 0.0842652), (0.7777851, 0.0842652), (0.5, 0.5), (0.6913417, 0.038060278), (0.6913417, 0.038060278), (0.5, 0.5), (0.59754515, 0.0096074045), (0.59754515, 0.0096074045), (0.5, 0.5), (0.5, 0), (0.5, 0), (0.5, 0.5), (0.40245485, 0.0096074045), (0.40245485, 0.0096074045), (0.5, 0.5), (0.3086583, 0.038060278), (0.3086583, 0.038060278), (0.5, 0.5), (0.22221488, 0.0842652), (0.22221488, 0.0842652), (0.5, 0.5), (0.14644659, 0.14644662), (0.14644659, 0.14644662), (0.5, 0.5), (0.08426523, 0.22221488), (0.08426523, 0.22221488), (0.5, 0.5), (0.038060308, 0.3086583), (0.038060308, 0.3086583), (0.5, 0.5), (0.009607434, 0.40245482), (0.009607434, 0.40245482), (0.5, 0.5), (0, 0.5), (0, 0.5), (0.5, 0.5), (0.009607434, 0.59754515), (0.009607434, 0.59754515), (0.5, 0.5), (0.038060308, 0.6913417), (0.038060308, 0.6913417), (0.5, 0.5), (0.08426523, 0.7777851), (0.08426523, 0.7777851), (0.5, 0.5), (0.14644659, 0.8535534), (0.14644659, 0.8535534), (0.5, 0.5), (0.22221488, 0.91573477), (0.22221488, 0.91573477), (0.5, 0.5), (0.3086583, 0.9619397), (0.3086583, 0.9619397), (0.5, 0.5), (0.40245485, 0.99039257), (0.40245485, 0.99039257), (0.5, 0.5), (0.5, 1), (0.5, 1), (0.5, 0.5), (0.59754515, 0.99039257), (0.59754515, 0.99039257), (0.5, 0.5), (0.6913417, 0.9619397), (0.6913417, 0.9619397), (0.5, 0.5), (0.7777851, 0.91573477), (0.7777851, 0.91573477), (0.5, 0.5), (0.8535534, 0.8535534), (0.8535534, 0.8535534), (0.5, 0.5), (0.91573477, 0.7777851), (0.91573477, 0.7777851), (0.5, 0.5), (0.9619397, 0.6913417), (0.9619397, 0.6913417), (0.5, 0.5), (0.99039257, 0.59754515), (0.99039257, 0.59754515), (0.5, 0.5), (1, 0.5)] (
            interpolation = "faceVarying"
        )
        uniform token subdivisionScheme = "none"
        double3 xformOp:rotateXYZ = (0, 0, 0)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:rotateXYZ", "xformOp:scale"]
    }
}

def Xform "Environment"
{
    double3 xformOp:rotateXYZ = (0, 0, 0)
    double3 xformOp:scale = (1, 1, 1)
    double3 xformOp:translate = (0, 0, 0)
    uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:rotateXYZ", "xformOp:scale"]

    def DistantLight "defaultLight" (
        prepend apiSchemas = ["ShapingAPI"]
    )
    {
        float inputs:angle = 1
        float inputs:intensity = 3000
        float inputs:shaping:cone:angle = 180
        float inputs:shaping:cone:softness
        float inputs:shaping:focus
        color3f inputs:shaping:focusTint
        asset inputs:shaping:ies:file
        double3 xformOp:rotateXYZ = (315, 0, 0)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:rotateXYZ", "xformOp:scale"]
    }
}

