// SPDX-FileCopyrightText: Copyright (c) 2023-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
// SPDX-License-Identifier: Apache-2.0
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once
// clang-format off
#include <pch/UsdPCH.h>
// clang-format on


#include <string>


namespace isaacsim
{
namespace asset
{
namespace importer
{
namespace urdf
{
pxr::SdfPath SimpleImport(pxr::UsdStageRefPtr usdStage,
                          const std::string& path,
                          const std::string& meshPath,
                          std::map<pxr::TfToken, pxr::SdfPath>& meshList,
                          std::map<pxr::TfToken, pxr::SdfPath>& materialList,
                          const pxr::SdfPath& rootPath);


}
}
}
}
