# Changelog
## [1.1.5] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.1.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.1.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.1.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.1.1] - 2024-12-09
### Fixed
- Unit tests after renaming

## [1.1.0] - 2024-12-03
### Changed
- Renamed extension name to Physics API Editor, <PERSON> Util menu to Tools->Robotics menu,

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-10-02
### Changed
- Extension renamed to isaacsim.util.physics.

## [0.1.4] - 2024-05-15
### Fixed
- Issue when applying collisions to prims that have no points

## [0.1.3] - 2024-02-07
### Fixed
- Slow performance when clearning physics apis on large scenes

## [0.1.2] - 2023-08-09
### Fixed
- Switch from ui.Window to ScrollingWindow wrapper for extension because scrolling was broken

## [0.1.1] - 2023-01-06
### Fixed
- onclick_fn warning when creating UI

## [0.1.0] - 2021-07-27
### Added
- Initial version of Physics Inspector
