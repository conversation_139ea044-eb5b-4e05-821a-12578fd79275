{"IsaacCreateRenderProduct": {"version": 2, "icon": "icons/isaac-sim.svg", "description": "Isaac Sim node that creates a render product for use with offscreen rendering", "language": "Python", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {"uiName": "Isaac Create Render Product"}, "inputs": {"execIn": {"type": "execution", "description": "Input execution trigger"}, "width": {"type": "uint", "description": "Width of the render product, in pixels", "default": 1280}, "height": {"type": "uint", "description": "Height of the render product, in pixels", "default": 720}, "cameraPrim": {"type": "target", "description": "Usd prim reference to the camera associated with this render product"}, "enabled": {"type": "bool", "description": "Set to false to disable downstream execution and RP creation", "default": true}}, "outputs": {"renderProductPath": {"type": "token", "description": "Render product path for the created hydra texture"}, "execOut": {"type": "execution", "description": "Output execution trigger"}}}}