{"comment1": "parameters obtained from https://data.ouster.io/downloads/datasheets/datasheet-rev7-v3p0-os1.pdf", "comment2": "parameters without comments are assumed correct, but will ultimately need checking", "class": "sensor", "type": "lidar", "name": "OS1 REV7 128 10hz @ 512 resolution", "comment3": "Ouster OS1 REV7 128 channels @ 10Hz 512 horizontal resolution", "driveWorksId": "GENERIC", "profile": {"scanType": "rotary", "intensityProcessing": "normalization", "rotationDirection": "CW", "rayType": "IDEALIZED", "nearRangeM": 0.5, "comment4": "OPTICAL PERFORMANCE-Minimum Range 0.3 m for point cloud data", "farRangeM": 170.0, "comment5": "OPTICAL PERFORMANCE- Range 17 m @ >90% detection probability, 100 klx sunlight", "startAzimuthDeg": 0.0, "comment6": "x+ is 0°  y+ is 90° (and z is up, right handed)", "endAzimuthDeg": 360.0, "comment7": "OPTICAL PERFORMANCE- Field of View Horizontal: 360°", "upElevationDeg": 22.5, "comment8": "OPTICAL PERFORMANCE- Field of View Vertical: 45° (+22.5° to -22.5°)", "downElevationDeg": -22.5, "rangeResolutionM": 0.001, "comment9": "OPTICAL PERFORMANCE- Range Resolution 0.1 cm", "rangeAccuracyM": 0.025, "comment10": "OPTICAL PERFORMANCE- Range Accuracy Chart Min ±2.5 cm, Max ±5 cm", "avgPowerW": 0.002, "minReflectance": 0.1, "comment11": "OPTICAL PERFORMANCE - Range (10% Lambertian reflectivity, 1024 @ 10 Hz mode)", "minReflectanceRange": 90.0, "comment12": "OPTICAL PERFORMANCE - Range 90 m @ >90% detection probability, 100 klx sunlight", "wavelengthNm": 865.0, "comment13": "LASER-  Laser Wavelength 865 nm", "pulseTimeNs": 6, "comment14": "These add noise to the emitter direction to each point randomly if Std is not 0.0", "azimuthErrorMean": 0.0, "azimuthErrorStd": 0.01, "comment15": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Horizontal: ±0.01°", "elevationErrorMean": 0.0, "elevationErrorStd": 0.01, "comment16": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Vertical: ±0.01°", "maxReturns": 2, "scanRateBaseHz": 10.0, "reportRateBaseHz": 5120, "numberOfEmitters": 128, "emitterStateCount": 1, "emitterStates": [{"comment19": "beam_azimuth_angles from get_beam_intrinsics", "azimuthDeg": [4.2, 1.39, -1.41, -4.2, 4.21, 1.41, -1.4, -4.2, 4.21, 1.41, -1.4, -4.2, 4.21, 1.4, -1.39, -4.2, 4.21, 1.4, -1.4, -4.21, 4.21, 1.4, -1.4, -4.2, 4.2, 1.4, -1.4, -4.21, 4.2, 1.4, -1.39, -4.21, 4.2, 1.39, -1.4, -4.21, 4.21, 1.4, -1.4, -4.21, 4.2, 1.4, -1.4, -4.21, 4.2, 1.4, -1.4, -4.22, 4.21, 1.4, -1.4, -4.22, 4.2, 1.41, -1.42, -4.22, 4.2, 1.42, -1.41, -4.21, 4.21, 1.41, -1.41, -4.21, 4.21, 1.41, -1.41, -4.21, 4.21, 1.41, -1.41, -4.21, 4.22, 1.41, -1.4, -4.21, 4.21, 1.4, -1.4, -4.21, 4.22, 1.41, -1.4, -4.22, 4.21, 1.41, -1.4, -4.21, 4.22, 1.41, -1.4, -4.21, 4.23, 1.41, -1.4, -4.21, 4.22, 1.41, -1.4, -4.22, 4.22, 1.41, -1.4, -4.21, 4.23, 1.41, -1.39, -4.22, 4.22, 1.41, -1.39, -4.22, 4.23, 1.4, -1.4, -4.22, 4.22, 1.4, -1.4, -4.22, 4.22, 1.4, -1.4, -4.22, 4.22, 1.39, -1.4, -4.23], "comment20": "beam_altitude_angles from get_beam_intrinsics", "elevationDeg": [20.93, 20.67, 20.36, 20, 19.72, 19.45, 19.12, 18.77, 18.47, 18.19, 17.87, 17.51, 17.2, 16.92, 16.6, 16.24, 15.92, 15.64, 15.31, 14.94, 14.63, 14.32, 14, 13.63, 13.31, 13, 12.67, 12.3, 11.96, 11.66, 11.33, 10.96, 10.62, 10.3, 9.97, 9.61, 9.26, 8.95, 8.6, 8.24, 7.89, 7.57, 7.23, 6.87, 6.53, 6.2, 5.85, 5.48, 5.14, 4.8, 4.46, 4.09, 3.75, 3.41, 3.05, 2.7, 2.36, 2.02, 1.65, 1.3, 0.96, 0.62, 0.25, -0.09, -0.44, -0.79, -1.15, -1.49, -1.84, -2.18, -2.54, -2.88, -3.23, -3.58, -3.94, -4.28, -4.63, -4.99, -5.33, -5.67, -6.02, -6.38, -6.72, -7.07, -7.4, -7.76, -8.1, -8.44, -8.78, -9.14, -9.48, -9.8, -10.13, -10.51, -10.84, -11.17, -11.49, -11.86, -12.19, -12.51, -12.83, -13.2, -13.53, -13.84, -14.16, -14.54, -14.85, -15.15, -15.48, -15.85, -16.16, -16.46, -16.77, -17.15, -17.45, -17.75, -18.06, -18.42, -18.73, -19.01, -19.31, -19.67, -19.98, -20.25, -20.55, -20.92, -21.22, -21.48], "fireTimeNs": [762, 2287, 3812, 5337, 6862, 8387, 9912, 11437, 12962, 14487, 16012, 17537, 19062, 20587, 22112, 23637, 25162, 26687, 28212, 29737, 31262, 32787, 34312, 35837, 37362, 38887, 40412, 41937, 43462, 44987, 46512, 48037, 49562, 51087, 52612, 54137, 55662, 57187, 58712, 60237, 61762, 63287, 64812, 66337, 67862, 69387, 70912, 72437, 73962, 75487, 77012, 78537, 80062, 81587, 83112, 84637, 86162, 87687, 89212, 90737, 92262, 93787, 95312, 96837, 98362, 99887, 101412, 102937, 104462, 105987, 107512, 109037, 110562, 112087, 113612, 115137, 116662, 118187, 119712, 121237, 122762, 124287, 125812, 127337, 128862, 130387, 131912, 133437, 134962, 136487, 138012, 139537, 141062, 142587, 144112, 145637, 147162, 148687, 150212, 151737, 153262, 154787, 156312, 157837, 159362, 160887, 162412, 163937, 165462, 166987, 168512, 170037, 171562, 173087, 174612, 176137, 177662, 179187, 180712, 182237, 183762, 185287, 186812, 188337, 189862, 191387, 192912, 194437]}], "intensityMappingType": "LINEAR"}}