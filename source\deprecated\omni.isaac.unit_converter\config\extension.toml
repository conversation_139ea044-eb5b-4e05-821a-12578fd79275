[package]
version = "0.1.16"
category = "Other"
title = "Distance Unit Converter"
description="Utility tool to change the USD stage units per meter parameter. Converts most linear attributes that are bound by the units per meter stage attribute."
keywords = ["isaac", "utils"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.0.0. Replaced by omni.usd.metrics_assembler"

[dependencies]
"isaacsim.gui.components" = {}
"isaacsim.robot.schema" = {}
"omni.isaac.ui" = {}
"omni.kit.menu.utils" = {}
"omni.physx" = {}
"omni.usdphysics" = {}

[[python.module]]
name = "omni.isaac.unit_converter"

[[test]]
dependencies = [
]
