# Changelog
## [2.0.5] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.0.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.0.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

##  [2.0.0] - 2024-09-27

### Changed
- Extension deprecated since <PERSON> Sim 4.5.0. Replaced by isaacsim.cortex.behaviors

## [1.0.6] - 2024-07-25
### Removed
- Deprecation tag

## [1.0.5] - 2024-05-22
### Added
- Deprecation tag

## [1.0.4] - 2023-10-12
### Fixed
- Fix invalid Rotation Matrix in UR10 bin stacking behavior waypoints

## [1.0.3] - 2023-10-10
### Fixed
- Fix typos in UR10 bin stacking behavior waypoints - Cross products now use both primary and secondary axes accordingly;

## [1.0.2] - 2023-08-25
### Fixed
- Cleanup extra dependencies

## [1.0.1] - 2023-08-18
### Fixed
- Fix bug in UR10 bin stacking behavior; make flip timing agnostic to robot speed.

## [1.0.0] - 2023-08-15
### Added
- Moved The Cortex sample Behaviors into their own extension
