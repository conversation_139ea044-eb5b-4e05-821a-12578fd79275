# Changelog
## [2.2.1] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.2.0] - 2025-04-28
### Changed
- Updated image visualization window to show ROS yaml file and 180 degree rotation as default config for image generation

## [2.1.1] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.1.0] - 2025-03-31
### Changed
- Menu items were added by MenuHelpers.

## [2.0.5] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

### Added
- Layout specifically for Occupancy Map was added.

## [2.0.4] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.3] - 2024-12-03
### Changed
- Isaac Util menu to Tools->Robotics menu

## [2.0.2] - 2024-10-28
### Changed
- Remove test imports from runtime

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-10-04
### Changed
- Extension  renamed to isaacsim.asset.gen.omap.ui.

## [1.1.1] - 2024-09-03
### Changed
- Limit Cell Size slider's minimum value to avoid it begin less than or equal to 0

## [1.1.0] - 2024-05-16
### Changed
- Invisible geometry will not be mapped when Use PhysX Collision Geometry is false

## [1.0.1] - 2024-05-15
### Fixed
- Issue when generating occupancy map with prims that have no points

## [1.0.0] - 2024-03-11
### Added
- Initial version of Isaac Sim Occupancy Map UI Extension
