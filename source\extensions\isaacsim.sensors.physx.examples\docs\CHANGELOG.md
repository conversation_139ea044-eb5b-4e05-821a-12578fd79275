# Changelog
## [2.2.12] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [2.2.11] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.2.10] - 2025-05-10
### Changed
- Enable FSD in test settings

## [2.2.9] - 2025-04-09
### Changed
- Update all test args to be consistent

## [2.2.8] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.2.7] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.2.6] - 2025-03-24
### Changed
- Migrate to Events 2.0

## [2.2.5] - 2025-03-11
### Changed
- Switch asset root for tests to internal nucleus

## [2.2.4] - 2025-01-27
### Changed
- Updated docs link

## [2.2.3] - 2025-01-26
### Changed
- Update test settings

## [2.2.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.2.1] - 2025-01-17
### Changed
- Temporarily changed docs link

## [2.2.0] - 2024-12-16
### Added
- lightbeam_sensor example

## [2.1.1] - 2024-11-25
### Fixed
- lidar examples pointers and shutting down properly

## [2.1.0] - 2024-10-29
### Changed
- moved examples from menu to browser

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-10-04
### Removed
- Ultrasonic sensor example.

## [1.0.1] - 2024-09-03
### Fixed
- Disables "Load Lidar" button in Lidar example while stage is clearing

## [1.0.0] - 2024-03-12
### Added
- Initial version of Isaac Sim Range sensor extension examples
