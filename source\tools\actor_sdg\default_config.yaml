# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

isaacsim.replicator.agent:
  version: 0.7.0
  global:
    seed: 123456
    simulation_length: 1800
  sensor:
    camera_num: 4
  character:
    asset_path:
    command_file: default_command.txt
    num: 8
  robot:
    command_file: default_robot_command.txt
    nova_carter_num: 0
    iw_hub_num: 0
    write_data: false
  replicator:
    writer: IRABasicWriter
    parameters:
      object_info_bounding_box_2d_tight: false
      object_info_bounding_box_2d_loose: false
      object_info_bounding_box_3d: false
      agent_info_skeleton_data: false
      semantic_filter_predicate: class:character|robot;id:*
      rgb: true
      camera_params: true
