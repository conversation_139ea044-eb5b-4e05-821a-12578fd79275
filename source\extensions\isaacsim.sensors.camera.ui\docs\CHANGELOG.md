# Changelog
## [0.1.13] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [0.1.12] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.1.11] - 2025-05-10
### Changed
- Enable FSD in test settings

## [0.1.10] - 2025-04-16
### Changed
- Assets root path lookup moved to on-click callback, rather than on extension startup

## [0.1.9] - 2025-04-09
### Changed
- Update all test args to be consistent

## [0.1.8] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [0.1.7] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [0.1.6] - 2025-03-20
### Added
- sensors to context menu

## [0.1.5] - 2025-03-11
### Changed
- Switch asset root for tests to internal nucleus

## [0.1.4] - 2025-01-26
### Changed
- Update test settings

## [0.1.3] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [0.1.2] - 2025-01-06
### Changed
- Updated menu name of SG8-AR0820C-5300-G2A-H120YA, SG8-AR0820C-5300-G2A-H30YA, SG8-AR0820C-5300-G2A-H60SA to SG8S-AR0820C-5300-G2A-H120YA, SG8S-AR0820C-5300-G2A-H30YA, SG8S-AR0820C-5300-G2A-H60SA

## [0.1.1] - 2024-12-20
### Changed
- Corrected menu path of the H30SA camera to H30YA

## [0.1.0] - 2024-12-10
### Added
- Initial version of isaacsim.sensors.camera.ui
