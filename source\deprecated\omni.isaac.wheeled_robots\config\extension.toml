[package]
version = "3.0.7"
category = "Simulation"
title = "Wheeled Robots"
description = "This extension provides wheeled robot utilities"
keywords = ["isaac", "physics", "analyze", "tune"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot.wheeled_robots, isaacsim.robot.wheeled_robots.examples."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.robot.wheeled_robots" = {}

[[python.module]]
name = "omni.isaac.wheeled_robots"

[[python.module]]
name = "omni.isaac.wheeled_robots.tests"

[[test]]
dependencies = [
    "omni.isaac.core",
    "omni.isaac.nucleus",
]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
