{"ROS2SubscribeJointState": {"version": 2, "icon": "icons/isaac-sim.svg", "description": ["This node subscribes to a joint state command of a robot in a ROS2 JointState message"], "metadata": {"uiName": "ROS2 Subscribe Joint State"}, "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2:subscriber", "inputs": {"execIn": {"type": "execution", "description": "The input execution port"}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0}, "nodeNamespace": {"type": "string", "description": "Namespace of ROS2 Node, prepends any published/subscribed topic by the node namespace", "default": ""}, "topicName": {"type": "string", "description": "Name of ROS2 Topic", "default": "joint_command"}, "qosProfile": {"type": "string", "description": "QoS profile config", "default": ""}, "queueSize": {"type": "uint64", "description": "The number of messages to queue up before throwing some away, in case messages are collected faster than they can be sent. Only honored if 'history' QoS policy was set to 'keep last'. This setting can be overwritten by qosProfile input.", "default": 10}}, "outputs": {"execOut": {"type": "execution", "description": "Output execution triggers when a new message is received"}, "timeStamp": {"type": "double", "description": "ROS2 Timestamp in seconds", "uiName": "Timestamp", "default": 0.0}, "jointNames": {"type": "token[]", "description": "Commanded joint names"}, "positionCommand": {"type": "double[]", "description": "Position commands"}, "velocityCommand": {"type": "double[]", "description": "Velocity commands"}, "effortCommand": {"type": "double[]", "description": "Effort commands"}}}}