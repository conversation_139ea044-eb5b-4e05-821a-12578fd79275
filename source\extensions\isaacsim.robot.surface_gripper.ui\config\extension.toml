[package]
version = "3.0.2"
category = "Simulation"
title = "Isaac Sim Surface Gripper UI Components"
description = "UI components for the Surface Gripper extension"
keywords = ["isaac", "physics", "end-effector","ui"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.gui.components" = {}
"isaacsim.gui.menu" = {}
"isaacsim.robot.surface_gripper" = {}

[[python.module]]
name = "isaacsim.robot.surface_gripper.ui"
