[package]
version = "3.0.7"
category = "Simulation"
title = "Isaac Sim Manipulators"
description = "Manipulators"
keywords = ["isaac", "manipulators"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot.manipulators."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.robot.manipulators" = {}

[[python.module]]
name = "omni.isaac.manipulators"

[[python.module]]
name = "omni.isaac.manipulators.tests"

[[test]]
dependencies = [
    "omni.isaac.core",
    "omni.isaac.nucleus",
]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
