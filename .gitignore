﻿# all folders starting with _ are local
_*/
!_impl/
# exclude local website repo, used for publishing
website/

.lastrun
.lastformat
.lastverify
.DS_Store
**/.vscode/ipch
Makefile
*.swp
*.swo
exts.deps.generated.kit

# byte-compiled python files
*.py[cod]

# pip cache
.cache

# packman package files
*@*.7z
*@*.zip
*@*.txt

# packaged unittest archives
/test_binaries-*.zip
/tests-*.zip
/run_tests.*
*.sh1

# patch files
*.diff
*.patch

# binaries
# *.ico
*.bmp
*.gif
*.jpg
*.jpeg
*.tga
*.psd
*.raw
*.exr
*.hdr
*.dds
*.ktx
*.spv
*.dxbc
*.dxil
*.gltf
*.glb
*.fbx
*.usd
*.usda
*.usdz
*.obj
*.mat

*.suo

# ignore the autoexec.lst file
autoexec.lst

# don't ignore carb.usd folder
!*.usd/

NvfPerflog.txt
imgui.ini
CON

# packman .user config
*.packman.xml.user

# allow anything in data and docs/images (both under Git LFS management)
!data/**
!docs/images/**
!source/extensions/**/data/**

# ignore docs autogenerated in or copied into the docs/source folder
docs/source/*

# ignore the cache
data/cache

symbols.txt
/.vs

# petert: temp file i create for debugging
output.txt

# OVAT files
gcn_local.yml
_venv/
venv/
.venv/
outputs.yml

# ignoring thumbnails generated by kit
.thumbs

.vscode/.python_samples.env
.vscode/.pxr_plugin.env
# vscode settings are copied and filled in from 'settings.json.template'
.vscode/settings.json

.nvidia-omniverse
PACKAGE-DEPS.yaml
.ipynb_checkpoints


# ignore docs output
.local
conf.py


# ignore docs file
ogn.rst
source/extensions/**/docs/ogn/
source/extensions/**/docs/index.rst
source/deprecated/**/docs/ogn/
source/deprecated/**/docs/index.rst
docs/autogenerated-extension-toc.txt

output

!schema.usda
!RobotSchema.usda
generatedSchema.usda

# ignore crash logs from streaming client sessions
NvStreamer-*.etli

# nodejs modules
node_modules

#container
tools/container/report.json
tools/container/_*
tools/container/7za
tools/container/VERSION.md
report.json

Temp_Config*.json

# Ignore .nvcode

.nvcode/
.nvcode.json

# Ignore packman verification results
packman_verification_results.json
packman_full_results.json
packman_full_results.csv

# Ignore results from banned word check
results.json
results_by_file.json

# EULA check marker file
.eula_accepted