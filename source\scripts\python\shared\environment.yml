# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: isaac-sim
channels: 
  - defaults
  - pytorch
  - nvidia
dependencies: 
  - python=3.11
  - pip
  - pytorch 
  - torchvision 
  - torchaudio 
  - cuda-toolkit=11.8
  - pip:
    - tensorboard==2.18.0
    - tensorboard-plugin-wit==1.8.1 
    - protobuf==3.20.3
