# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

adjacencies:
- id: cross
  neighbors:
  - neighbor_id: dead_end
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: corridor
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: dead_end
    neighbor_rotation: 1
    self_rotation: 2
  - neighbor_id: corner
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: corridor
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: corner
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: cross
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: dead_end
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: cross
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: corner
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: corner
    neighbor_rotation: 1
    self_rotation: 0
  - neighbor_id: corridor
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: dead_end
    neighbor_rotation: 1
    self_rotation: 3
  - neighbor_id: corridor
    neighbor_rotation: 1
    self_rotation: 1
  - neighbor_id: cross
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: corner
    neighbor_rotation: 0
    self_rotation: 2
  - neighbor_id: cross
    neighbor_rotation: 0
    self_rotation: 0
  - neighbor_id: corner
    neighbor_rotation: 1
    self_rotation: 2
  - neighbor_id: corner
    neighbor_rotation: 1
    self_rotation: 3
  - neighbor_id: corner
    neighbor_rotation: 0
    self_rotation: 3
- id: corridor
  neighbors:
  - neighbor_id: cross
    neighbor_rotation: 0
    self_rotation: 1
  - neighbor_id: cross
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 1
    self_rotation: 1
- id: corner
  neighbors:
  - neighbor_id: cross
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 3
    self_rotation: 2
  - neighbor_id: cross
    neighbor_rotation: 3
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 2
    self_rotation: 2
  - neighbor_id: cross
    neighbor_rotation: 0
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 1
    self_rotation: 2
  - neighbor_id: cross
    neighbor_rotation: 1
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 0
    self_rotation: 2
- id: dead_end
  neighbors:
  - neighbor_id: cross
    neighbor_rotation: 2
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 1
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 0
    self_rotation: 3
  - neighbor_id: cross
    neighbor_rotation: 3
    self_rotation: 3
tiles:
- id: cross
  weights:
  - 1
  - 1
  - 1
  - 1
- id: corridor
  weights:
  - 1
  - 1
  - 1
  - 1
- id: corner
  weights:
  - 1
  - 1
  - 1
  - 1
- id: dead_end
  weights:
  - 1
  - 1
  - 1
  - 1
