# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot descriptor defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - joint1
    - joint2
    - joint3
    - joint4
    - joint5
    - joint6
default_q: [
    0.0,-0.2,-1.7,-1.507,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:
    - {name: finger_joint, rule: fixed, value: -0.0}
    - {name: left_inner_knuckle_joint, rule: fixed, value: 0.0}
    - {name: right_inner_knuckle_joint, rule: fixed, value: -0.0}
    - {name: right_outer_knuckle_joint, rule: fixed, value: 0.0}
    - {name: left_inner_finger_joint, rule: fixed, value: -0.0}
    - {name: right_inner_finger_joint, rule: fixed, value: 0.0}

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - onrobot_rg2_base_link:
    - "center": [0.0, 0.001, 0.04]
      "radius": 0.053
    - "center": [0.0, -0.002, 0.084]
      "radius": 0.044
    - "center": [0.0, 0.01, 0.12]
      "radius": 0.037
    - "center": [-0.0, -0.011, 0.115]
      "radius": 0.037
  - left_outer_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.018
    - "center": [-0.0, -0.04, 0.034]
      "radius": 0.018
    - "center": [-0.0, -0.013, 0.011]
      "radius": 0.018
    - "center": [-0.0, -0.027, 0.023]
      "radius": 0.018
  - left_inner_knuckle:
    - "center": [0.0, -0.014, 0.014]
      "radius": 0.018
    - "center": [-0.001, -0.002, 0.002]
      "radius": 0.018
    - "center": [0.001, -0.031, 0.031]
      "radius": 0.018
  - right_inner_knuckle:
    - "center": [0.0, -0.014, 0.014]
      "radius": 0.018
    - "center": [-0.001, -0.002, 0.002]
      "radius": 0.018
    - "center": [0.001, -0.031, 0.031]
      "radius": 0.018
  - right_inner_finger:
    - "center": [0.002, 0.01, 0.028]
      "radius": 0.016
    - "center": [0.003, 0.006, 0.014]
      "radius": 0.014
    - "center": [-0.003, 0.012, 0.037]
      "radius": 0.014
  - left_inner_finger:
    - "center": [0.002, 0.01, 0.028]
      "radius": 0.016
    - "center": [0.003, 0.006, 0.014]
      "radius": 0.014
    - "center": [-0.003, 0.012, 0.037]
      "radius": 0.014
  - right_outer_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.018
    - "center": [-0.0, -0.04, 0.034]
      "radius": 0.018
    - "center": [-0.0, -0.013, 0.011]
      "radius": 0.018
    - "center": [-0.0, -0.027, 0.023]
      "radius": 0.018
  - link3:
    - "center": [0.002, 0.029, 0.07]
      "radius": 0.17
    - "center": [-0.001, 0.016, -0.047]
      "radius": 0.16
  - link1:
    - "center": [0.175, 0.117, -0.126]
      "radius": 0.15
    - "center": [-0.067, 0.151, 0.001]
      "radius": 0.117
    - "center": [0.107, -0.05, -0.332]
      "radius": 0.109
    - "center": [-0.085, -0.068, -0.319]
      "radius": 0.105
    - "center": [0.016, 0.078, -0.338]
      "radius": 0.103
    - "center": [-0.124, -0.079, -0.195]
      "radius": 0.104
  - link2:
    - "center": [0.803, -0.02, -0.21]
      "radius": 0.09
    - "center": [-0.051, -0.031, -0.177]
      "radius": 0.088
    - "center": [0.432, 0.056, -0.195]
      "radius": 0.082
    - "center": [0.101, -0.053, -0.177]
      "radius": 0.088
    - "center": [-0.013, 0.081, -0.177]
      "radius": 0.088
    - "center": [0.896, 0.057, -0.209]
      "radius": 0.087
    - "center": [0.578, 0.007, -0.225]
      "radius": 0.074
    - "center": [0.894, -0.063, -0.207]
      "radius": 0.087
    - "center": [0.126, 0.068, -0.176]
      "radius": 0.088
    - "center": [0.655, -0.056, -0.23]
      "radius": 0.07
    - "center": [0.675, 0.05, -0.229]
      "radius": 0.069
    - "center": [-0.032, -0.081, -0.177]
      "radius": 0.088
    - "center": [0.502, 0.002, -0.21]
      "radius": 0.079
    - "center": [0.735, 0.017, -0.22]
      "radius": 0.079
    - "center": [0.357, 0.059, -0.19]
      "radius": 0.083
    - "center": [0.281, 0.062, -0.186]
      "radius": 0.085
    - "center": [0.204, 0.065, -0.181]
      "radius": 0.086
    - "center": [0.206, -0.039, -0.185]
      "radius": 0.086
    - "center": [0.307, -0.025, -0.194]
      "radius": 0.084
    - "center": [0.406, -0.011, -0.202]
      "radius": 0.081
  - link4:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.08
    - "center": [-0.009, 0.001, 0.344]
      "radius": 0.1
    - "center": [-0.002, 0.0, 0.063]
      "radius": 0.084
    - "center": [-0.003, 0.0, 0.128]
      "radius": 0.087
    - "center": [-0.005, 0.0, 0.197]
      "radius": 0.091
    - "center": [-0.007, 0.0, 0.269]
      "radius": 0.096
    - "center": [-0.005, 0.0, 0.798]
      "radius": 0.12
    - "center": [-0.005, 0.0, 0.422]
      "radius": 0.116
    - "center": [-0.005, 0.0, 0.722]
      "radius": 0.119
    - "center": [-0.005, 0.0, 0.646]
      "radius": 0.118
    - "center": [-0.005, 0.0, 0.571]
      "radius": 0.117
    - "center": [-0.005, 0.0, 0.496]
      "radius": 0.117
  - link5:
    - "center": [0.11, 0.0, 0.003]
      "radius": 0.08
