[package]
version = "4.0.7"
category = "Simulation"
title = "Isaac Sim Core"
description = "Core"
keywords = ["isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.core.api"

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.utils" = {}

[[python.module]]
name = "omni.isaac.core"

[[python.module]]
name = "omni.isaac.core.tests"

[[test]]
dependencies = [
    "omni.isaac.cloner",
    "omni.isaac.nucleus",
    "omni.isaac.sensor",
    "omni.replicator.core",
]
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
