# Changelog

## [0.5.2] - 2025-06-12
### Fixed
- Fix broken autodocs references in api.rst

## [0.5.1] - 2025-06-07
### Changed
- Set test timeout to 900 seconds

## [0.5.0] - 2025-06-06
### Changed
- Update source code to use the experimental core utils API

## [0.4.3] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [0.4.2] - 2025-05-23
### Changed
- Rename test utils.py to common.py

## [0.4.1] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.4.0] - 2025-05-16
### Changed
- Rename properties related to the wrapped prim paths

## [0.3.2] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [0.3.1] - 2025-05-16
### Fixed
- Fix rigid prim's angular velocity unit for USD backend

## [0.3.0] - 2025-05-12
### Changed
- Update implementation to use experimental material API (visual and physics materials)

## [0.2.0] - 2025-04-29
### Added
- Add support for articulation actuator modeling: advanced joint properties and drive envelope

## [0.1.2] - 2025-04-27
### Fixed
- Added extension specific unit test settings to fix broken tests

## [0.1.1] - 2025-04-21
### Changed
- Update Isaac Sim robot asset path
- Update docstrings for the Franka Panda robot

## [0.1.0] - 2025-03-21
### Added
- Initial release
