{"ROS2Publisher": {"version": 1, "icon": "icons/isaac-sim.svg", "description": ["This node publishes any existing ROS2 message"], "metadata": {"uiName": "ROS2 Publisher"}, "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2:publisher", "inputs": {"execIn": {"type": "execution", "description": "The input execution port.", "metadata": {"displayGroup": "parameters"}}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0, "metadata": {"displayGroup": "parameters"}}, "nodeNamespace": {"type": "string", "description": "Namespace of ROS2 Node, prepends any published/subscribed topic by the node namespace", "default": "", "metadata": {"displayGroup": "parameters"}}, "topicName": {"type": "string", "description": "Name of ROS2 Topic", "default": "topic", "metadata": {"displayGroup": "parameters"}}, "qosProfile": {"type": "string", "description": "QoS profile config", "default": "", "metadata": {"displayGroup": "parameters"}}, "queueSize": {"type": "uint64", "description": "The number of messages to queue up before throwing some away, in case messages are collected faster than they can be processed. Only honored if 'history' QoS policy was set to 'keep last'. This setting can be overwritten by qosProfile input.", "default": 10, "metadata": {"displayGroup": "parameters"}}, "messagePackage": {"type": "string", "description": "Message package (e.g.: std_msgs for std_msgs/msg/Int32)", "metadata": {"displayGroup": "parameters"}}, "messageSubfolder": {"type": "string", "description": "Message subfolder (e.g.: msg for std_msgs/msg/Int32)", "default": "msg", "metadata": {"displayGroup": "parameters"}}, "messageName": {"type": "string", "description": "Message name (e.g.: Int32 for std_msgs/msg/Int32)", "metadata": {"displayGroup": "parameters"}}}, "outputs": {"execOut": {"type": "execution", "description": "Output execution triggers when a new message is published"}}}}