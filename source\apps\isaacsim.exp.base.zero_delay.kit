[package]
title = "Isaac Sim Python"
description = "A trimmed down app for use with python providing zero frame delay"
version = "5.0.0"
execFile = "isaac-sim.zero_delay"
keywords = ["experience", "app", "usd", "isaacsim"] # That makes it browsable in UI with "experience" filter


[dependencies]
"isaacsim.exp.base" = {}

[settings]
app.name = "Isaac-Sim Zero Delay"
app.version = "5.0.0"
app.hydraEngine.waitIdle = true
app.updateOrder.checkForHydraRenderComplete = 1000
app.vulkan = true                     # Explicitly enable Vulkan (on by default on Linux, off by default on Windows)
app.enableDeveloperWarnings = false     # disable developer warnings to reduce log noise
app.exts.isaacsim.ros2.bridge.publish_multithreading_disabled = true # disable multithreading for ros2 bridge to reduce latency

[settings.app.exts.folders]
'++' = [
    "${app}",
    "${app}/../exts",
    "${app}/../extscache",
    "${app}/../extsUser",
    "${app}/../extsDeprecated",
]

[settings.exts."omni.kit.registry.nucleus"]
registries = [
	{ name = "kit/default", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/107/shared" },
	{ name = "kit/sdk", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/sdk/${kit_version_short}/${kit_git_hash}" },
	{ name = "kit/community", url = "https://dw290v42wisod.cloudfront.net/exts/kit/community" },
]
