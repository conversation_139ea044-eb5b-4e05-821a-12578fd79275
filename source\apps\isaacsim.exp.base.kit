[package]
description = "Base Isaac Sim App"
title = "Isaac Sim Base"
version = "5.0.0"
keywords = ["experience", "app", "usd"] # That makes it browsable in UI with "experience" filter


[dependencies]
# Isaac Sim extensions
"isaacsim.app.about" = {}
"isaacsim.asset.browser" = {}
"isaacsim.core.api" = {}
"isaacsim.core.cloner" = {}
"isaacsim.core.deprecation_manager" = { order = -100 }
"isaacsim.core.experimental.materials" = {}
"isaacsim.core.experimental.objects" = {}
"isaacsim.core.experimental.prims" = {}
"isaacsim.core.experimental.utils" = {}
"isaacsim.core.nodes" = {}
"isaacsim.core.simulation_manager" = {}
"isaacsim.core.throttling" = {}
"isaacsim.core.utils" = {}
"isaacsim.core.version" = {}
"isaacsim.cortex.behaviors" = {}
"isaacsim.cortex.framework" = {}
"isaacsim.gui.menu" = {}
"isaacsim.gui.content_browser" = {}
"isaacsim.gui.property" = {}
"isaacsim.gui.sensors.icon" = {}
"isaacsim.replicator.behavior" = {}
"isaacsim.replicator.domain_randomization" = {}
"isaacsim.replicator.writers" = {}
"isaacsim.robot_motion.lula" = {}
"isaacsim.robot_motion.motion_generation" = {}
"isaacsim.robot.manipulators" = {}
"isaacsim.robot.policy.examples" = {}
"isaacsim.robot.schema" = {}
"isaacsim.robot.surface_gripper" = {}
"isaacsim.robot.wheeled_robots" = {}
"isaacsim.sensors.camera" = {}
"isaacsim.sensors.physics" = {}
"isaacsim.sensors.physx" = {}
"isaacsim.sensors.rtx" = {}
"isaacsim.simulation_app" = {}
"isaacsim.storage.native" = {}
"isaacsim.util.debug_draw" = {}
"omni.isaac.core_archive" = {}
"omni.isaac.ml_archive" = {}
"omni.kit.loop-isaac" = {}
"omni.pip.cloud" = {}
"omni.pip.compute" = {}

# Deprecated extensions for backwards compatibility
"omni.isaac.asset_browser" = {}
"omni.isaac.assets_check" = {}
"omni.isaac.cloner" = {}
"omni.isaac.core_nodes" = {}
"omni.isaac.core" = {}
"omni.isaac.cortex.sample_behaviors" = {}
"omni.isaac.cortex" = {}
"omni.isaac.dynamic_control" = {}
"omni.isaac.franka" = {}
"omni.isaac.kit" = {}
"omni.isaac.lula_test_widget" = {}
"omni.isaac.lula" = {}
"omni.isaac.manipulators" = {}
"omni.isaac.menu" = {}
"omni.isaac.motion_generation" = {}
"omni.isaac.nucleus" = {}
"omni.isaac.quadruped" = {}
"omni.isaac.range_sensor" = {}
"omni.isaac.sensor" = {}
"omni.isaac.surface_gripper" = {}
"omni.isaac.universal_robots" = {}
"omni.isaac.utils" = {}
"omni.isaac.version" = {}
"omni.isaac.wheeled_robots" = {}
"omni.isaac.window.about" = {}
"omni.kit.property.isaac" = {}
"omni.replicator.isaac" = {}
"omni.usd.schema.isaac" = {}

# Isaac Sim Extra
"isaacsim.asset.importer.mjcf" = {}
"isaacsim.asset.importer.urdf" = {}
"omni.physx.tensors" = {}
"omni.replicator.core" = {}
"omni.replicator.replicator_yaml" = {}
"omni.syntheticdata" = {}
"semantics.schema.editor" = {}
"semantics.schema.property" = {}

# Kit based editor extensions
"omni.anim.curve.core" = {}
"omni.anim.graph.schema" = {}
"omni.anim.navigation.schema" = {}
"omni.graph.action" = {}
"omni.graph.core" = {}
"omni.graph.nodes" = {}
"omni.graph.scriptnode" = {}
"omni.graph.ui_nodes" = {}
"omni.hydra.engine.stats" = {}
"omni.hydra.rtx" = {}
"omni.kit.mainwindow" = {}
"omni.kit.manipulator.camera" = {}
"omni.kit.manipulator.prim" = {}
"omni.kit.manipulator.selection" = {}
"omni.kit.material.library" = {}
"omni.kit.menu.common" = { order = 1000 }
"omni.kit.menu.create" = {}
"omni.kit.menu.stage" = {}
"omni.kit.menu.utils" = {}
"omni.kit.primitive.mesh" = {}
"omni.kit.property.bundle" = {}
"omni.kit.raycast.query" = {}
"omni.kit.stage_template.core" = {}
"omni.kit.telemetry" = {}
"omni.kit.tool.asset_importer" = {}
"omni.kit.tool.collect" = {}
"omni.kit.viewport.legacy_gizmos" = {}
"omni.kit.viewport.menubar.camera" = {}
"omni.kit.viewport.menubar.display" = {}
"omni.kit.viewport.menubar.lighting" = {}
"omni.kit.viewport.menubar.render" = {}
"omni.kit.viewport.menubar.settings" = {}
"omni.kit.viewport.scene_camera_model" = {}
"omni.kit.viewport.window" = {}
"omni.kit.window.console" = {}
"omni.kit.window.content_browser" = {}
"omni.kit.window.property" = {}
"omni.kit.window.stage" = {}
"omni.kit.window.status_bar" = {}
"omni.kit.window.toolbar" = {}
"omni.physics.physx" = {}
"omni.physics.stageupdate" = {}
"omni.rtx.settings.core" = {}
"omni.uiaudio" = {}
"omni.usd.metrics.assembler.ui" = {}
"omni.usd.schema.flow" = {}
"omni.usd.schema.metrics.assembler" = {}
"omni.warp.core" = {}
"omni.hydra.usdrt_delegate" = {}

# Extensions normally enabled in kit base editor
# "omni.activity.profiler" = {}
# "omni.activity.pump" = {}
# "omni.kit.viewport.ready" = {}

[settings]
exts."omni.kit.material.library".ui_show_list = [
    "OmniPBR",
    "OmniGlass",
    "OmniSurface",
    "USD Preview Surface",
]
exts."omni.kit.renderer.core".present.enabled = false # Fixes MGPU stability issue
exts."omni.kit.viewport.window".windowMenu.entryCount = 2 # Allow user to create two viewports by default
exts."omni.kit.viewport.window".windowMenu.label = "" # Put Viewport menuitem under Window menu
exts."omni.rtx.window.settings".window_menu = "Window" # Where to put the render settings menuitem
exts."omni.usd".locking.onClose = false # reduce time it takes to close/create stage
exts."omni.ui".raster.default_rasterpolicy_enabled = true # Improve UI rendering performance
exts."omni.kit.widget.graph".raster_nodes = true # Improve UI rendering performance
renderer.asyncInit = true # Don't block while renderer inits
renderer.gpuEnumeration.glInterop.enabled = false # Improves startup speed.
rtx-transient.dlssg.enabled = false # DLSSG frame generation is not compatible with synthetic data generation
rtx.hydra.mdlMaterialWarmup = true # start loading the MDL shaders needed before any delegate is actually created.
rtx.post.dlss.execMode = 3 # Set the DLSS model can be 0 (Performance), 1 (Balanced), 2 (Quality), or 3 (Auto)
omni.replicator.asyncRendering = false # Async rendering must be disabled for SDG
exts."omni.kit.test".includeTests = ["*isaac*"] # Add isaac tests to test runner
foundation.verifyOsVersion.enabled = false
physics.autoPopupSimulationOutputWindow = false
exts."semantics_schema_editor".hideWindowOnStartup = true

# Disable for base application
[settings."filter:platform"."windows-x86_64"]
isaac.startup.ros_bridge_extension = ""
[settings."filter:platform"."linux-x86_64"]
isaac.startup.ros_bridge_extension = ""

# menu styling
[settings.exts."omni.kit.menu.utils"]
logDeprecated = false
margin_size = [18, 3]
tick_spacing = [10, 6]
margin_size_posttick = [0, 3]
separator_size = [14, 10]
root_spacing = 3
post_label_spaces = 6
color_tick_enabled = 0xFFFAC434
color_separator = 0xFF7E7E7E
color_label_enabled = 0xFFEEEEEE
menu_title_color = 0xFF202020
menu_title_line_color = 0xFF5E5E5E
menu_title_text_color = 0xFF8F8F8F
menu_title_text_height = 24
menu_title_close_color = 0xFFC6C6C6
indent_all_ticks = false
show_menu_titles = true

[settings.app]
name = "Isaac-Sim Base"
version = "5.0.0"
versionFile = "${app}/../VERSION"
content.emptyStageOnStart = true
fastShutdown = true
file.ignoreUnsavedOnExit = true
font.file = "${fonts}/OpenSans-SemiBold.ttf"
font.size = 16
gatherRenderResults = true # True to prevent artifacts in multiple viewport configurations, can be set to false for better performance in some cases
hangDetector.enabled = true
hangDetector.timeout = 120
settings.fabricDefaultStageFrameHistoryCount = 3 # needed for omni.syntheticdata TODO105 still true?
settings.persistent = true                       # settings are persistent for this app
useFabricSceneDelegate = true                    # use FSD by default for better performance
enableDeveloperWarnings = false                  # disable developer warnings to reduce log noise
runLoops.main.rateLimitEnabled = false           # Disable rate limiting, simulation runs as fast as possible
runLoops.main.manualModeEnabled = true           # Enable manual mode, dt is fixed at requested rate
player.useFixedTimeStepping = false              # Disable fixed time stepping, dt is controlled by loop runner
events.qualifiedNames = true                     # Enable qualified names for carb-events-2.0 observation api

vulkan = true # Enable Vulkan by default for all platforms
### async rendering settings
asyncRendering = false
asyncRenderingLowLatency = false

[settings.app.window]
iconPath = "${isaacsim.simulation_app}/data/omni.isaac.sim.png"
title = "Isaac Sim Base"

[settings.app.exts.folders]
'++' = [
    "${app}",
    "${app}/../exts",
    "${app}/../extscache",
    "${app}/../extsUser",
    "${app}/../extsDeprecated",
]

[settings.app.renderer]
resolution.height = 720
resolution.width = 1280
skipWhileMinimized = false # python app does not throttle
sleepMsOnFocus = 0         # python app does not throttle
sleepMsOutOfFocus = 0      # python app does not throttle

[settings.app.viewport]
defaultCamPos.x = 5
defaultCamPos.y = 5
defaultCamPos.z = 5
defaults.fillViewport = false # default to not fill viewport
grid.enabled = true
outline.enabled = true
boundingBoxes.enabled = false
show.camera=false
show.lights=false

[settings.telemetry]
enableAnonymousAppName = true # Anonymous Kit application usage telemetry
enableAnonymousData = true # Anonymous Kit application usage telemetry

[settings.persistent]
app.primCreation.DefaultXformOpOrder = "xformOp:translate, xformOp:orient, xformOp:scale"
app.primCreation.DefaultXformOpType = "Scale, Orient, Translate"
app.primCreation.typedDefaults.camera.clippingRange = [0.01, 10000000.0] # Meters default
app.primCreation.DefaultXformOpPrecision = "Double"
app.primCreation.DefaultRotationOrder = "ZYX"
app.primCreation.PrimCreationWithDefaultXformOps = true
app.stage.timeCodeRange = [0, 1000000]
app.stage.upAxis = "Z" # Isaac Sim default Z up
app.viewport.camMoveVelocity = 0.05 # Meters default
app.viewport.gizmo.scale = 0.01 # Meters default
app.viewport.grid.scale = 1.0 # Meters default
app.viewport.camShowSpeedOnStart = false # Hide camera speed on startup
app.omniverse.gamepadCameraControl = false # Disable gamepad control for camera by default
exts."omni.anim.navigation.core".navMesh.config.autoRebakeOnChanges = false
exts."omni.anim.navigation.core".navMesh.viewNavMesh = false
physics.visualizationDisplayJoints = false # improves performance
physics.visualizationSimulationOutput = false # improves performance
physics.resetOnStop = true # Physics state is reset on stop
renderer.startupMessageDisplayed = true # hides the IOMMU popup window
resourcemonitor.timeBetweenQueries = 100 # improves performance
simulation.defaultMetersPerUnit = 1.0 # Meters default
omni.replicator.captureOnPlay = true

[settings.exts."omni.kit.registry.nucleus"]
registries = [
	{ name = "kit/default", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/107/shared" },
	{ name = "kit/sdk", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/sdk/${kit_version_short}/${kit_git_hash}" },
	{ name = "kit/community", url = "https://dw290v42wisod.cloudfront.net/exts/kit/community" },
]
