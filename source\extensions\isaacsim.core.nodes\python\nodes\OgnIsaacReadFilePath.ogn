{"IsaacReadFilePath": {"version": 1, "icon": "icons/isaac-sim.svg", "description": "Loads contents of file when given path, if file exists", "language": "Python", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {"uiName": "<PERSON> File Path"}, "inputs": {"path": {"type": "path", "description": "Input path to file", "metadata": {"uiName": "Input Path"}, "default": ""}}, "outputs": {"fileContents": {"type": ["string", "token"], "description": "Output contents of file at path, returns empty string if file is not found", "metadata": {"uiName": "Output File Contents"}, "default": ""}}}}