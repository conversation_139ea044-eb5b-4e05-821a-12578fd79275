[package]
version = "2.0.5"
category = "Simulation"
title = "Isaac Sim Manipulators UI"
description = "Manipulators"
keywords = ["isaac", "manipulators", "ui"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation] 
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot.manipulators.ui"

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.robot.manipulators.ui" = {}

[[python.module]]
name = "omni.isaac.manipulators.ui"

[[test]]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
