{
    "editor.rulers": [120],
    "typescript.tsc.autoDetect": "off",
    "grunt.autoDetect": "off",
    "jake.autoDetect": "off",
    "gulp.autoDetect": "off",
    "npm.autoDetect": "off",
    "spellright.language": [
        "en"
    ],
    "spellright.documentTypes": [
        "markdown",
        "latex",
        "plaintext",
        "cpp",
        "asciidoc"
    ],

    // This enables python language server. Seems to work slightly better than jedi:
    "python.jediEnabled": false,

    // Those paths are automatically filled by build system, see `repo.toml` for configuration:
    "python.analysis.extraPaths": [],

    "python.languageServer": "Pylance",
    "python.defaultInterpreterPath": "${workspaceFolder}/kit/python/python.exe",
    // python.pythonPath is deprecated
    // "python.pythonPath": "${workspaceFolder}/kit/python/bin/python3",


    // This enables python language server. Seems to work slightly better than jedi:
    "python.jediEnabled": false, 

    // We use "black" as a formatter:
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "120"],

    // Use flake8 for linting
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.languageServer": "Pylance"
}
