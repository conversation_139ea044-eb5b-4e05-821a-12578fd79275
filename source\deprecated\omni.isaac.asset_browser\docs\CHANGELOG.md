# Changelog
## [1.0.6] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.0.5] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.0.4] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.0.3] - 2025-03-25
### Changed
- Add import tests for deprecated extensions

## [1.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-09-23
### Changed

- Extension deprecated since <PERSON> Si<PERSON> 4.5.0. Replaced by isaacsim.asset.browser

## [0.5.2] - 2024-09-23
### Changed

- Update asset paths to 4.5

## [0.5.1] - 2024-08-05
### Changed

- Update asset paths to 4.2

## [0.5.0] - 2024-06-30
### Changed

- Update asset paths to 4.1

## [0.4.6] - 2024-05-20
### Added

- Add IsaacLab folder

## [0.4.5] - 2024-04-15
### Fixed

- Unit test

## [0.4.4] - 2024-01-31
### Changed

- Update asset paths to 4.0

## [0.4.3] - 2023-12-12
### Changed

- Disable test when windowing is not present

## [0.4.2] - 2023-11-29
### Changed

- Updated golden image

## [0.4.1] - 2023-11-15
### Changed

- Fix missing default icon

## [0.4.0] - 2023-11-13
### Changed

- Update based on omni.kit.browser.asset-1.3.7

## [0.3.2] - 2023-11-09
### Changed

- Update asset paths to 2023.1.1

## [0.3.1] - 2023-08-03
### Changed

- Add Materials folder

## [0.3.0] - 2023-03-31
### Changed

- Update asset paths to 2023.1.0

## [0.2.4] - 2023-02-14
### Changed

- Add Sensors folder

## [0.2.3] - 2023-01-06
### Changed

- Update asset paths to 2022.2.1

## [0.2.2] - 2022-12-12
### Changed

- Update asset paths to 2022.2.0

## [0.2.1] - 2022-12-10
### Added
- Added People folder

## [0.2.0] - 2022-09-02
### Changed
- Use new viewport iterfaces

## [0.1.1] - 2022-08-31
### Changed
- Update paths to 2022.2

## [0.1.0] - 2022-04-27
### Added
- First Release
