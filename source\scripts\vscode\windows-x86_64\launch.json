{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "env": {
                "RESOURCE_NAME": "IsaacSim",
                "CARB_APP_PATH": "${workspaceFolder}/kit",
                "ISAAC_PATH": "${workspaceFolder}",
                "EXP_PATH": "${workspaceFolder}/apps",
                "PYTHONPATH": "${workspaceFolder}/site"
            },
            "python": "${workspaceFolder}/kit/python/python.exe",
        },
        {
            "name": "Python: Attach (windows-x86_64/linux-x86_64)",
            "type": "python",
            "request": "attach",
            "port": 3000,
            "host": "localhost"
        },
        {
            "name": "(windows-x86_64) isaac-sim [release]",
            "type": "cppvsdbg",
            "request": "launch",
            "program": "${workspaceFolder}/kit/kit.exe",
            "args": ["${workspaceFolder}/apps/isaacsim.exp.full.kit",
                "--ext-folder", "${workspaceFolder}/apps"],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "internalConsoleOptions": "openOnSessionStart",
        },
    ]
}