{"comment1": "parameters obtained from https://data.ouster.io/downloads/datasheets/datasheet-rev7-v3p0-os1.pdf", "comment2": "parameters without comments are assumed correct, but will ultimately need checking", "class": "sensor", "type": "lidar", "name": "OS1 REV7 128 20hz @ 512 resolution", "comment3": "Ouster OS1 REV7 128 channels @ 20Hz 512 horizontal resolution", "driveWorksId": "GENERIC", "profile": {"scanType": "rotary", "intensityProcessing": "normalization", "rotationDirection": "CW", "rayType": "IDEALIZED", "nearRangeM": 0.5, "comment4": "OPTICAL PERFORMANCE-Minimum Range 0.3 m for point cloud data", "farRangeM": 170.0, "comment5": "OPTICAL PERFORMANCE- Range 17 m @ >90% detection probability, 100 klx sunlight", "startAzimuthDeg": 0.0, "comment6": "x+ is 0°  y+ is 90° (and z is up, right handed)", "endAzimuthDeg": 360.0, "comment7": "OPTICAL PERFORMANCE- Field of View Horizontal: 360°", "upElevationDeg": 22.5, "comment8": "OPTICAL PERFORMANCE- Field of View Vertical: 45° (+22.5° to -22.5°)", "downElevationDeg": -22.5, "rangeResolutionM": 0.001, "comment9": "OPTICAL PERFORMANCE- Range Resolution 0.1 cm", "rangeAccuracyM": 0.025, "comment10": "OPTICAL PERFORMANCE- Range Accuracy Chart Min ±2.5 cm, Max ±5 cm", "avgPowerW": 0.002, "minReflectance": 0.1, "comment11": "OPTICAL PERFORMANCE - Range (10% Lambertian reflectivity, 1024 @ 10 Hz mode)", "minReflectanceRange": 90.0, "comment12": "OPTICAL PERFORMANCE - Range 90 m @ >90% detection probability, 100 klx sunlight", "wavelengthNm": 865.0, "comment13": "LASER-  Laser Wavelength 865 nm", "pulseTimeNs": 6, "comment14": "These add noise to the emitter direction to each point randomly if Std is not 0.0", "azimuthErrorMean": 0.0, "azimuthErrorStd": 0.01, "comment15": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Horizontal: ±0.01°", "elevationErrorMean": 0.0, "elevationErrorStd": 0.01, "comment16": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Vertical: ±0.01°", "maxReturns": 2, "scanRateBaseHz": 20.0, "reportRateBaseHz": 10240, "numberOfEmitters": 128, "emitterStateCount": 1, "emitterStates": [{"comment19": "beam_azimuth_angles from get_beam_intrinsics", "azimuthDeg": [4.2, 1.39, -1.41, -4.2, 4.21, 1.41, -1.4, -4.2, 4.21, 1.41, -1.4, -4.2, 4.21, 1.4, -1.39, -4.2, 4.21, 1.4, -1.4, -4.21, 4.21, 1.4, -1.4, -4.2, 4.2, 1.4, -1.4, -4.21, 4.2, 1.4, -1.39, -4.21, 4.2, 1.39, -1.4, -4.21, 4.21, 1.4, -1.4, -4.21, 4.2, 1.4, -1.4, -4.21, 4.2, 1.4, -1.4, -4.22, 4.21, 1.4, -1.4, -4.22, 4.2, 1.41, -1.42, -4.22, 4.2, 1.42, -1.41, -4.21, 4.21, 1.41, -1.41, -4.21, 4.21, 1.41, -1.41, -4.21, 4.21, 1.41, -1.41, -4.21, 4.22, 1.41, -1.4, -4.21, 4.21, 1.4, -1.4, -4.21, 4.22, 1.41, -1.4, -4.22, 4.21, 1.41, -1.4, -4.21, 4.22, 1.41, -1.4, -4.21, 4.23, 1.41, -1.4, -4.21, 4.22, 1.41, -1.4, -4.22, 4.22, 1.41, -1.4, -4.21, 4.23, 1.41, -1.39, -4.22, 4.22, 1.41, -1.39, -4.22, 4.23, 1.4, -1.4, -4.22, 4.22, 1.4, -1.4, -4.22, 4.22, 1.4, -1.4, -4.22, 4.22, 1.39, -1.4, -4.23], "comment20": "beam_altitude_angles from get_beam_intrinsics", "elevationDeg": [20.93, 20.67, 20.36, 20, 19.72, 19.45, 19.12, 18.77, 18.47, 18.19, 17.87, 17.51, 17.2, 16.92, 16.6, 16.24, 15.92, 15.64, 15.31, 14.94, 14.63, 14.32, 14, 13.63, 13.31, 13, 12.67, 12.3, 11.96, 11.66, 11.33, 10.96, 10.62, 10.3, 9.97, 9.61, 9.26, 8.95, 8.6, 8.24, 7.89, 7.57, 7.23, 6.87, 6.53, 6.2, 5.85, 5.48, 5.14, 4.8, 4.46, 4.09, 3.75, 3.41, 3.05, 2.7, 2.36, 2.02, 1.65, 1.3, 0.96, 0.62, 0.25, -0.09, -0.44, -0.79, -1.15, -1.49, -1.84, -2.18, -2.54, -2.88, -3.23, -3.58, -3.94, -4.28, -4.63, -4.99, -5.33, -5.67, -6.02, -6.38, -6.72, -7.07, -7.4, -7.76, -8.1, -8.44, -8.78, -9.14, -9.48, -9.8, -10.13, -10.51, -10.84, -11.17, -11.49, -11.86, -12.19, -12.51, -12.83, -13.2, -13.53, -13.84, -14.16, -14.54, -14.85, -15.15, -15.48, -15.85, -16.16, -16.46, -16.77, -17.15, -17.45, -17.75, -18.06, -18.42, -18.73, -19.01, -19.31, -19.67, -19.98, -20.25, -20.55, -20.92, -21.22, -21.48], "fireTimeNs": [381, 1143, 1905, 2667, 3429, 4191, 4953, 5715, 6477, 7239, 8001, 8763, 9525, 10287, 11049, 11811, 12573, 13335, 14097, 14859, 15621, 16383, 17145, 17907, 18669, 19431, 20193, 20955, 21717, 22479, 23241, 24003, 24765, 25527, 26289, 27051, 27813, 28575, 29337, 30099, 30861, 31623, 32385, 33147, 33909, 34671, 35433, 36195, 36957, 37719, 38481, 39243, 40005, 40767, 41529, 42291, 43053, 43815, 44577, 45339, 46101, 46863, 47625, 48387, 49149, 49911, 50673, 51435, 52197, 52959, 53721, 54483, 55245, 56007, 56769, 57531, 58293, 59055, 59817, 60579, 61341, 62103, 62865, 63627, 64389, 65151, 65913, 66675, 67437, 68199, 68961, 69723, 70485, 71247, 72009, 72771, 73533, 74295, 75057, 75819, 76581, 77343, 78105, 78867, 79629, 80391, 81153, 81915, 82677, 83439, 84201, 84963, 85725, 86487, 87249, 88011, 88773, 89535, 90297, 91059, 91821, 92583, 93345, 94107, 94869, 95631, 96393, 97155]}], "intensityMappingType": "LINEAR"}}