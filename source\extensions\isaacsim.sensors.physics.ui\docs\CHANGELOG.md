# Changelog
## [0.1.9] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [0.1.8] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.1.7] - 2025-05-10
### Changed
- Enable FSD in test settings

## [0.1.6] - 2025-04-09
### Changed
- Update all test args to be consistent

## [0.1.5] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [0.1.4] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [0.1.3] - 2025-02-14
### Added
- Add Contact and IMU sensors to Viewport and Stage Context Menus

## [0.1.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [0.1.1] - 2024-12-12
### Fixed
- Restores missing methods to add IMU and Contact sensors to scene

## [0.1.0] - 2024-12-10
### Added
- Initial version of isaacsim.sensors.physics.ui
