# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - joint_1
    - joint_2
    - joint_3
    - joint_4
    - joint_5
    - joint_6
default_q: [
    0.0,0.3,1.2,0.0,0.0,0.0
]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:
    - {name: finger_joint, rule: fixed, value: 0.0}
    - {name: left_inner_knuckle_joint, rule: fixed, value: 0.0}
    - {name: right_inner_knuckle_joint, rule: fixed, value: 0.0}
    - {name: right_outer_knuckle_joint, rule: fixed, value: 0.0}
    - {name: left_inner_finger_joint, rule: fixed, value: 0.0}
    - {name: right_inner_finger_joint, rule: fixed, value: 0.0}

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - J1:
    - "center": [0.0, 0.0, 0.1]
      "radius": 0.08
    - "center": [0.0, 0.0, 0.15]
      "radius": 0.08
    - "center": [0.0, 0.0, 0.2]
      "radius": 0.08
  - J2:
    - "center": [0.0, 0.08, 0.0]
      "radius": 0.08
    - "center": [0.0, 0.174, 0.0]
      "radius": 0.08
    - "center": [-0.0, 0.186, 0.05]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.1]
      "radius": 0.065
    - "center": [-0.0, 0.18, 0.15]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.2]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.25]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.3]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.35]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.4]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.45]
      "radius": 0.065
    - "center": [0.0, 0.175, 0.5]
      "radius": 0.065
    - "center": [-0.002, 0.1, 0.507]
      "radius": 0.07
  - J3:
    - "center": [0.0, 0.025, 0.0]
      "radius": 0.065
    - "center": [0.0, -0.025, 0.0]
      "radius": 0.065
    - "center": [0.0, -0.025, 0.05]
      "radius": 0.065
    - "center": [0.0, -0.025, 0.1]
      "radius": 0.065
    - "center": [0.0, -0.025, 0.15]
      "radius": 0.06
    - "center": [0.0, -0.025, 0.2]
      "radius": 0.06
    - "center": [0.0, -0.025, 0.25]
      "radius": 0.06
    - "center": [0.0, -0.025, 0.3]
      "radius": 0.06
    - "center": [0.0, -0.025, 0.35]
      "radius": 0.055
    - "center": [0.0, -0.025, 0.4]
      "radius": 0.055
  - J5:
    - "center": [0.0, 0.05, 0.0]
      "radius": 0.055
    - "center": [0.0, 0.1, 0.0]
      "radius": 0.055
  - J6:
    - "center": [0.0, 0.0, -0.05]
      "radius": 0.05
    - "center": [0.0, 0.0, -0.1]
      "radius": 0.05
    - "center": [0.0, 0.0, -0.15]
      "radius": 0.05
    - "center": [0.0, 0.0, 0.04]
      "radius": 0.035
    - "center": [0.0, 0.0, 0.08]
      "radius": 0.035
    - "center": [0.0, 0.0, 0.12]
      "radius": 0.035
  - right_inner_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.02
    - "center": [0.0, -0.03, 0.025]
      "radius": 0.02
    - "center": [0.0, -0.05, 0.05]
      "radius": 0.02
  - right_inner_finger:
    - "center": [0.0, 0.02, 0.0]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.015]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.03]
      "radius": 0.015
    - "center": [0.0, 0.025, 0.04]
      "radius": 0.01
  - left_inner_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.02
    - "center": [0.0, -0.03, 0.025]
      "radius": 0.02
    - "center": [0.0, -0.05, 0.05]
      "radius": 0.02
  - left_inner_finger:
    - "center": [0.0, 0.02, 0.0]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.015]
      "radius": 0.015
    - "center": [0.0, 0.02, 0.03]
      "radius": 0.015
    - "center": [0.0, 0.025, 0.04]
      "radius": 0.01
