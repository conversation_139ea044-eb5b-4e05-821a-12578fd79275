{"IsaacSetCameraOnRenderProduct": {"version": 1, "icon": "icons/isaac-sim.svg", "description": "Isaac Sim node that sets the camera prim of an existing render product", "language": "Python", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {"uiName": "<PERSON>"}, "inputs": {"execIn": {"type": "execution", "description": "Input execution trigger"}, "renderProductPath": {"type": "token", "description": "Path of the render product"}, "cameraPrim": {"type": "target", "description": "Usd prim reference to the camera associated with this render product"}}, "outputs": {"execOut": {"type": "execution", "description": "Output execution trigger"}}}}