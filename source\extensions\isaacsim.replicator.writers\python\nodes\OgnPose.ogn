{"Pose": {"version": 1, "scheduling": "compute-on-request", "description": "This node outputs the poses of assets with semantic labels", "language": "Python", "metadata": {"uiName": "Pose"}, "inputs": {"exec": {"type": "execution", "description": "<PERSON><PERSON>"}, "includeOccludedPrims": {"type": "bool", "description": "Set to True if poses (and if enabled, centers) of fully occluded/out-of-frame semantic entities should be output"}, "getCenters": {"type": "bool", "description": "Set to True if producing center coordinates of every semantic entity projected in the image space"}, "cameraRotation": {"type": "float[]", "description": "Rotation of the desired camera frame from the default camera frame, as XYZ Euler angles"}, "imageWidth": {"type": "uint", "description": "Width of the viewport"}, "imageHeight": {"type": "uint", "description": "Height of the viewport"}, "cameraViewTransform": {"type": "matrixd[4]", "description": "Camera view matrix"}, "cameraProjection": {"type": "matrixd[4]", "description": "Camera projection matrix"}, "sdIMNumSemantics": {"type": "uint", "description": "Number of semantic entities in the semantic arrays"}, "sdIMNumSemanticTokens": {"type": "uint", "description": "Number of semantics token including the semantic entity path, the semantic entity types and if the number of semantic types is greater than one"}, "sdIMInstanceSemanticMap": {"type": "uchar[]", "description": "Raw array of uint16_t of size sdIMNumInstances*sdIMMaxSemanticHierarchyDepth containing the mapping from the instances index to their inherited semantic entities"}, "sdIMSemanticTokenMap": {"type": "token[]", "description": "Semantic array of token of size numSemantics * numSemanticTypes containing the mapping from the semantic entities to the semantic entity path and semantic types"}, "sdIMMinSemanticIndex": {"type": "uint", "description": "Semantic id of the first instance in the instance arrays"}, "sdIMMaxSemanticHierarchyDepth": {"type": "uint", "description": "Maximal number of semantic entities inherited by an instance"}, "sdIMSemanticWorldTransform": {"type": "float[]", "description": "Semantic array of 4x4 float matrices containing the transform from local to world space for every semantic entity"}, "data": {"type": "uchar[]", "memoryType": "cuda", "description": "Buffer array data", "default": []}, "bufferSize": {"type": "uint", "description": "Size (in bytes) of the buffer (0 if the input is a texture)"}, "semanticTypes": {"type": "token[]", "description": "Semantic Types to consider", "default": ["class"]}}, "outputs": {"exec": {"type": "execution", "description": "<PERSON><PERSON>"}, "data": {"description": "Semantic array of 4x4 float matrices containing the transform from local to view space for every semantic entity. Additionally, an optional semantic array of float[2] vectors containing the center coordinates of every semantic entity projected in the image space", "type": "uchar[]"}, "idToLabels": {"description": "Mapping from id to semantic labels.", "type": "string"}, "primPaths": {"description": "Prim paths corresponding to each pose.", "type": "token[]"}, "bufferSize": {"type": "uint", "description": "Size (in bytes) of the buffer (0 if the input is a texture)"}, "height": {"type": "uint", "description": "Shape of the data"}, "width": {"type": "uint", "description": "Shape of the data"}}}}