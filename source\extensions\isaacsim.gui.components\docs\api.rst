API
===

Python API
----------

.. Summary

.. currentmodule:: isaacsim.gui.components


.. rubric:: *UI Utils (Shared UI Elements)*
.. autosummary::
    :nosignatures:

    ~ui_utils.setup_ui_headers

.. rubric:: *UI Utils (Builder Functions)*
.. autosummary::
    :nosignatures:

    ~ui_utils.btn_builder
    ~ui_utils.state_btn_builder
    ~ui_utils.multi_btn_builder
    ~ui_utils.cb_builder
    ~ui_utils.multi_cb_builder
    ~ui_utils.str_builder
    ~ui_utils.int_builder
    ~ui_utils.float_builder
    ~ui_utils.combo_cb_str_builder
    ~ui_utils.dropdown_builder
    ~ui_utils.multi_dropdown_builder
    ~ui_utils.combo_cb_dropdown_builder
    ~ui_utils.combo_intfield_slider_builder
    ~ui_utils.combo_floatfield_slider_builder
    ~ui_utils.scrolling_frame_builder
    ~ui_utils.combo_cb_scrolling_frame_builder
    ~ui_utils.xyz_builder
    ~ui_utils.color_picker_builder
    ~ui_utils.progress_bar_builder

.. rubric:: *UI Utils (Plotting Functions)*
.. autosummary::
    :nosignatures:

    ~ui_utils.plot_builder
    ~ui_utils.combo_cb_plot_builder
    ~ui_utils.xyz_plot_builder
    ~ui_utils.combo_cb_xyz_plot_builder

.. rubric:: *UI Utils (Aesthetic Functions)*
.. autosummary::
    :nosignatures:

    ~ui_utils.add_separator

.. rubric:: *UI Element Wrappers*
.. autosummary::
    :nosignatures:

    ~element_wrappers.ui_widget_wrappers.Frame
    ~element_wrappers.ui_widget_wrappers.CollapsableFrame
    ~element_wrappers.ui_widget_wrappers.ScrollingFrame
    ~element_wrappers.ui_widget_wrappers.IntField
    ~element_wrappers.ui_widget_wrappers.FloatField
    ~element_wrappers.ui_widget_wrappers.StringField
    ~element_wrappers.ui_widget_wrappers.Button
    ~element_wrappers.ui_widget_wrappers.CheckBox
    ~element_wrappers.ui_widget_wrappers.StateButton
    ~element_wrappers.ui_widget_wrappers.DropDown
    ~element_wrappers.ui_widget_wrappers.ColorPicker
    ~element_wrappers.ui_widget_wrappers.TextBlock
    ~element_wrappers.ui_widget_wrappers.XYPlot

|

.. API

Shared UI Elements
^^^^^^^^^^^^^^^^^^

.. automethod:: isaacsim.gui.components.ui_utils.setup_ui_headers

|

Builder Functions
^^^^^^^^^^^^^^^^^

.. automethod:: isaacsim.gui.components.ui_utils.btn_builder

.. automethod:: isaacsim.gui.components.ui_utils.state_btn_builder

.. automethod:: isaacsim.gui.components.ui_utils.multi_btn_builder

.. automethod:: isaacsim.gui.components.ui_utils.cb_builder

.. automethod:: isaacsim.gui.components.ui_utils.multi_cb_builder

.. automethod:: isaacsim.gui.components.ui_utils.str_builder

.. automethod:: isaacsim.gui.components.ui_utils.int_builder

.. automethod:: isaacsim.gui.components.ui_utils.float_builder

.. automethod:: isaacsim.gui.components.ui_utils.combo_cb_str_builder

.. automethod:: isaacsim.gui.components.ui_utils.dropdown_builder

.. automethod:: isaacsim.gui.components.ui_utils.multi_dropdown_builder

.. automethod:: isaacsim.gui.components.ui_utils.combo_cb_dropdown_builder

.. automethod:: isaacsim.gui.components.ui_utils.combo_intfield_slider_builder

.. automethod:: isaacsim.gui.components.ui_utils.combo_floatfield_slider_builder

.. automethod:: isaacsim.gui.components.ui_utils.scrolling_frame_builder

.. automethod:: isaacsim.gui.components.ui_utils.combo_cb_scrolling_frame_builder

.. automethod:: isaacsim.gui.components.ui_utils.xyz_builder

.. automethod:: isaacsim.gui.components.ui_utils.color_picker_builder

.. automethod:: isaacsim.gui.components.ui_utils.progress_bar_builder

|

Plotting Functions
^^^^^^^^^^^^^^^^^^

.. automethod:: isaacsim.gui.components.ui_utils.plot_builder

.. automethod:: isaacsim.gui.components.ui_utils.combo_cb_plot_builder

.. automethod:: isaacsim.gui.components.ui_utils.xyz_plot_builder

.. automethod:: isaacsim.gui.components.ui_utils.combo_cb_xyz_plot_builder

|

Aesthetic Functions
^^^^^^^^^^^^^^^^^^^

.. automethod:: isaacsim.gui.components.ui_utils.add_separator

|

UI Element Wrappers
^^^^^^^^^^^^^^^^^^^

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.Frame
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.CollapsableFrame
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.ScrollingFrame
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.IntField
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.FloatField
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.StringField
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.Button
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.CheckBox
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.StateButton
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.DropDown
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.ColorPicker
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.TextBlock
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.gui.components.element_wrappers.ui_widget_wrappers.XYPlot
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
