# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot descriptor defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON>la will only use these joints to control the robot position.
cspace:
    - a1
    - a2
    - a3
    - a4
    - a5
    - a6
default_q: [
    0.0,0.0,0.0,-0.0,-0.0,-0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - link_1:
    - "center": [0.0, 0.032, 0.29]
      "radius": 0.11
    - "center": [0.0, 0.073, 0.331]
      "radius": 0.08
  - link_2:
    - "center": [-0.0, 0.024, -0.0]
      "radius": 0.08
    - "center": [-0.0, 0.018, 0.11]
      "radius": 0.07
    - "center": [-0.0, 0.021, 0.051]
      "radius": 0.08
    - "center": [-0.0, 0.315, 0.132]
      "radius": 0.06
    - "center": [-0.0, 0.26, 0.128]
      "radius": 0.062
    - "center": [-0.0, 0.202, 0.124]
      "radius": 0.064
    - "center": [-0.0, 0.143, 0.12]
      "radius": 0.066
    - "center": [-0.0, 0.082, 0.115]
      "radius": 0.068
    - "center": [-0.0, 0.336, 0.057]
      "radius": 0.06
    - "center": [-0.0, 0.326, 0.095]
      "radius": 0.06
  - link_3:
    - "center": [0.0, 0.035, 0.066]
      "radius": 0.06
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.07
    - "center": [0.0, 0.001, 0.034]
      "radius": 0.065
  - link_4:
    - "center": [0.0, -0.0, 0.124]
      "radius": 0.06
    - "center": [-0.0, 0.118, 0.163]
      "radius": 0.07
    - "center": [0.0, 0.037, 0.136]
      "radius": 0.063
    - "center": [-0.0, 0.077, 0.149]
      "radius": 0.066
    - "center": [-0.0, 0.131, 0.315]
      "radius": 0.06
    - "center": [-0.0, 0.122, 0.203]
      "radius": 0.067
    - "center": [-0.0, 0.125, 0.242]
      "radius": 0.065
    - "center": [-0.0, 0.128, 0.279]
      "radius": 0.062
    - "center": [0.0, 0.096, 0.327]
      "radius": 0.05
  - link_5:
    - "center": [-0.0, -0.051, -0.0]
      "radius": 0.06
    - "center": [0.0, 0.068, 0.0]
      "radius": 0.06
    - "center": [-0.0, -0.011, -0.0]
      "radius": 0.06
    - "center": [0.0, 0.029, 0.0]
      "radius": 0.06
    - "center": [-0.0, 0.0, -0.028]
      "radius": 0.06
  - link_6:
    - "center": [0.0, -0.0, 0.106]
      "radius": 0.05
    - "center": [0.017, 0.047, 0.118]
      "radius": 0.02
    - "center": [-0.008, 0.048, 0.12]
      "radius": 0.02
