<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from src/fanuc_experimental/fanuc_crx10ia_support/urdf/crx10ial.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="fanuc_crx10ial">
  <!-- links: main serial chain -->
  <link name="base_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/visual/base_link.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.23921568627450981 0.23921568627450981 0.23921568627450981 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/collision/base_link.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_1">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/visual/link_1.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.86 0.85 0.81 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/collision/link_1.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_2">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/visual/link_2.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.86 0.85 0.81 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/collision/link_2.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_3">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/visual/link_3.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.86 0.85 0.81 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/collision/link_3.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_4">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/visual/link_4.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.86 0.85 0.81 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/collision/link_4.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_5">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/visual/link_5.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.86 0.85 0.81 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/collision/link_5.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_6">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/visual/link_6.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.23921568627450981 0.23921568627450981 0.23921568627450981 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://fanuc_crx10ia_support/meshes/crx10ial/collision/link_6.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joints: main serial chain -->
  <joint name="joint_1" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0.245"/>
    <parent link="base_link"/>
    <child link="link_1"/>
    <axis xyz="0 0 1"/>
    <limit effort="0" lower="-3.141592653589793" upper="3.141592653589793" velocity="2.0943951023931953"/>
  </joint>
  <joint name="joint_2" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="link_1"/>
    <child link="link_2"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-3.141592653589793" upper="3.141592653589793" velocity="2.0943951023931953"/>
  </joint>
  <joint name="joint_3" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0.710"/>
    <parent link="link_2"/>
    <child link="link_3"/>
    <axis xyz="0 -1 0"/>
    <limit effort="0" lower="-4.71238898038469" upper="4.71238898038469" velocity="3.141592653589793"/>
  </joint>
  <joint name="joint_4" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="link_3"/>
    <child link="link_4"/>
    <axis xyz="-1 0 0"/>
    <limit effort="0" lower="-3.3161255787892263" upper="3.3161255787892263" velocity="3.141592653589793"/>
  </joint>
  <joint name="joint_5" type="revolute">
    <origin rpy="0 0 0" xyz="0.540 -0.150 0"/>
    <parent link="link_4"/>
    <child link="link_5"/>
    <axis xyz="0 -1 0"/>
    <limit effort="0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.141592653589793"/>
  </joint>
  <joint name="joint_6" type="revolute">
    <origin rpy="0 0 0" xyz="0.160 0 0"/>
    <parent link="link_5"/>
    <child link="link_6"/>
    <axis xyz="-1 0 0"/>
    <limit effort="0" lower="-3.3161255787892263" upper="3.3161255787892263" velocity="3.141592653589793"/>
  </joint>
  <!-- ROS-Industrial 'base' frame: base_link to Fanuc World Coordinates transform -->
  <link name="base"/>
  <joint name="base_link-base" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.245"/>
    <parent link="base_link"/>
    <child link="base"/>
  </joint>
  <!-- ROS-Industrial 'flange' frame: attachment point for EEF models -->
  <link name="flange"/>
  <joint name="joint_6-flange" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="link_6"/>
    <child link="flange"/>
  </joint>
  <!-- ROS-Industrial 'tool0' frame: all-zeros tool frame -->
  <link name="tool0"/>
  <joint name="link_6-tool0" type="fixed">
    <origin rpy="3.141592653589793 -1.5707963267948966 0" xyz="0 0 0"/>
    <parent link="flange"/>
    <child link="tool0"/>
  </joint>
</robot>

