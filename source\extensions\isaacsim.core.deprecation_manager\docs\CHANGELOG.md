# Changelog
## [0.2.7] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.2.6] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [0.2.5] - 2025-04-30
### Changed
- Update event subscriptions to Event 2.0 system

## [0.2.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [0.2.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [0.2.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [0.2.1] - 2024-10-25
### Fixed
- Fix referenced USD asset paths listing order and use absolute paths

## [0.2.0] - 2024-10-24
### Added
- List referenced USD asset paths containing deprecated OmniGraph nodes in the opened stage

## [0.1.2] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [0.1.1] - 2024-09-26
### Fixed
- Graphs were not functional after renaming types, force reload all graphs if naming changes were made

## [0.1.0] - 2024-09-22
### Added
- Initial release
