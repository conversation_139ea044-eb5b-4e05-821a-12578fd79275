{"IsaacReadWorldPose": {"version": 2, "icon": "icons/isaac-sim.svg", "description": "<PERSON> node that reads world pose of an xform", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {"uiName": "<PERSON> Pose"}, "inputs": {"prim": {"type": "target", "description": "Usd prim reference from which fabric pose will be read"}, "includeScale": {"type": "bool", "description": "If True the output bundle would include scale", "uiName": "include scale", "default": false}}, "outputs": {"primsBundle": {"type": "bundle", "description": ["An output bundle containing xformOp data"]}}}}