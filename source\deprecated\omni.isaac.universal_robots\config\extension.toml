[package]
version = "1.0.6"
category = "Simulation"
title = "Isaac Universal Robots"
description = "Isaac Universal Robots Helper Class"
keywords = ["isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot.manipulators.examples"

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.robot.manipulators.examples" = {}

[[python.module]]
name = "omni.isaac.universal_robots"

[[test]]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
