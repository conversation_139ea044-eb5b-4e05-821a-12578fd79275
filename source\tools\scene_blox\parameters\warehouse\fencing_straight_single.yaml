# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

root_prim: fences
generated:
  - name: fence_a
    path: fence_a
    usd_config:
      root: /NVIDIA/Assets/ArchVis/Industrial/Railing/
      search_depth: 0
      filter: MetalFencing_A2.usd
    semantic: "fence"
    position:
      base: [0, -2.20, 0]
    orientation:
      base: [0, 0, 0]
    scale: [0.01, 0.01, 0.01]
    physics:
      apply_children: false
      collision: "convexDecomposition"
      rigid_body: false
  - name: fence_b
    path: fence_b
    usd_config:
      root: /NVIDIA/Assets/ArchVis/Industrial/Railing/
      search_depth: 0
      filter: MetalFencing_A2.usd
    semantic: "fence"
    position:
      base: [-1.0, -2.20, 0]
    orientation:
      base: [0, 0, 0]
    scale: [0.01, 0.01, 0.01]
    physics:
      apply_children: false
      collision: "convexDecomposition"
      rigid_body: false
  - name: fence_c
    path: fence_c
    usd_config:
      root: /NVIDIA/Assets/ArchVis/Industrial/Railing/
      search_depth: 0
      filter: MetalFencing_A2.usd
    semantic: "fence"
    position:
      base: [0, 2.20, 0]
    orientation:
      base: [0, 0, 0]
    scale: [0.01, 0.01, 0.01]
    physics:
      apply_children: false
      collision: "convexDecomposition"
      rigid_body: false
  - name: fence_d
    path: fence_d
    usd_config:
      root: /NVIDIA/Assets/ArchVis/Industrial/Railing/
      search_depth: 0
      filter: MetalFencing_A2.usd
    semantic: "fence"
    position:
      base: [-1.0, 2.20, 0]
    orientation:
      base: [0, 0, 0]
    scale: [0.01, 0.01, 0.01]
    physics:
      apply_children: false
      collision: "convexDecomposition"
      rigid_body: false