[package]
version = "2.0.5"
category = "Simulation"
title = "Wheeled Robots UI"
description = "This extension provides the UI elements for the wheeled_robots extension"
keywords = ["isaac", "physics", "analyze", "tune", "ui"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation] 
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot.wheeled_robots.ui"

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.robot.wheeled_robots.ui" = {}

[[python.module]]
name = "omni.isaac.wheeled_robots.ui"

[[test]]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
