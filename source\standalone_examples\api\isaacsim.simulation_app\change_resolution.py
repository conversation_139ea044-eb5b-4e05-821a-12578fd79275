# SPDX-FileCopyrightText: Copyright (c) 2020-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import random

from isaacsim import SimulationApp

# Simple example showing how to change resolution
kit = SimulationApp({"headless": True})
kit.update()
for i in range(100):
    width = random.randint(128, 1980)
    height = random.randint(128, 1980)
    kit.set_setting("/app/renderer/resolution/width", width)
    kit.set_setting("/app/renderer/resolution/height", height)
    kit.update()
    print(f"resolution set to: {width}, {height}")

# cleanup
kit.close()
