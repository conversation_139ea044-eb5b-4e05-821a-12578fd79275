[package]
version = "2.0.8"
category = "SyntheticData"
title = "Replicator Isaac"
description="Omni Replicator Isaac Sim Extension"
keywords = ["Synthetic", "Data", "Randomization", "Generator", "Domain Randomization", "DR"]
changelog="docs/CHANGELOG.md"
readme  = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.replicator.domain_randomization, isaacsim.replicator.examples, isaacsim.replicator.writers."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.replicator.domain_randomization" = {}
"isaacsim.replicator.examples" = {}
"isaacsim.replicator.writers" = {}

[[python.module]]
name = "omni.replicator.isaac"

[[python.module]]
name = "omni.replicator.isaac.tests"

[[test]]
dependencies = [
   "omni.isaac.core",
   "omni.isaac.nucleus"
]
args = [
'--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
'--/persistent/omni/replicator/captureOnPlay = true',
]

[[test]]
name = "startup"
args = [
    '--/app/settings/fabricDefaultStageFrameHistoryCount = 3',
]
