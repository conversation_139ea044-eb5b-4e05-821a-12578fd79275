# Changelog
## [1.1.3] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.1.2] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.1.1] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [1.1.0] - 2025-03-06
### Added
- Introduced new widget for setting the isaac:namespace attribute

## [1.0.4] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.0.3] - 2025-01-07
### Fixed
- Dual Name Override Menu entries

## [1.0.2] - 2024-11-19
### Fixed
- Startup test

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-09-27
### Changed
- Renamed extension to isaacsim.gui.property

## [0.2.3] - 2024-03-22
### Fixed
- Prim Custom Data field can support nested objects (dictionaries) now

## [0.2.2] - 2023-11-14
### Changed
- Prim Custom Data field can support arrays now

## [0.2.1] - 2023-06-12
### Changed
- Update to kit 105.1
- python 3.11 super().__init__ added

## [0.2.0] - 2023-01-21
### Added
- Name Override widget

## [0.1.1] - 2020-10-09
### Changed
- file structure

## [0.1.0] - 2020-07-08
### Added
- Initial version, supports arrays and json
