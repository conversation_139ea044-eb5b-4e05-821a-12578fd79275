[package]
version = "1.0.6"
category = "Simulation"
title = "Isaac Sim UI Example"
description = "Example with all the Core UI Elements in Isaac Sim"
keywords = ["isaac", "example", "ui",]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.examples.ui."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.examples.ui" = {}

[[python.module]]
name = "omni.isaac.ui_template"
