[package]
version = "1.0.9"
category = "Simulation"
title = "Isaac USD to URDF exporter"
description = "Extension that exports USD files to URDF"
keywords = ["isaac", "URDF"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.asset.exporter.urdf."

[dependencies]
"isaacsim.asset.exporter.urdf" = {}
"isaacsim.core.deprecation_manager" = {}

[[python.module]]
name = "omni.exporter.urdf"

[[python.module]]
name = "omni.exporter.urdf.tests"
