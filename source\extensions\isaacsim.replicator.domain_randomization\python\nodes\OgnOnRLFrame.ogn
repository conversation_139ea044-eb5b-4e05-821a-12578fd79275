{"OgnOnRLFrame": {"description": ["Triggered every frame in an Rl setting"], "version": 1, "uiName": "On Frame", "language": "Python", "scheduling": "compute-on-request", "categories": {"Replicator": "On Frame"}, "state": {}, "inputs": {"run": {"type": "bool", "description": "Run"}, "num_envs": {"type": "int", "description": "number of RL environments"}}, "outputs": {"execOut": {"type": "execution", "description": "Output Execution"}, "resetInds": {"description": "indices of environments to be reset", "type": "int[]"}, "frameNum": {"description": "frame number for every environment", "type": "int[]"}}}}