# Changelog
## [2.0.6] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.0.5] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [2.0.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.0.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-09-23
### Changed
- Extension rename to isaacsim.core.version

## [1.1.0] - 2024-05-13
### Added
- Get the version file path from the ISAAC_PATH environment variable first
- Validate if the version file exists and returns empty version values if not

## [1.0.3] - 2023-09-11
### Fixed
- Scan for test modules instead of importing them

## [1.0.2] - 2023-08-22
### Fixed
- Docs and docstrings

## [1.0.1] - 2023-08-11
### Fixed
- Fixed closing of parsed file for reading the version

## [1.0.0] - 2022-05-12
### Added
- Added first version of extension.
