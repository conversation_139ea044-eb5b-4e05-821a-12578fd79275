[package]
title = "Isaac Sim"
description = "Main Omniverse Isaac Sim Application"
version = "5.0.0"
execFile = "isaac-sim.fabric"
keywords = ["experience", "app", "usd", "isaacsim"] # That makes it browsable in UI with "experience" filter


[dependencies]
"isaacsim.exp.full" = {}
"omni.physx.fabric" = {}
"omni.physics.physx" = {}
"omni.physics.stageupdate" = {}
"usdrt.scenegraph" = {}

[settings]
app.name = "Isaac-Sim Fabric"
app.version = "5.0.0"
app.vulkan = true # Explicitly enable Vulkan (on by default on Linux, off by default on Windows)
app.enableDeveloperWarnings = false # disable developer warnings to reduce log noise

[settings.persistent]
omnigraph.updateToUsd = false
omnihydra.useFastSceneDelegate = true
omnihydra.useSceneGraphInstancing = true
physics.updateForceSensorsToUsd = false
physics.visualizationDisplayJoints = false

[settings.app.exts.folders]
'++' = [
    "${app}",
    "${app}/../exts",
    "${app}/../extscache",
    "${app}/../extsUser",
    "${app}/../extsDeprecated",
]

[settings.exts."omni.kit.registry.nucleus"]
registries = [
	{ name = "kit/default", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/107/shared" },
	{ name = "kit/sdk", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/sdk/${kit_version_short}/${kit_git_hash}" },
	{ name = "kit/community", url = "https://dw290v42wisod.cloudfront.net/exts/kit/community" },
]
