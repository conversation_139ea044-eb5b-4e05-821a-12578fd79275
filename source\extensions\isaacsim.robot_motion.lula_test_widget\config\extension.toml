[package]
version = "1.0.11"
category = "Simulation"
title = "Lula Test Widget"
description = "Run Simple Tests Using Lula Algorithms"
keywords = ["isaac", "lula"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.gui.components" = {}
"isaacsim.robot_motion.lula" = {}
"isaacsim.robot_motion.motion_generation" = {}
"omni.kit.uiapp" = {}

[[python.module]]
name = "isaacsim.robot_motion.lula_test_widget"
