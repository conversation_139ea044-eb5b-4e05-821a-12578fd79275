{
    "files.associations": {
        "array": "cpp",
        "atomic": "cpp",
        "*.tcc": "cpp",
        "bitset": "cpp",
        "cctype": "cpp",
        "cfenv": "cpp",
        "chrono": "cpp",
        "cinttypes": "cpp",
        "clocale": "cpp",
        "cmath": "cpp",
        "complex": "cpp",
        "condition_variable": "cpp",
        "csetjmp": "cpp",
        "csignal": "cpp",
        "cstdarg": "cpp",
        "cstddef": "cpp",
        "cstdint": "cpp",
        "cstdio": "cpp",
        "cstdlib": "cpp",
        "cstring": "cpp",
        "ctime": "cpp",
        "cwchar": "cpp",
        "cwctype": "cpp",
        "deque": "cpp",
        "forward_list": "cpp",
        "list": "cpp",
        "unordered_map": "cpp",
        "unordered_set": "cpp",
        "vector": "cpp",
        "exception": "cpp",
        "optional": "cpp",
        "string_view": "cpp",
        "rope": "cpp",
        "slist": "cpp",
        "fstream": "cpp",
        "functional": "cpp",
        "future": "cpp",
        "initializer_list": "cpp",
        "iomanip": "cpp",
        "iosfwd": "cpp",
        "iostream": "cpp",
        "istream": "cpp",
        "limits": "cpp",
        "mutex": "cpp",
        "new": "cpp",
        "ostream": "cpp",
        "numeric": "cpp",
        "ratio": "cpp",
        "scoped_allocator": "cpp",
        "shared_mutex": "cpp",
        "sstream": "cpp",
        "stdexcept": "cpp",
        "streambuf": "cpp",
        "system_error": "cpp",
        "thread": "cpp",
        "regex": "cpp",
        "tuple": "cpp",
        "type_traits": "cpp",
        "utility": "cpp",
        "typeindex": "cpp",
        "typeinfo": "cpp",
        "valarray": "cpp",
        "memory": "cpp",
        "map": "cpp",
        "*.idl": "cpp",
        "algorithm": "cpp",
        "codecvt": "cpp",
        "concepts": "cpp",
        "filesystem": "cpp",
        "ios": "cpp",
        "iterator": "cpp",
        "locale": "cpp",
        "memory_resource": "cpp",
        "queue": "cpp",
        "random": "cpp",
        "set": "cpp",
        "stack": "cpp",
        "string": "cpp",
        "variant": "cpp",
        "xfacet": "cpp",
        "xhash": "cpp",
        "xiosbase": "cpp",
        "xlocale": "cpp",
        "xlocbuf": "cpp",
        "xlocinfo": "cpp",
        "xlocmes": "cpp",
        "xlocmon": "cpp",
        "xlocnum": "cpp",
        "xloctime": "cpp",
        "xmemory": "cpp",
        "xstddef": "cpp",
        "xstring": "cpp",
        "xtr1common": "cpp",
        "xtree": "cpp",
        "xutility": "cpp",
        "resumable": "cpp",
        "xthread": "cpp",
        "xmemory0": "cpp",
        "hash_map": "cpp",
        "hash_set": "cpp",
        "*.kit": "toml",
        "*.ogn": "json"
    },
    "editor.rulers": [120],
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/**": true,
        "**/_build/**": true,
        "**/_compiler/**": true
    },
    "typescript.tsc.autoDetect": "off",
    "grunt.autoDetect": "off",
    "jake.autoDetect": "off",
    "gulp.autoDetect": "off",
    "npm.autoDetect": "off",
    "spellright.language": [
        "en"
    ],
    "spellright.documentTypes": [
        "markdown",
        "latex",
        "plaintext",
        "cpp",
        "asciidoc"
    ],

    // This enables python language server. Seems to work slightly better than jedi:
    "python.jediEnabled": false,
    "python.languageServer": "Pylance",

    // Those paths are automatically filled by build system, see `repo.toml` for configuration:
    "python.analysis.extraPaths": [
    ],

    "python.defaultInterpreterPath": "${workspaceFolder}/_build/linux-x86_64/release/kit/python/bin/python3",
    // python.pythonPath is deprecated
    // "python.pythonPath": "${workspaceFolder}/_build/linux-x86_64/release/kit/python/bin/python3",

    // We use "black" as a formatter:
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "120"],

    // Use flake8 for linting
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
}
