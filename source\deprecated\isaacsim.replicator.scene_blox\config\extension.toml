[package]
version = "1.0.8"
category = "SyntheticData"
title = "Isaac Sim Replicator Scene Blox"
description = "The extension implements SceneBlox, a rule-based randomized scene generation workflow using the wave function collapse algorithm."
keywords = ["isaac", "omniverse", "scene blox"]
changelog = "docs/CHANGELOG.md"
readme  = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 5.0.0."

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.gui.components" = {}
"isaacsim.storage.native" = {}
"omni.kit.uiapp" = {}

[[python.module]]
name = "isaacsim.replicator.scene_blox"
