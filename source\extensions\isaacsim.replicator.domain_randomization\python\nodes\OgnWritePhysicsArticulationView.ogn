{"OgnWritePhysicsArticulationView": {"version": 1, "language": "python", "categories": {"Replicator": "Write Attribute"}, "description": "This node writes physics attributes to TensorAPI views", "metadata": {"uiName": "Write Physics Attribute using Tensor API"}, "inputs": {"prims": {"description": ["Name of registered view to randomize"], "type": "string"}, "attribute": {"description": ["Name of attribute that is to be written"], "type": "string"}, "operation": {"description": ["Type of randomization operation to be applied"], "type": "string"}, "distribution": {"description": ["Type of distribution used to sample values"], "type": "string"}, "dist_param_1": {"description": ["Distribution parameter 1"], "type": "float[]"}, "dist_param_2": {"description": ["Distribution parameter 2"], "type": "float[]"}, "num_buckets": {"description": ["Number of buckets to randomize from"], "type": "int"}, "values": {"description": ["Values to be assigned to the physics attribute"], "type": "float[]"}, "indices": {"description": ["Indices of the environments to assign the physics attribute"], "type": "int[]"}, "on_reset": {"description": ["indicates whether an on_reset context triggered the execution"], "type": "bool"}, "execIn": {"description": ["exec"], "type": "execution"}}, "outputs": {"execOut": {"description": ["exec"], "type": "execution"}}, "exclude": ["tests"]}}