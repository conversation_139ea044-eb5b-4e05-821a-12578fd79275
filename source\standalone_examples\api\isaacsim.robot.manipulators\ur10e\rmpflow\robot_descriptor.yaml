# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - shoulder_pan_joint
    - shoulder_lift_joint
    - elbow_joint
    - wrist_1_joint
    - wrist_2_joint
    - wrist_3_joint
default_q: [
    0.0,-0.0005,-0.0,-0.0,0.0,-0.0
]

acceleration_limits: [
   10,10,10,10,10,10
]

jerk_limits: [
   10000,10000,10000,10000,10000,10000
]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:
    - {name: finger_joint, rule: fixed, value: 0.0}
    - {name: left_inner_knuckle_joint, rule: fixed, value: 0.0}
    - {name: right_inner_knuckle_joint, rule: fixed, value: -0.0}
    - {name: right_outer_knuckle_joint, rule: fixed, value: -1e-04}
    - {name: left_inner_finger_joint, rule: fixed, value: 1e-04}
    - {name: right_inner_finger_joint, rule: fixed, value: -0.0}

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - ee_link/robotiq_arg2f_base_link:
    - "center": [0.0, 0.0, 0.032]
      "radius": 0.04315
    - "center": [0.0, 0.0, 0.06]
      "radius": 0.03187
  - ee_link/left_outer_knuckle:
    - "center": [-0.0, 0.019, 0.019]
      "radius": 0.01932
    - "center": [-0.0, 0.092, -0.003]
      "radius": 0.01932
    - "center": [-0.0, 0.037, 0.013]
      "radius": 0.01932
    - "center": [-0.0, 0.056, 0.008]
      "radius": 0.01932
    - "center": [-0.0, 0.074, 0.002]
      "radius": 0.01932
  - ee_link/left_inner_finger:
    - "center": [0.001, 0.006, -0.015]
      "radius": 0.01319
    - "center": [-0.001, 0.001, -0.003]
      "radius": 0.01246
    - "center": [-0.0, 0.009, -0.024]
      "radius": 0.01392
    - "center": [0.0, 0.07, -0.026]
      "radius": 0.01319
    - "center": [-0.0, 0.025, -0.025]
      "radius": 0.01374
    - "center": [0.0, 0.04, -0.025]
      "radius": 0.01355
    - "center": [0.0, 0.055, -0.026]
      "radius": 0.01337
  - ee_link/left_inner_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.01559
    - "center": [0.0, 0.095, -0.002]
      "radius": 0.01559
    - "center": [0.0, 0.019, -0.0]
      "radius": 0.01559
    - "center": [0.0, 0.038, -0.001]
      "radius": 0.01559
    - "center": [0.0, 0.057, -0.001]
      "radius": 0.01559
    - "center": [0.0, 0.076, -0.002]
      "radius": 0.01559
  - ee_link/right_inner_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.01559
    - "center": [-0.0, 0.1, -0.001]
      "radius": 0.01559
    - "center": [-0.0, 0.02, -0.0]
      "radius": 0.01559
    - "center": [-0.0, 0.04, -0.0]
      "radius": 0.01559
    - "center": [-0.0, 0.06, -0.0]
      "radius": 0.01559
    - "center": [-0.0, 0.08, -0.0]
      "radius": 0.01559
  - ee_link/right_outer_knuckle:
    - "center": [-0.0, 0.095, 0.0]
      "radius": 0.01932
    - "center": [-0.0, 0.016, 0.023]
      "radius": 0.01932
    - "center": [-0.0, 0.075, 0.006]
      "radius": 0.01932
    - "center": [-0.0, 0.055, 0.012]
      "radius": 0.01932
    - "center": [-0.0, 0.036, 0.017]
      "radius": 0.01932
  - ee_link/right_inner_finger:
    - "center": [-0.002, 0.006, -0.025]
      "radius": 0.01649
    - "center": [-0.001, 0.067, -0.024]
      "radius": 0.01558
    - "center": [-0.001, 0.021, -0.025]
      "radius": 0.01626
    - "center": [-0.001, 0.037, -0.025]
      "radius": 0.01603
    - "center": [-0.001, 0.052, -0.024]
      "radius": 0.0158
  - wrist_3_link:
    - "center": [-0.0, -0.0, -0.049]
      "radius": 0.05313
    - "center": [0.0, 0.0, -0.004]
      "radius": 0.05313
  - wrist_2_link:
    - "center": [0.0, 0.018, -0.004]
      "radius": 0.05473
    - "center": [0.0, 0.0, -0.057]
      "radius": 0.05401
    - "center": [0.0, -0.021, -0.003]
      "radius": 0.05473
  - wrist_1_link:
    - "center": [0.0, -0.008, 0.0]
      "radius": 0.05445
    - "center": [0.0, 0.001, -0.049]
      "radius": 0.05445
    - "center": [0.0, 0.026, -0.0]
      "radius": 0.05295
  - forearm_link:
    - "center": [-0.043, -0.002, 0.039]
      "radius": 0.06466
    - "center": [-0.57, -0.0, 0.039]
      "radius": 0.05447
    - "center": [-0.095, -0.001, 0.039]
      "radius": 0.06366
    - "center": [-0.146, -0.001, 0.039]
      "radius": 0.06268
    - "center": [-0.196, -0.001, 0.039]
      "radius": 0.06171
    - "center": [-0.245, -0.001, 0.039]
      "radius": 0.06075
    - "center": [-0.294, -0.001, 0.039]
      "radius": 0.05981
    - "center": [-0.342, -0.001, 0.039]
      "radius": 0.05889
    - "center": [-0.389, -0.001, 0.039]
      "radius": 0.05798
    - "center": [-0.435, -0.001, 0.039]
      "radius": 0.05708
    - "center": [-0.481, -0.0, 0.039]
      "radius": 0.0562
    - "center": [-0.526, -0.0, 0.039]
      "radius": 0.05533
  - upper_arm_link:
    - "center": [-0.005, -0.001, 0.195]
      "radius": 0.09433
    - "center": [-0.613, -0.0, 0.097]
      "radius": 0.07676
    - "center": [-0.09, -0.0, 0.176]
      "radius": 0.09159
    - "center": [-0.202, -0.0, 0.175]
      "radius": 0.08893
    - "center": [-0.3, -0.0, 0.174]
      "radius": 0.08635
    - "center": [-0.406, -0.0, 0.173]
      "radius": 0.08385
    - "center": [-0.509, -0.0, 0.172]
      "radius": 0.08142
    - "center": [-0.603, -0.0, 0.181]
      "radius": 0.07905
  - shoulder_link:
    - "center": [-0.0, -0.013, 0.012]
      "radius": 0.09494
    - "center": [0.001, -0.109, -0.005]
      "radius": 0.08766
  - world:
    - "center": [0.0, 0.0, 0.107]
      "radius": 0.09932
