# Changelog
## [3.0.5] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [3.0.4] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [3.0.3] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [3.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [3.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [3.0.0] - 2024-10-20
### Deprecated
- Extension deprecated since <PERSON> Sim 4.5.0. Replaced by isaacsim.robot.schema

## [2.1.0] - 2024-06-28
### Added
- Added lightbeam sensor to <PERSON>, bumping version

## [2.0.2] - 2024-04-19
### Changed
- Removed omni.usd.schema.physics dependency

## [2.0.1] - 2023-06-21
### Changed
- Direct inheritance for IsA schema <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>

## [2.0.0] - 2023-06-12
### Changed
- Updated to usd 22.11
- Update to kit 105.1
- Using omni-isaacsim-schema as dependency

## [1.1.0] - 2023-01-21
### Added
- named Override Attribute

## [1.0.0] - 2022-09-29
### Removed
- Remove DR and ROS bridge schemas

## [0.2.0] - 2022-03-21
### Added
- Add Isaac Sensor USD Schema
- Add Contact Sensor USD Schema
- Add IMU Sensor USD Schema

## [0.1.1] - 2021-08-02
### Added
- Add USS material support

## [0.1.0] - 2021-06-03
### Added
- Initial version of Isaac Sim Usd Schema Extension
