<?xml version="1.0" encoding="utf-8"?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from kaya.xacro                     | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="kaya">
  <link name="base_link">
    <visual>
      <geometry>
        <mesh filename="package://meshes/base.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <visual>
      <origin xyz="0.000825 0.077436 0.024927"/>
      <geometry>
        <mesh filename="package://meshes/nano_chassis_base.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <visual>
      <origin xyz="0.001140 0.071549 0.062302"/>
      <geometry>
        <mesh filename="package://meshes/nano_chassis_bridge.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <visual>
      <origin xyz="0.001192 0.079804 0.084978"/>
      <geometry>
        <mesh filename="package://meshes/nano_chassis_rs_mount.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <visual>
      <origin xyz="0.001053 0.090384 0.090843"/>
      <geometry>
        <mesh filename="package://meshes/realsense.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://meshes/base_collision.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
    </inertial>
  </link>
  <link name="assembly_battery">
    <visual>
      <geometry>
        <mesh filename="package://meshes/assembly_battery.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.0000033 -0.00127 0.000241"/>
      <geometry>
        <box size="0.0770 0.1140 0.0486"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00033 -0.00127 0.000241"/>
      <mass value="0.4266108"/>
      <inertia ixx="0.000545989300164" ixy="0" ixz="0" iyy="0.000294751089864" iyz="0" izz="0.0006728007825"/>
    </inertial>
  </link>
  <joint name="assembly_battery_joint" type="fixed">
    <origin xyz="0.000996 0.055389 -0.033654"/>
    <parent link="base_link"/>
    <child link="assembly_battery"/>
  </joint>
  <link name="shock_mount_0">
    <visual>
      <geometry>
        <mesh filename="package://meshes/shock_mount.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
  </link>
  <joint name="shock_mount_0_joint" type="fixed">
    <origin xyz="-0.021377 0.075734 0.073117"/>
    <parent link="base_link"/>
    <child link="shock_mount_0"/>
  </joint>
  <link name="shock_mount_1">
    <visual>
      <geometry>
        <mesh filename="package://meshes/shock_mount.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
  </link>
  <joint name="shock_mount_1_joint" type="fixed">
    <origin xyz=" 0.023644 0.075734 0.073117"/>
    <parent link="base_link"/>
    <child link="shock_mount_1"/>
  </joint>
  <link name="shock_mount_2">
    <visual>
      <geometry>
        <mesh filename="package://meshes/shock_mount.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
  </link>
  <joint name="shock_mount_2_joint" type="fixed">
    <origin xyz=" 0.001141 0.054717 0.073117"/>
    <parent link="base_link"/>
    <child link="shock_mount_2"/>
  </joint>
  <link name="pusher">
    <visual>
      <geometry>
        <mesh filename="package://meshes/pusher.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="        0 -0.000983 -0.047219"/>
      <geometry>
        <box size="0.0290 0.0090 0.0952"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 -0.41888" xyz="-0.049078  0.017466 -0.074854"/>
      <geometry>
        <box size="0.0904 0.0056 0.0394"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0  0.41888" xyz=" 0.049078  0.017466 -0.074854"/>
      <geometry>
        <box size="0.0904 0.0056 0.0394"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 -1.05770" xyz="-0.095612  0.044530 -0.081565"/>
      <geometry>
        <box size="0.0198 0.0056 0.0258"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0  1.05770" xyz=" 0.095612  0.044530 -0.081565"/>
      <geometry>
        <box size="0.0198 0.0056 0.0258"/>
      </geometry>
    </collision>
  </link>
  <joint name="pusher_joint" type="fixed">
    <origin xyz="0.0 0.124025 0.015933"/>
    <parent link="base_link"/>
    <child link="pusher"/>
  </joint>
  <link name="mx12w_0">
    <visual>
      <geometry>
        <mesh filename="package://meshes/mx12w.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.0328 0.0380 0.0502"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.06256928"/>
      <inertia ixx="2.06689273909e-05" ixy="0" ixz="0" iyy="1.87493018805e-05" iyz="0" izz="1.31387145429e-05"/>
    </inertial>
  </link>
  <joint name="mx12w_0_joint" type="fixed">
    <origin rpy="0 0 0" xyz=" 0.000105 -0.025453 -0.032597"/>
    <parent link="base_link"/>
    <child link="mx12w_0"/>
  </joint>
  <link name="axle_0">
    <visual>
      <geometry>
        <mesh filename="package://meshes/axle.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0.0 -0.018622 0.0"/>
      <geometry>
        <cylinder length="0.0448" radius="0.0155"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="1.57079632679 0 0" xyz="0.0 -0.018622 0.0"/>
      <mass value="0.0338135900491"/>
      <inertia ixx="7.68636440001e-06" ixy="0" ixz="0" iyy="7.68636440001e-06" iyz="0" izz="4.06185750465e-06"/>
    </inertial>
  </link>
  <joint name="axle_0_joint" type="continuous">
    <origin xyz="-0.000105 -0.022823 -0.012904"/>
    <axis xyz="0 -1 0"/>
    <parent link="mx12w_0"/>
    <child link="axle_0"/>
  </joint>
  <link name="wheel_0">
    <visual>
      <geometry>
        <mesh filename="package://meshes/wheel_and_rollers.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.0292" radius="0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <mass value="0.131073930817"/>
      <inertia ixx="5.61341585248e-05" ixy="0" ixz="0" iyy="5.61341585248e-05" iyz="0" izz="9.36418376542e-05"/>
    </inertial>
  </link>
  <joint name="wheel_0_joint" type="fixed">
    <origin xyz="-0.000023 -0.019556 -0.000006"/>
    <axis xyz="0 -1 0"/>
    <parent link="axle_0"/>
    <child link="wheel_0"/>
  </joint>
  <link name="roller_0_0_0">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_0_0_joint" type="continuous">
    <origin rpy="0 -0.0 0" xyz="0.0       0.009521       -0.021319"/>
    <parent link="wheel_0"/>
    <child link="roller_0_0_0"/>
  </joint>
  <link name="roller_0_0_1">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_0_1_joint" type="continuous">
    <origin rpy="0 -1.25663706144 0" xyz="0.0202755738709       0.009521       -0.00658793330308"/>
    <parent link="wheel_0"/>
    <child link="roller_0_0_1"/>
  </joint>
  <link name="roller_0_0_2">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_0_2_joint" type="continuous">
    <origin rpy="0 -2.51327412287 0" xyz="0.0125309937936       0.009521       0.0172474333031"/>
    <parent link="wheel_0"/>
    <child link="roller_0_0_2"/>
  </joint>
  <link name="roller_0_0_3">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_0_3_joint" type="continuous">
    <origin rpy="0 -3.76991118431 0" xyz="-0.0125309937936       0.009521       0.0172474333031"/>
    <parent link="wheel_0"/>
    <child link="roller_0_0_3"/>
  </joint>
  <link name="roller_0_0_4">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_0_4_joint" type="continuous">
    <origin rpy="0 -5.02654824574 0" xyz="-0.0202755738709       0.009521       -0.00658793330308"/>
    <parent link="wheel_0"/>
    <child link="roller_0_0_4"/>
  </joint>
  <link name="roller_0_1_0">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_1_0_joint" type="continuous">
    <origin rpy="0 -0.628318530718 0" xyz="0.0125309937936       -0.009521       -0.0172474333031"/>
    <parent link="wheel_0"/>
    <child link="roller_0_1_0"/>
  </joint>
  <link name="roller_0_1_1">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_1_1_joint" type="continuous">
    <origin rpy="0 -1.88495559215 0" xyz="0.0202755738709       -0.009521       0.00658793330308"/>
    <parent link="wheel_0"/>
    <child link="roller_0_1_1"/>
  </joint>
  <link name="roller_0_1_2">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_1_2_joint" type="continuous">
    <origin rpy="0 -3.14159265359 0" xyz="2.6108245111e-18       -0.009521       0.021319"/>
    <parent link="wheel_0"/>
    <child link="roller_0_1_2"/>
  </joint>
  <link name="roller_0_1_3">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_1_3_joint" type="continuous">
    <origin rpy="0 -4.39822971503 0" xyz="-0.0202755738709       -0.009521       0.00658793330308"/>
    <parent link="wheel_0"/>
    <child link="roller_0_1_3"/>
  </joint>
  <link name="roller_0_1_4">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_0_1_4_joint" type="continuous">
    <origin rpy="0 -5.65486677646 0" xyz="-0.0125309937936       -0.009521       -0.0172474333031"/>
    <parent link="wheel_0"/>
    <child link="roller_0_1_4"/>
  </joint>
  <link name="mx12w_1">
    <visual>
      <geometry>
        <mesh filename="package://meshes/mx12w.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.0328 0.0380 0.0502"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.06256928"/>
      <inertia ixx="2.06689273909e-05" ixy="0" ixz="0" iyy="1.87493018805e-05" iyz="0" izz="1.31387145429e-05"/>
    </inertial>
  </link>
  <joint name="mx12w_1_joint" type="fixed">
    <origin rpy="0 0 2.0944" xyz=" 0.065344  0.087794 -0.032597"/>
    <parent link="base_link"/>
    <child link="mx12w_1"/>
  </joint>
  <link name="axle_1">
    <visual>
      <geometry>
        <mesh filename="package://meshes/axle.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0.0 -0.018622 0.0"/>
      <geometry>
        <cylinder length="0.0448" radius="0.0155"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="1.57079632679 0 0" xyz="0.0 -0.018622 0.0"/>
      <mass value="0.0338135900491"/>
      <inertia ixx="7.68636440001e-06" ixy="0" ixz="0" iyy="7.68636440001e-06" iyz="0" izz="4.06185750465e-06"/>
    </inertial>
  </link>
  <joint name="axle_1_joint" type="continuous">
    <origin xyz="-0.000105 -0.022823 -0.012904"/>
    <axis xyz="0 -1 0"/>
    <parent link="mx12w_1"/>
    <child link="axle_1"/>
  </joint>
  <link name="wheel_1">
    <visual>
      <geometry>
        <mesh filename="package://meshes/wheel_and_rollers.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.0292" radius="0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <mass value="0.131073930817"/>
      <inertia ixx="5.61341585248e-05" ixy="0" ixz="0" iyy="5.61341585248e-05" iyz="0" izz="9.36418376542e-05"/>
    </inertial>
  </link>
  <joint name="wheel_1_joint" type="fixed">
    <origin xyz="-0.000023 -0.019556 -0.000006"/>
    <axis xyz="0 -1 0"/>
    <parent link="axle_1"/>
    <child link="wheel_1"/>
  </joint>
  <link name="roller_1_0_0">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_0_0_joint" type="continuous">
    <origin rpy="0 -0.0 0" xyz="0.0       0.009521       -0.021319"/>
    <parent link="wheel_1"/>
    <child link="roller_1_0_0"/>
  </joint>
  <link name="roller_1_0_1">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_0_1_joint" type="continuous">
    <origin rpy="0 -1.25663706144 0" xyz="0.0202755738709       0.009521       -0.00658793330308"/>
    <parent link="wheel_1"/>
    <child link="roller_1_0_1"/>
  </joint>
  <link name="roller_1_0_2">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_0_2_joint" type="continuous">
    <origin rpy="0 -2.51327412287 0" xyz="0.0125309937936       0.009521       0.0172474333031"/>
    <parent link="wheel_1"/>
    <child link="roller_1_0_2"/>
  </joint>
  <link name="roller_1_0_3">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_0_3_joint" type="continuous">
    <origin rpy="0 -3.76991118431 0" xyz="-0.0125309937936       0.009521       0.0172474333031"/>
    <parent link="wheel_1"/>
    <child link="roller_1_0_3"/>
  </joint>
  <link name="roller_1_0_4">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_0_4_joint" type="continuous">
    <origin rpy="0 -5.02654824574 0" xyz="-0.0202755738709       0.009521       -0.00658793330308"/>
    <parent link="wheel_1"/>
    <child link="roller_1_0_4"/>
  </joint>
  <link name="roller_1_1_0">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_1_0_joint" type="continuous">
    <origin rpy="0 -0.628318530718 0" xyz="0.0125309937936       -0.009521       -0.0172474333031"/>
    <parent link="wheel_1"/>
    <child link="roller_1_1_0"/>
  </joint>
  <link name="roller_1_1_1">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_1_1_joint" type="continuous">
    <origin rpy="0 -1.88495559215 0" xyz="0.0202755738709       -0.009521       0.00658793330308"/>
    <parent link="wheel_1"/>
    <child link="roller_1_1_1"/>
  </joint>
  <link name="roller_1_1_2">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_1_2_joint" type="continuous">
    <origin rpy="0 -3.14159265359 0" xyz="2.6108245111e-18       -0.009521       0.021319"/>
    <parent link="wheel_1"/>
    <child link="roller_1_1_2"/>
  </joint>
  <link name="roller_1_1_3">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_1_3_joint" type="continuous">
    <origin rpy="0 -4.39822971503 0" xyz="-0.0202755738709       -0.009521       0.00658793330308"/>
    <parent link="wheel_1"/>
    <child link="roller_1_1_3"/>
  </joint>
  <link name="roller_1_1_4">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_1_1_4_joint" type="continuous">
    <origin rpy="0 -5.65486677646 0" xyz="-0.0125309937936       -0.009521       -0.0172474333031"/>
    <parent link="wheel_1"/>
    <child link="roller_1_1_4"/>
  </joint>
  <link name="mx12w_2">
    <visual>
      <geometry>
        <mesh filename="package://meshes/mx12w.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.0328 0.0380 0.0502"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.06256928"/>
      <inertia ixx="2.06689273909e-05" ixy="0" ixz="0" iyy="1.87493018805e-05" iyz="0" izz="1.31387145429e-05"/>
    </inertial>
  </link>
  <joint name="mx12w_2_joint" type="fixed">
    <origin rpy="0 0 4.1888" xyz="-0.065344  0.087794 -0.032597"/>
    <parent link="base_link"/>
    <child link="mx12w_2"/>
  </joint>
  <link name="axle_2">
    <visual>
      <geometry>
        <mesh filename="package://meshes/axle.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0.0 -0.018622 0.0"/>
      <geometry>
        <cylinder length="0.0448" radius="0.0155"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="1.57079632679 0 0" xyz="0.0 -0.018622 0.0"/>
      <mass value="0.0338135900491"/>
      <inertia ixx="7.68636440001e-06" ixy="0" ixz="0" iyy="7.68636440001e-06" iyz="0" izz="4.06185750465e-06"/>
    </inertial>
  </link>
  <joint name="axle_2_joint" type="continuous">
    <origin xyz="-0.000105 -0.022823 -0.012904"/>
    <axis xyz="0 -1 0"/>
    <parent link="mx12w_2"/>
    <child link="axle_2"/>
  </joint>
  <link name="wheel_2">
    <visual>
      <geometry>
        <mesh filename="package://meshes/wheel_and_rollers.dae" scale="0.1 0.1 0.1"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.0292" radius="0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <mass value="0.131073930817"/>
      <inertia ixx="5.61341585248e-05" ixy="0" ixz="0" iyy="5.61341585248e-05" iyz="0" izz="9.36418376542e-05"/>
    </inertial>
  </link>
  <joint name="wheel_2_joint" type="fixed">
    <origin xyz="-0.000023 -0.019556 -0.000006"/>
    <axis xyz="0 -1 0"/>
    <parent link="axle_2"/>
    <child link="wheel_2"/>
  </joint>
  <link name="roller_2_0_0">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_0_0_joint" type="continuous">
    <origin rpy="0 -0.0 0" xyz="0.0       0.009521       -0.021319"/>
    <parent link="wheel_2"/>
    <child link="roller_2_0_0"/>
  </joint>
  <link name="roller_2_0_1">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_0_1_joint" type="continuous">
    <origin rpy="0 -1.25663706144 0" xyz="0.0202755738709       0.009521       -0.00658793330308"/>
    <parent link="wheel_2"/>
    <child link="roller_2_0_1"/>
  </joint>
  <link name="roller_2_0_2">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_0_2_joint" type="continuous">
    <origin rpy="0 -2.51327412287 0" xyz="0.0125309937936       0.009521       0.0172474333031"/>
    <parent link="wheel_2"/>
    <child link="roller_2_0_2"/>
  </joint>
  <link name="roller_2_0_3">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_0_3_joint" type="continuous">
    <origin rpy="0 -3.76991118431 0" xyz="-0.0125309937936       0.009521       0.0172474333031"/>
    <parent link="wheel_2"/>
    <child link="roller_2_0_3"/>
  </joint>
  <link name="roller_2_0_4">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_0_4_joint" type="continuous">
    <origin rpy="0 -5.02654824574 0" xyz="-0.0202755738709       0.009521       -0.00658793330308"/>
    <parent link="wheel_2"/>
    <child link="roller_2_0_4"/>
  </joint>
  <link name="roller_2_1_0">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_1_0_joint" type="continuous">
    <origin rpy="0 -0.628318530718 0" xyz="0.0125309937936       -0.009521       -0.0172474333031"/>
    <parent link="wheel_2"/>
    <child link="roller_2_1_0"/>
  </joint>
  <link name="roller_2_1_1">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_1_1_joint" type="continuous">
    <origin rpy="0 -1.88495559215 0" xyz="0.0202755738709       -0.009521       0.00658793330308"/>
    <parent link="wheel_2"/>
    <child link="roller_2_1_1"/>
  </joint>
  <link name="roller_2_1_2">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_1_2_joint" type="continuous">
    <origin rpy="0 -3.14159265359 0" xyz="2.6108245111e-18       -0.009521       0.021319"/>
    <parent link="wheel_2"/>
    <child link="roller_2_1_2"/>
  </joint>
  <link name="roller_2_1_3">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_1_3_joint" type="continuous">
    <origin rpy="0 -4.39822971503 0" xyz="-0.0202755738709       -0.009521       0.00658793330308"/>
    <parent link="wheel_2"/>
    <child link="roller_2_1_3"/>
  </joint>
  <link name="roller_2_1_4">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0335103216383"/>
      <inertia ixx="5.36165146213e-06" ixy="0" ixz="0" iyy="5.36165146213e-06" iyz="0" izz="5.36165146213e-06"/>
    </inertial>
  </link>
  <joint name="roller_2_1_4_joint" type="continuous">
    <origin rpy="0 -5.65486677646 0" xyz="-0.0125309937936       -0.009521       -0.0172474333031"/>
    <parent link="wheel_2"/>
    <child link="roller_2_1_4"/>
  </joint>
</robot>
