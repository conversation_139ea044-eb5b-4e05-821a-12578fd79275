[core]
# Load as early as possible
order = -1000
reloadable = false

[package]
version = "2.6.1"
category = "Internal"
title = "Isaac Sim Core Pip Archive"
description = "Pip packages needed by isaac sim extensions"
keywords = ["isaac", "pip"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true
writeTarget.platform = true # pip prebundle makes this extension os specific
writeTarget.python = true

[dependencies]
"omni.kit.pip_archive" = {} # import the base kit python archive
"omni.pip.cloud" = {} # import the cloud python archive
"omni.pip.compute" = {} # import the compute python archive

[[python.module]]
path = "pip_prebundle"

# That is empty module, added here only to make tests discoverable in test run
[[python.module]]
name = "omni.isaac.core_archive"
