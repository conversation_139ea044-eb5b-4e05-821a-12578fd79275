{"ROS2CameraInfoHelper": {"version": 1, "icon": "icons/isaac-sim.svg", "description": "This node automates the CameraInfo message pipeline for monocular and stereo cameras.", "language": "Python", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "isaacRos2", "metadata": {"uiName": "ROS2 Camera Info Helper"}, "inputs": {"execIn": {"type": "execution", "description": "Triggering this causes the sensor pipeline to be generated"}, "enabled": {"type": "bool", "description": "True to enable the camera helper, False to disable", "default": true}, "context": {"type": "uint64", "description": "ROS2 context handle, Default of zero will use the default global context", "default": 0}, "nodeNamespace": {"type": "string", "description": "Namespace of ROS2 Node, prepends any published/subscribed topic by the node namespace", "default": ""}, "qosProfile": {"type": "string", "description": "QoS profile config", "default": ""}, "queueSize": {"type": "uint64", "description": "The number of messages to queue up before throwing some away, in case messages are collected faster than they can be sent. Only honored if 'history' QoS policy was set to 'keep last'. This setting can be overwritten by qosProfile input.", "default": 10}, "frameId": {"type": "string", "description": "FrameId for ROS2 message from the monocular or left stereo camera.", "default": "sim_camera"}, "topicName": {"type": "string", "description": "Topic name for the monocular or left stereo camera data.", "default": "camera_info"}, "renderProductPath": {"type": "token", "description": "Path of the render product used for capturing data from the monocular or left stereo camera"}, "frameIdRight": {"type": "string", "description": "FrameId for ROS2 message from the right stereo camera.", "default": "sim_camera_right", "optional": true}, "topicNameRight": {"type": "string", "description": "Topic name for the right stereo camera data.", "default": "camera_info_right", "optional": true}, "renderProductPathRight": {"type": "token", "description": "Path of the render product used for capturing data from the right stereo camera", "optional": true}, "resetSimulationTimeOnStop": {"type": "bool", "description": "If True the simulation time will reset when stop is pressed, False means time increases monotonically. This setting is ignored if useSystemTime is enabled.", "uiName": "Reset Simulation Time On Stop", "default": false}, "useSystemTime": {"type": "bool", "description": "If True, system timestamp will be included in messages. If False, simulation timestamp will be included in messages", "default": false}, "frameSkipCount": {"type": "uint", "description": "Specifies the number of simulation frames to skip between each message publish. (e.g. Set to 0 to publish each frame. Set 1 to publish every other frame)", "default": 0}}}}