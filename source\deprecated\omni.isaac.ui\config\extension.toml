[package]
version = "1.0.7"
category = "Simulation"
title = "Isaac Sim UI Utilities"
description = "Core UI Elements for Isaac Sim"
keywords = ["isaac", "ui",]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.gui.components."

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.gui.components" = {}

[[python.module]]
name = "omni.isaac.ui"

[[python.module]]
name = "omni.isaac.ui.tests"

[[test]]
dependencies = [
    "isaacsim.examples.extension",
    "omni.isaac.core",
    "omni.isaac.core_archive",
    "omni.isaac.nucleus",
]
