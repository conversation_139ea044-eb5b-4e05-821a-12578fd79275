# Changelog
## [1.1.0] - 2025-06-03
### Changed
- Use isaacsim.core.experimental for prim operations
- Reshuffled extension files

### Added
- Unit tests

## [1.0.2] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [1.0.1] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.0.0] - 2025-04-30
### Added
- New ROS 2 Sim Control extension to control Isaac Sim using Simulation Interfaces (v1.0.0: https://github.com/ros-simulation/simulation_interfaces/releases/tag/1.0.0)
