[package]
version = "1.0.11"
category = "Simulation"
title = "Isaac Sim Conveyor belt utility"
description="Omnigraph Node for Defining the behavior of a linear conveyor belt"
keywords = ["kit", "example"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.asset.gen.conveyor.ui."

[dependencies]
"isaacsim.asset.gen.conveyor" = {}
"isaacsim.core.deprecation_manager" = {}

[[python.module]]
name = "omni.isaac.conveyor"

[[python.module]]
name = "omni.isaac.conveyor.tests"

[[test]]
stdoutFailPatterns.exclude = [
    "*[Error] [carb.glinterop.plugin] GLInteropContext::init: carb::windowing is not available*",
]
