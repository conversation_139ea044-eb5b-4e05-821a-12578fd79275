API
===

Python API
----------

.. Summary

.. currentmodule:: isaacsim.replicator.writers

.. autosummary::
    :nosignatures:

    DataVisualizationWriter
    DOPEWriter
    PoseWriter
    PytorchListener
    PytorchWriter
    YCBVideoWriter

|

.. API

.. autoclass:: isaacsim.replicator.writers.DataVisualizationWriter
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
    
.. autoclass:: isaacsim.replicator.writers.DOPEWriter
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.replicator.writers.PoseWriter
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.replicator.writers.PytorchListener
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.replicator.writers.PytorchWriter
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: isaacsim.replicator.writers.YCBVideoWriter
    :members:
    :undoc-members:
    :inherited-members:
    :show-inheritance:
