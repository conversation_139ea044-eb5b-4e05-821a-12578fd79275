# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

root_prim: "racks"
generated:
  - name: base_rack
    path: base
    usd_config:
      root: "/NVIDIA/Assets/ArchVis/Industrial/Racks/"
      search_depth: 0
      filter: "RackLong_A[0-7].*"
    position:
      base: [-0.55, 0, 0]
    orientation:
      base: [0, 0, 0]
    scale: [0.01, 0.01, 0.01]
    semantic: "shelf"
    physics:
      collision: "convexHull"
  - name: middle_rack
    path: middle
    usd_config:
      root: "/NVIDIA/Assets/ArchVis/Industrial/Racks/"
      search_depth: 0
      filter: "RackLong_A[0-7].*"
    position:
      base: [-0.55, 0, 3.01]
    orientation:
      base: [0, 0, 0]
    scale: [0.01, 0.01, 0.01]
    semantic: "shelf"
    spawn_proba: 0.66
    physics:
      collision: "convexHull"
  - name: pallets
    path: pallets
    semantic: "pallet"
    usd_config:
      root: "/NVIDIA/Assets/ArchVis/Industrial/Piles/"
      search_depth: 0
      filter: "Pallets.*"
    position:
      base: [0.80, 0.20, 0]
      noise:
        type: "uniform"
        params:
          low: [0, -0.80, 0]
          high: [0, 0.80, 0]
    orientation:
      base: [0, 0, 180]
    scale: [0.01, 0.01, 0.01]
    physics:
      apply_children: true
      collision: "convexHull"
      rigid_body: true