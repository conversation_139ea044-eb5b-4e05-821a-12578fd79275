/******************************************************************************
 *       Copyright 1986, 2019 NVIDIA ARC GmbH. All rights reserved.           *
 ******************************************************************************
 
Permission is hereby granted by NVIDIA Corporation ("NVIDIA"), free of charge,
to any person obtaining a copy of the sample definition code that uses our 
Material Definition Language (the "MDL Materials"), to reproduce and distribute
the MDL Materials, including without limitation the rights to use, copy, merge,
publish, distribute, and sell modified and unmodified copies of the MDL 
Materials, and to permit persons to whom the MDL Materials is furnished to do
so, in all cases solely for use with NVIDIA’s Material Definition Language,
subject to the following further conditions:

1. The above copyright notices, this list of conditions, and the disclaimer
that follows shall be retained in all copies of one or more of the MDL
Materials, including in any software with which the MDL Materials are bundled,
redistributed, and/or sold, and included either as stand-alone text files,
human-readable headers or in the appropriate machine-readable metadata fields
within text or binary files as long as those fields can be easily viewed by the
user, as applicable.
2. The name of NVIDIA shall not be used to promote, endorse or advertise any 
Modified Version without specific prior written permission, except a) to comply
 with the notice requirements otherwise contained herein; or b) to acknowledge
the contribution(s) of NVIDIA.

THE MDL MATERIALS ARE PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF COPYRIGHT, PATENT,
TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR 
ANY CLAIM, DAMAGES OR OTHER LIABILITY, INCLUDING ANY GENERAL, SPECIAL, 
INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, WHETHER IN AN ACTION OF 
CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR INABILITY TO USE
THE MDL MATERIALS OR FROM OTHER DEALINGS IN THE MDL MATERIALS.
*/

mdl 1.3;

import df::*;
import state::*;
import math::*;
import tex::*;
import anno::*;

export annotation distill_off();

export material Ue4Translucent(
	float3 base_color = float3(0.8, 0.8, 0.8),
	float emissive_scale = 1,
	float3 normal = float3(0.0,0.0,1.0)
	)
[[
	distill_off()
]]
 = let {     
	color final_base_color = math::saturate(base_color);
	float3 final_normal = math::normalize(normal);
 
	bsdf frosted_bsdf = df::specular_bsdf(
		tint: final_base_color,
		mode: df::scatter_transmit
		);	
	bsdf test_bsdf = df::diffuse_reflection_bsdf(
		tint: final_base_color
		);	
} 
in material(
	surface: material_surface(
		scattering: frosted_bsdf,
		emission:
            material_emission (
                emission:  df::diffuse_edf (),
                intensity: base_color * emissive_scale
                )
	),
	
	geometry: material_geometry(
		normal: final_normal
	)	
);
