[package]
version = "1.0.1"
category = "Simulation"
title = "{EXTENSION_TITLE}"
description = "{EXTENSION_DESCRIPTION}"
keywords = []
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.gui.components" = {}
"omni.kit.uiapp" = {}

[[python.module]]
name = "{PYTHON_PACKAGE_NAME}"
