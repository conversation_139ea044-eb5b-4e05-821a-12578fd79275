# Changelog
## [2.0.11] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.0.10] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [2.0.9] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.0.8] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.0.7] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.6] - 2024-12-09
### Fixed
- startup test

## [2.0.5] - 2024-12-07
### Fixed
- moved from deprecated editor_menu.add_item to omni.kit.menu.utils

## [2.0.4] - 2024-11-19
### Fixed
- Startup test

## [2.0.3] - 2024-10-28
### Changed
- Remove test imports from runtime

## [2.0.2] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.1] - 2024-10-04
### Changed
- Updated omni.isaac.version dependency to isaacsim.core.version

## [2.0.0] - 2024-09-27
### Changed
- Extension renamed to isaacsim.app.about

## [1.0.1] - 2022-05-12
### Changed
- Use omni.isaac.version.get_version()

## [1.0.0] - 2021-02-26
### Changed
- Added test

## [0.2.1] - 2020-12-08
### Changed
- Added "App Version"
- Added right mouse button copy to clipboard

## [0.2.0] - 2020-12-04
### Changed
- Updated to new UI and added resizing

## [0.1.0] - 2020-10-29
### Changed
- Ported old version to extensions 2.0
