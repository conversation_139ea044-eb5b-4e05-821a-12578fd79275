# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot descriptor defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - joint1
    - joint2
    - joint3
    - joint4
    - joint5
    - joint6
default_q: [
    0.0,-0.2,-1.7,-1.507,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:
    - {name: finger_joint, rule: fixed, value: -0.0}
    - {name: left_inner_knuckle_joint, rule: fixed, value: 0.0}
    - {name: right_inner_knuckle_joint, rule: fixed, value: -0.0}
    - {name: right_outer_knuckle_joint, rule: fixed, value: 0.0}
    - {name: left_inner_finger_joint, rule: fixed, value: -0.0}
    - {name: right_inner_finger_joint, rule: fixed, value: 0.0}

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - link1:
    - "center": [-0.031, 0.0, -0.07]
      "radius": 0.083
    - "center": [-0.013, -0.001, -0.007]
      "radius": 0.077
    - "center": [-0.079, -0.0, -0.051]
      "radius": 0.078
  - link2:
    - "center": [0.031, -0.009, -0.106]
      "radius": 0.061
    - "center": [0.103, 0.001, -0.117]
      "radius": 0.059
    - "center": [0.267, -0.001, -0.119]
      "radius": 0.054
    - "center": [0.191, 0.0, -0.121]
      "radius": 0.054
    - "center": [0.351, 0.003, -0.116]
      "radius": 0.051
    - "center": [-0.019, 0.004, -0.101]
      "radius": 0.056
    - "center": [0.47, 0.013, -0.105]
      "radius": 0.044
    - "center": [0.4, 0.011, -0.113]
      "radius": 0.048
  - link3:
    - "center": [0.004, -0.0, 0.011]
      "radius": 0.105
  - link4:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.05
    - "center": [0.001, -0.001, 0.125]
      "radius": 0.05
    - "center": [0.0, -0.0, 0.042]
      "radius": 0.05
    - "center": [0.001, -0.001, 0.084]
      "radius": 0.05
    - "center": [0.0, -0.0, 0.372]
      "radius": 0.065
    - "center": [0.0, -0.0, 0.162]
      "radius": 0.065
    - "center": [0.0, -0.0, 0.318]
      "radius": 0.064
    - "center": [0.0, -0.0, 0.265]
      "radius": 0.065
    - "center": [0.0, -0.0, 0.213]
      "radius": 0.065
  - link5:
    - "center": [0.04, 0.0, 0.0]
      "radius": 0.041
  - onrobot_rg2_base_link:
    - "center": [0.0, 0.001, 0.04]
      "radius": 0.044
    - "center": [0.0, -0.002, 0.084]
      "radius": 0.037
    - "center": [0.0, 0.01, 0.12]
      "radius": 0.031
    - "center": [-0.0, -0.011, 0.115]
      "radius": 0.031
  - left_outer_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.015
    - "center": [-0.0, -0.04, 0.034]
      "radius": 0.015
    - "center": [-0.0, -0.013, 0.011]
      "radius": 0.015
    - "center": [-0.0, -0.027, 0.023]
      "radius": 0.015
  - left_inner_knuckle:
    - "center": [0.0, -0.014, 0.014]
      "radius": 0.015
    - "center": [-0.001, -0.002, 0.002]
      "radius": 0.015
    - "center": [0.001, -0.031, 0.031]
      "radius": 0.015
  - right_inner_knuckle:
    - "center": [0.0, -0.014, 0.014]
      "radius": 0.015
    - "center": [-0.001, -0.002, 0.002]
      "radius": 0.015
    - "center": [0.001, -0.031, 0.031]
      "radius": 0.015
  - right_inner_finger:
    - "center": [0.002, 0.01, 0.028]
      "radius": 0.013
    - "center": [0.003, 0.006, 0.014]
      "radius": 0.012
    - "center": [-0.003, 0.012, 0.037]
      "radius": 0.012
  - left_inner_finger:
    - "center": [0.002, 0.01, 0.028]
      "radius": 0.013
    - "center": [0.003, 0.006, 0.014]
      "radius": 0.012
    - "center": [-0.003, 0.012, 0.037]
      "radius": 0.012
  - right_outer_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.015
    - "center": [-0.0, -0.04, 0.034]
      "radius": 0.015
    - "center": [-0.0, -0.013, 0.011]
      "radius": 0.015
    - "center": [-0.0, -0.027, 0.023]
      "radius": 0.015
