[core]
reloadable = false
# Load at the start, load all schemas with order -100 (with order -1000 the USD libs are loaded)
order = -100

[package]
version = "3.0.5"
category = "Simulation"
title = "USD Isaac schema"
description="USD Isaac schema"
keywords = ["isaac", "usd", "schema"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot.schema."

[dependencies]
"omni.usd.libs" = {}
