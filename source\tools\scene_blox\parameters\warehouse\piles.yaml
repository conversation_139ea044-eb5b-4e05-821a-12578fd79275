# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

root_prim: clutter
generated:
  - name: warehouse_pile
    path: warehouse_pile
    position:
      base: [0, 0, 0]
      noise:
        type: uniform
        params:
          low: [-1.00, -0.20, 0]
          high: [1.00, 0.20, 0]
    orientation:
      base: [0, 0, 0]
      noise:
        type: "uniform"
        params:
          low: [0, 0, -180]
          high: [0, 0, 180]
    scale: [0.01, 0.01, 0.01]
    usd_config:
      root: /NVIDIA/Assets/ArchVis/Industrial/Piles/
      search_depth: 0
      filter: "WarehousePile_.*"
    semantic: "pile"
    physics:
      apply_children: true
      collision: "convexHull"
      rigid_body: true