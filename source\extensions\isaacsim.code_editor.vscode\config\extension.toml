[package]
version = "1.1.0"
category = "Utility"
title = "VS Code integration"
description = "VS Code version of Omniverse's script editor"
keywords = ["isaac", "python", "vscode", "editor"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"omni.kit.notification_manager" = { optional = true }
"omni.kit.uiapp" = { optional = true }

[[python.module]]
name = "isaacsim.code_editor.vscode"

[settings]
# IP address where the extension server will listen for connections.
exts."isaacsim.code_editor.vscode".host = "127.0.0.1"
# Port number where the extension server will listen for connections.
exts."isaacsim.code_editor.vscode".port = 8226
# Whether to publish incoming carb logging messages.
# Warning: enabling this feature may cause the application to freeze in certain circumstances.
exts."isaacsim.code_editor.vscode".carb_logs = false
