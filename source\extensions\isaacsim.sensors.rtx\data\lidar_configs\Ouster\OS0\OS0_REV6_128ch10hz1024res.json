{"comment1": "parameters obtained from https://data.ouster.io/downloads/datasheets/datasheet-rev06-v2p5-os0.pdf", "comment2": "parameters without comments are assumed correct, but will ultimately need checking", "class": "sensor", "type": "lidar", "name": "OS0 REV6 128 10hz @ 1024 resolution", "comment3": "ouster OS0 REV6 128 channels @ 10Hz 1024 horizontal resolution", "driveWorksId": "GENERIC", "profile": {"scanType": "rotary", "intensityProcessing": "normalization", "rotationDirection": "CW", "rayType": "IDEALIZED", "nearRangeM": 0.3, "comment4": "OPTICAL PERFORMANCE-Minimum Range 0.3 m for point cloud data", "farRangeM": 50.0, "comment5": "OPTICAL PERFORMANCE- Range 45 m @ >90% detection probability, 100 klx sunlight 50 m @ >50% detection probability, 100 klx sunlight", "startAzimuthDeg": 0.0, "comment6": "x+ is 0°  y+ is 90° (and z is up, right handed)", "endAzimuthDeg": 360.0, "comment7": "OPTICAL PERFORMANCE- Field of View Horizontal: 360°", "upElevationDeg": 45.0, "comment8": "OPTICAL PERFORMANCE- Field of View Vertical: 90° (+45° to -45°)", "downElevationDeg": -45.0, "rangeResolutionM": 0.001, "comment9": "OPTICAL PERFORMANCE- Range Resolution 0.1 cm", "rangeAccuracyM": 0.03, "comment10": "OPTICAL PERFORMANCE- Range Accuracy ±3 cm for lambertian targets, ±10 cm for retroreflectors", "avgPowerW": 0.002, "minReflectance": 0.1, "comment11": "OPTICAL PERFORMANCE - Range (10% Lambertian reflectivity, 1024 @ 10 Hz mode)", "minReflectanceRange": 20.0, "comment12": "OPTICAL PERFORMANCE - Range 15 m @ >90% detection probability, 100 klx sunlight 20 m @ >50% detection probability, 100 klx sunlight", "wavelengthNm": 865.0, "comment13": "LASER-  Laser Wavelength 865 nm", "pulseTimeNs": 6, "comment14": "These add noise to the emitter direction to each point randomly if Std is not 0.0", "azimuthErrorMean": 0.0, "azimuthErrorStd": 0.01, "comment15": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Horizontal: ±0.01°", "elevationErrorMean": 0.0, "elevationErrorStd": 0.01, "comment16": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Vertical: ±0.01°", "maxReturns": 2, "scanRateBaseHz": 10.0, "reportRateBaseHz": 10240, "numberOfEmitters": 128, "emitterStateCount": 1, "emitterStates": [{"comment19": "beam_azimuth_angles from get_beam_intrinsics", "azimuthDeg": [4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22, 4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22, 4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22, 4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22], "comment20": "beam_altitude_angles from get_beam_intrinsics", "elevationDeg": [44.5, 43.8, 43.1, 42.4, 41.7, 41.0, 40.3, 39.59, 38.89, 38.19, 37.49, 36.79, 36.09, 35.39, 34.69, 33.99, 33.29, 32.59, 31.89, 31.19, 30.48, 29.78, 29.08, 28.38, 27.68, 26.98, 26.28, 25.58, 24.88, 24.18, 23.48, 22.78, 22.07, 21.37, 20.67, 19.97, 19.27, 18.57, 17.87, 17.17, 16.47, 15.77, 15.07, 14.37, 13.67, 12.96, 12.26, 11.56, 10.86, 10.16, 9.46, 8.76, 8.06, 7.36, 6.66, 5.96, 5.26, 4.56, 3.85, 3.15, 2.45, 1.75, 1.05, 0.35, -0.35, -1.05, -1.75, -2.45, -3.15, -3.85, -4.56, -5.26, -5.96, -6.66, -7.36, -8.06, -8.76, -9.46, -10.16, -10.86, -11.56, -12.26, -12.96, -13.67, -14.37, -15.07, -15.77, -16.47, -17.17, -17.87, -18.57, -19.27, -19.97, -20.67, -21.37, -22.07, -22.78, -23.48, -24.18, -24.88, -25.58, -26.28, -26.98, -27.68, -28.38, -29.08, -29.78, -30.48, -31.19, -31.89, -32.59, -33.29, -33.99, -34.69, -35.39, -36.09, -36.79, -37.49, -38.19, -38.89, -39.59, -40.3, -41.0, -41.7, -42.4, -43.1, -43.8, -44.5], "fireTimeNs": [381, 1143, 1905, 2667, 3429, 4191, 4953, 5715, 6477, 7239, 8001, 8763, 9525, 10287, 11049, 11811, 12573, 13335, 14097, 14859, 15621, 16383, 17145, 17907, 18669, 19431, 20193, 20955, 21717, 22479, 23241, 24003, 24765, 25527, 26289, 27051, 27813, 28575, 29337, 30099, 30861, 31623, 32385, 33147, 33909, 34671, 35433, 36195, 36957, 37719, 38481, 39243, 40005, 40767, 41529, 42291, 43053, 43815, 44577, 45339, 46101, 46863, 47625, 48387, 49149, 49911, 50673, 51435, 52197, 52959, 53721, 54483, 55245, 56007, 56769, 57531, 58293, 59055, 59817, 60579, 61341, 62103, 62865, 63627, 64389, 65151, 65913, 66675, 67437, 68199, 68961, 69723, 70485, 71247, 72009, 72771, 73533, 74295, 75057, 75819, 76581, 77343, 78105, 78867, 79629, 80391, 81153, 81915, 82677, 83439, 84201, 84963, 85725, 86487, 87249, 88011, 88773, 89535, 90297, 91059, 91821, 92583, 93345, 94107, 94869, 95631, 96393, 97155]}], "intensityMappingType": "LINEAR"}}