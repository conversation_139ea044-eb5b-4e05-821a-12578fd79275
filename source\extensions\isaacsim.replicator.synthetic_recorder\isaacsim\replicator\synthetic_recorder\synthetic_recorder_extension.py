# SPDX-FileCopyrightText: Copyright (c) 2018-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import asyncio

import omni.ext
import omni.ui as ui
from omni.kit.menu.utils import MenuHelperExtensionFull

from .synthetic_recorder_window import SyntheticRecorderWindow


class SyntheticRecorderExtension(omni.ext.IExt, MenuHelperExtensionFull):
    WINDOW_NAME = "Synthetic Data Recorder"
    MENU_GROUP = "Tools/Replicator"

    def on_startup(self, ext_id):
        # Add the menu item
        self.menu_startup(
            lambda: SyntheticRecorderWindow(SyntheticRecorderExtension.WINDOW_NAME, ext_id),
            SyntheticRecorderExtension.WINDOW_NAME,
            SyntheticRecorderExtension.WINDOW_NAME,
            SyntheticRecorderExtension.MENU_GROUP,
        )

    def on_shutdown(self):
        self.menu_shutdown()
