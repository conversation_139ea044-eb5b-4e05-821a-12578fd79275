[package]
version = "1.0.7"
category = "Utility"
title = "Jupyter notebook integration"
description = "Jupyter notebook version of Omniverse's script editor"
keywords = ["isaac", "python", "jupyter", "notebook"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.code_editor.jupyter."

[dependencies]
"isaacsim.code_editor.jupyter" = {}
"isaacsim.core.deprecation_manager" = {}

[[python.module]]
name = "omni.isaac.jupyter_notebook.tests"
