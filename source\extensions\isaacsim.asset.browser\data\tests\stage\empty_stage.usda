#usda 1.0
(
    customLayerData = {
        dictionary cameraSettings = {
            dictionary Front = {
                double3 position = (0, 0, 50000)
                double radius = 500
            }
            dictionary Perspective = {
                double3 position = (500.0000000000001, 500.0000000000001, 499.9999999999998)
                double3 target = (0, 0, 0)
            }
            dictionary Right = {
                double3 position = (-50000, 0, -1.1102230246251565e-11)
                double radius = 500
            }
            dictionary Top = {
                double3 position = (-4.329780281177466e-12, 50000, 1.1102230246251565e-11)
                double radius = 500
            }
            string boundCamera = "/OmniverseKit_Persp"
        }
        dictionary omni_layer = {
            dictionary muteness = {
            }
        }
        dictionary renderSettings = {
        }
    }
    defaultPrim = "World"
    endTimeCode = 100
    metersPerUnit = 0.01
    startTimeCode = 0
    timeCodesPerSecond = 24
    upAxis = "Y"
)

def Xform "World"
{
    def Scope "Looks"
    {
    }
}

