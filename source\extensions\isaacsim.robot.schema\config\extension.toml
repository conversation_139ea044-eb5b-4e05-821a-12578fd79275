[core]
reloadable = false
# Load at the start, load all schemas with order -100 (with order -1000 the USD libs are loaded)
order = -100

[package]
version = "3.5.1"
category = "Simulation"
title = "Isaac USD schema"
description="Extension used to host all USD schemas made for robots. Provides the generated schema USDA. and a few utility functions used for informal schemas."
keywords = ["isaac", "usd", "schema"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[dependencies]
"omni.usd.libs" = {}

[[python.module]]
name = "omni.isaac.RangeSensorSchema"
path = "./plugins"

[[python.module]]
name = "omni.isaac.IsaacSensorSchema"
path = "./plugins"

[[python.module]]
name = "usd.schema.isaac"

[[native.library]]
"filter:platform"."linux-x86_64"."path" = "plugins/lib/${lib_prefix}rangeSensorSchema${lib_ext}"
"filter:platform"."windows-x86_64"."path" = "plugins/bin/${lib_prefix}rangeSensorSchema${lib_ext}"

[[native.library]]
# path = "plugins/IsaacSensorSchema/lib/${lib_prefix}isaacSensorSchema${lib_ext}"
"filter:platform"."linux-x86_64"."path" = "plugins/lib/${lib_prefix}isaacSensorSchema${lib_ext}"
"filter:platform"."windows-x86_64"."path" = "plugins/bin/${lib_prefix}isaacSensorSchema${lib_ext}"
