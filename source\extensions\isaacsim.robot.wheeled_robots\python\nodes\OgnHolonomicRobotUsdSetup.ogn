{"HolonomicRobotUsdSetup": {"version": 1, "description": "setup any robot to be ready to be used by the holonomic controller by extract attributes from USD", "language": "Python", "categories": {"isaacWheeledRobots": "robot controller prep inside Isaac Sim"}, "metadata": {"uiName": "Usd Setup Holonomic Robot"}, "$comment": "Use this node to extract all the holonomic drive information from USD if the listed information are stored in the USD file already. If they are not in USD, you can manually set those values in the HolonomicController node", "inputs": {"robotPrim": {"type": "target", "description": "prim for the robot's articulation root"}, "comPrim": {"type": "target", "description": "prim for the center of mass xform"}, "usePath": {"type": "bool", "description": "use prim path instead of prim target", "default": false}, "robotPrimPath": {"type": "token", "description": "prim path to the robot's articulation root link when usdPath is true"}, "comPrimPath": {"type": "token", "description": "prim path to the robot's center of mass xform"}}, "outputs": {"wheelRadius": {"type": "double[]", "description": "an array of wheel radius"}, "wheelPositions": {"type": "double[3][]", "description": "position of the wheel with respect to chassis' center of mass"}, "wheelOrientations": {"type": "double[4][]", "description": "orientation of the wheel with respect to chassis' center of mass frame "}, "mecanumAngles": {"type": "double[]", "description": "angles of the mechanum wheels with respect to wheel's rotation axis"}, "wheelAxis": {"type": "double[3]", "description": "the rotation axis of the wheels, assuming all wheels have the same"}, "upAxis": {"type": "double[3]", "description": "the rotation axis of the vehicle"}, "wheelDofNames": {"type": "token[]", "description": "name of the left wheel joint"}}}}