{"comment1": "parameters obtained from https://data.ouster.io/downloads/datasheets/datasheet-rev06-v2p5-os0.pdf", "comment2": "parameters without comments are assumed correct, but will ultimately need checking", "class": "sensor", "type": "lidar", "name": "OS0 REV6 128 10hz @ 512 resolution", "comment3": "ouster OS0 REV6 128 channels @ 10Hz 512 horizontal resolution", "driveWorksId": "GENERIC", "profile": {"scanType": "rotary", "intensityProcessing": "normalization", "rotationDirection": "CW", "rayType": "IDEALIZED", "nearRangeM": 0.3, "comment4": "OPTICAL PERFORMANCE-Minimum Range 0.3 m for point cloud data", "farRangeM": 50.0, "comment5": "OPTICAL PERFORMANCE- Range 45 m @ >90% detection probability, 100 klx sunlight 50 m @ >50% detection probability, 100 klx sunlight", "startAzimuthDeg": 0.0, "comment6": "x+ is 0°  y+ is 90° (and z is up, right handed)", "endAzimuthDeg": 360.0, "comment7": "OPTICAL PERFORMANCE- Field of View Horizontal: 360°", "upElevationDeg": 45.0, "comment8": "OPTICAL PERFORMANCE- Field of View Vertical: 90° (+45° to -45°)", "downElevationDeg": -45.0, "rangeResolutionM": 0.001, "comment9": "OPTICAL PERFORMANCE- Range Resolution 0.1 cm", "rangeAccuracyM": 0.03, "comment10": "OPTICAL PERFORMANCE- Range Accuracy ±3 cm for lambertian targets, ±10 cm for retroreflectors", "avgPowerW": 0.002, "minReflectance": 0.1, "comment11": "OPTICAL PERFORMANCE - Range (10% Lambertian reflectivity, 1024 @ 10 Hz mode)", "minReflectanceRange": 20.0, "comment12": "OPTICAL PERFORMANCE - Range 15 m @ >90% detection probability, 100 klx sunlight 20 m @ >50% detection probability, 100 klx sunlight", "wavelengthNm": 865.0, "comment13": "LASER-  Laser Wavelength 865 nm", "pulseTimeNs": 6, "comment14": "These add noise to the emitter direction to each point randomly if Std is not 0.0", "azimuthErrorMean": 0.0, "azimuthErrorStd": 0.01, "comment15": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Horizontal: ±0.01°", "elevationErrorMean": 0.0, "elevationErrorStd": 0.01, "comment16": "OPTICAL PERFORMANCE-Angular Sampling Accuracy Vertical: ±0.01°", "maxReturns": 2, "scanRateBaseHz": 10.0, "reportRateBaseHz": 5120, "numberOfEmitters": 128, "emitterStateCount": 1, "emitterStates": [{"comment19": "beam_azimuth_angles from get_beam_intrinsics", "azimuthDeg": [4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22, 4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22, 4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22, 4.23, 1.41, -1.4, -4.21, 4.22, 1.42, -1.41, -4.22, 4.23, 1.41, -1.42, -4.21, 4.23, 1.42, -1.4, -4.2, 4.23, 1.41, -1.39, -4.21, 4.25, 1.43, -1.41, -4.22, 4.24, 1.44, -1.41, -4.22, 4.23, 1.42, -1.38, -4.22], "comment20": "beam_altitude_angles from get_beam_intrinsics", "elevationDeg": [44.5, 43.8, 43.1, 42.4, 41.7, 41.0, 40.3, 39.59, 38.89, 38.19, 37.49, 36.79, 36.09, 35.39, 34.69, 33.99, 33.29, 32.59, 31.89, 31.19, 30.48, 29.78, 29.08, 28.38, 27.68, 26.98, 26.28, 25.58, 24.88, 24.18, 23.48, 22.78, 22.07, 21.37, 20.67, 19.97, 19.27, 18.57, 17.87, 17.17, 16.47, 15.77, 15.07, 14.37, 13.67, 12.96, 12.26, 11.56, 10.86, 10.16, 9.46, 8.76, 8.06, 7.36, 6.66, 5.96, 5.26, 4.56, 3.85, 3.15, 2.45, 1.75, 1.05, 0.35, -0.35, -1.05, -1.75, -2.45, -3.15, -3.85, -4.56, -5.26, -5.96, -6.66, -7.36, -8.06, -8.76, -9.46, -10.16, -10.86, -11.56, -12.26, -12.96, -13.67, -14.37, -15.07, -15.77, -16.47, -17.17, -17.87, -18.57, -19.27, -19.97, -20.67, -21.37, -22.07, -22.78, -23.48, -24.18, -24.88, -25.58, -26.28, -26.98, -27.68, -28.38, -29.08, -29.78, -30.48, -31.19, -31.89, -32.59, -33.29, -33.99, -34.69, -35.39, -36.09, -36.79, -37.49, -38.19, -38.89, -39.59, -40.3, -41.0, -41.7, -42.4, -43.1, -43.8, -44.5], "fireTimeNs": [762, 2287, 3812, 5337, 6862, 8387, 9912, 11437, 12962, 14487, 16012, 17537, 19062, 20587, 22112, 23637, 25162, 26687, 28212, 29737, 31262, 32787, 34312, 35837, 37362, 38887, 40412, 41937, 43462, 44987, 46512, 48037, 49562, 51087, 52612, 54137, 55662, 57187, 58712, 60237, 61762, 63287, 64812, 66337, 67862, 69387, 70912, 72437, 73962, 75487, 77012, 78537, 80062, 81587, 83112, 84637, 86162, 87687, 89212, 90737, 92262, 93787, 95312, 96837, 98362, 99887, 101412, 102937, 104462, 105987, 107512, 109037, 110562, 112087, 113612, 115137, 116662, 118187, 119712, 121237, 122762, 124287, 125812, 127337, 128862, 130387, 131912, 133437, 134962, 136487, 138012, 139537, 141062, 142587, 144112, 145637, 147162, 148687, 150212, 151737, 153262, 154787, 156312, 157837, 159362, 160887, 162412, 163937, 165462, 166987, 168512, 170037, 171562, 173087, 174612, 176137, 177662, 179187, 180712, 182237, 183762, 185287, 186812, 188337, 189862, 191387, 192912, 194437]}], "intensityMappingType": "LINEAR"}}