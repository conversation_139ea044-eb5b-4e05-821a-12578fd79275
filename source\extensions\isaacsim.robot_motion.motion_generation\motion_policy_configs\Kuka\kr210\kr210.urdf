<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from kr210l150.xacro                | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<!--Generates a urdf from the macro in kr210_macro.xacro -->
<robot name="kuka_kr210" xmlns:xacro="http://wiki.ros.org/xacro">
  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.027804 0.00039112 0.14035"/>
      <mass value="1572.9"/>
      <inertia ixx="89.282" ixy="-0.47721" ixz="0.85562" iyy="107.51" iyz="0.0067576" izz="172.02"/>
    </inertial>
    <visual>
      <origin rpy="-1.570796327 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/visual/base_link.dae"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="-1.570796327 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/collision/base_link.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_1">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.036811 -0.024697 0.56577"/>
      <mass value="1385.5"/>
      <inertia ixx="90.873" ixy="33.809" ixz="17.159" iyy="147.03" iyz="0.063634" izz="168.19"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/visual/link_1.dae"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/collision/link_1.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_2">
    <inertial>
      <origin rpy="0 0 0" xyz="0.016923 -0.19196 0.44751"/>
      <mass value="958.62"/>
      <inertia ixx="180.42" ixy="-0.83462" ixz="0.32549" iyy="177.68" iyz="-20.82" izz="20.495"/>
    </inertial>
    <visual>
      <origin rpy="-1.570796327 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/visual/link_2.dae"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/collision/link_2.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_3">
    <inertial>
      <origin rpy="0 0 0" xyz="0.18842 0.18344 -0.042799"/>
      <mass value="710.03"/>
      <inertia ixx="11.887" ixy="-0.12154" ixz="-1.3604" iyy="98.805" iyz="-0.056505" izz="96.251"/>
    </inertial>
    <visual>
      <origin rpy="-1.570796327 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/visual/link_3.dae"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/collision/link_3.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_4">
    <inertial>
      <origin rpy="0 0 0" xyz="0.27146 -0.007326 5.2775E-05"/>
      <mass value="173.73"/>
      <inertia ixx="1.8001" ixy="-0.18515" ixz="0.00051232" iyy="5.514" iyz="0.00070469" izz="6.3498"/>
    </inertial>
    <visual>
      <origin rpy="-1.570796327 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/visual/link_4.dae"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/collision/link_4.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_5">
    <inertial>
      <origin rpy="0 0 0" xyz="0.04379 0.025984 3.5491E-07"/>
      <mass value="72.17"/>
      <inertia ixx="0.3938" ixy="-0.085332" ixz="1.7223E-06" iyy="0.68945" iyz="-7.0292E-06" izz="0.67292"/>
    </inertial>
    <visual>
      <origin rpy="-1.570796327 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/visual/link_5.dae"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/collision/link_5.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="link_6">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.017956 -1.5237E-05 0.00015484"/>
      <mass value="6.3154"/>
      <inertia ixx="0.031746" ixy="1.7673E-07" ixz="-6.6558E-06" iyy="0.016686" iyz="1.4304E-07" izz="0.016723"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/visual/link_6.dae"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://kuka_kr210_support/meshes/kr210l150/collision/link_6.stl"/>
      </geometry>
    </collision>
  </link>
  <link name="tool0"/>
  <joint name="joint_a1" type="revolute">
    <origin rpy="0 0 0" xyz="-0.00262 0.00097586 0.33099"/>
    <parent link="base_link"/>
    <child link="link_1"/>
    <axis xyz="0 0 1"/>
    <limit effort="0" lower="-3.228859205" upper="3.228859205" velocity="2.146755039"/>
  </joint>
  <joint name="joint_a2" type="revolute">
    <origin rpy="0 0 0" xyz="0.35277 -0.037476 0.4192"/>
    <parent link="link_1"/>
    <child link="link_2"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-0.785398185" upper="1.483529905" velocity="2.007128695"/>
  </joint>
  <joint name="joint_a3" type="revolute">
    <origin rpy="0 0 0" xyz="-9.8483E-05 -0.1475 1.2499"/>
    <parent link="link_2"/>
    <child link="link_3"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-3.66519153" upper="1.134464045" velocity="1.954768816"/>
  </joint>
  <joint name="joint_a4" type="revolute">
    <origin rpy="0 0 0" xyz="0.95795 0.184 -0.055059"/>
    <parent link="link_3"/>
    <child link="link_4"/>
    <axis xyz="1 0 0"/>
    <limit effort="0" lower="-6.10865255" upper="6.10865255" velocity="3.124139447"/>
  </joint>
  <joint name="joint_a5" type="revolute">
    <origin rpy="0 0 0" xyz="0.542 0 0"/>
    <parent link="link_4"/>
    <child link="link_5"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-2.181661625" upper="2.181661625" velocity="3.001966396"/>
  </joint>
  <joint name="joint_a6" type="revolute">
    <origin rpy="0 0 0" xyz="0.1925 0 0"/>
    <parent link="link_5"/>
    <child link="link_6"/>
    <axis xyz="1 0 0"/>
    <limit effort="0" lower="-6.10865255" upper="6.10865255" velocity="3.822271167"/>
  </joint>
  <joint name="link_6-tool0" type="fixed">
    <parent link="link_6"/>
    <child link="tool0"/>
    <origin rpy="0 0 0" xyz="0.0375 0 -0.00023924"/>
  </joint>
  <link name="Link1"/>
  <joint name="Link1-link_1" type="fixed">
    <parent link="link_1"/>
    <child link="Link1"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
</robot>

