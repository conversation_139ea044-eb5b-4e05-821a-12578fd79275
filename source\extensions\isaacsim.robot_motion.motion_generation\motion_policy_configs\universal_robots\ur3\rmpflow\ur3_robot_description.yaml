# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# Lula will only use these joints to control the robot position.
cspace:
    - shoulder_pan_joint
    - shoulder_lift_joint
    - elbow_joint
    - wrist_1_joint
    - wrist_2_joint
    - wrist_3_joint
default_q: [
    0.0,-1.0,0.9,0.0,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - shoulder_link:
    - "center": [-0.0, 0.0, -0.02]
      "radius": 0.055
    - "center": [0.01, -0.019, -0.0]
      "radius": 0.045
    - "center": [0.004, -0.007, 0.019]
      "radius": 0.05
  - upper_arm_link:
    - "center": [0.003, 0.002, 0.104]
      "radius": 0.052
    - "center": [-0.232, 0.002, 0.112]
      "radius": 0.043
    - "center": [-0.121, -0.001, 0.12]
      "radius": 0.042
    - "center": [-0.163, 0.002, 0.118]
      "radius": 0.041
    - "center": [-0.086, 0.001, 0.121]
      "radius": 0.041
    - "center": [-0.02, 0.014, 0.121]
      "radius": 0.041
    - "center": [-0.026, -0.019, 0.126]
      "radius": 0.035
    - "center": [-0.238, 0.0, 0.146]
      "radius": 0.04
  - forearm_link:
    - "center": [-0.013, 0.001, 0.04]
      "radius": 0.042
    - "center": [-0.214, -0.002, 0.035]
      "radius": 0.039
    - "center": [-0.171, -0.0, 0.027]
      "radius": 0.036
    - "center": [-0.083, 0.0, 0.029]
      "radius": 0.036
    - "center": [0.009, -0.006, 0.054]
      "radius": 0.034
    - "center": [-0.204, 0.006, 0.003]
      "radius": 0.036
    - "center": [-0.103, 0.002, 0.028]
      "radius": 0.035
    - "center": [0.006, 0.01, 0.054]
      "radius": 0.034
    - "center": [-0.213, 0.005, 0.043]
      "radius": 0.037
    - "center": [-0.022, -0.002, 0.025]
      "radius": 0.033
    - "center": [-0.137, 0.001, 0.027]
      "radius": 0.036
    - "center": [-0.05, 0.0, 0.034]
      "radius": 0.039
  - wrist_1_link:
    - "center": [0.0, -0.009, -0.002]
      "radius": 0.041
    - "center": [-0.003, 0.019, 0.001]
      "radius": 0.037
    - "center": [0.006, 0.007, -0.024]
      "radius": 0.033
  - wrist_2_link:
    - "center": [-0.0, 0.0, -0.015]
      "radius": 0.041
    - "center": [-0.0, 0.012, 0.001]
      "radius": 0.039
    - "center": [-0.0, -0.018, -0.001]
      "radius": 0.04
  - wrist_3_link:
    - "center": [0.0, 0.002, -0.025]
      "radius": 0.035
