# Changelog
## [1.0.13] - 2025-05-31
### Changed
- Use default nucleus server for all tests

## [1.0.12] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [1.0.11] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [1.0.10] - 2025-05-10
### Changed
- Enable FSD in test settings

## [1.0.9] - 2025-04-22
### Changed
- Update to new Surface Gripper

## [1.0.8] - 2025-04-11
### Changed
- Update Isaac Sim robot asset path

## [1.0.7] - 2025-04-09
### Changed
- Update all test args to be consistent

## [1.0.6] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [1.0.5] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code
- Switch asset root for tests to internal nucleus

## [1.0.4] - 2025-02-19
### Changed
- Updated ur10 class's usd path
- Using variant for gripper creation

## [1.0.3] - 2025-01-26
### Changed
- Update test settings

## [1.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [1.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [1.0.0] - 2024-09-30
### Changed
- extension renamed to isaacsim.robot.manipulators.examples (from omni.isaac.manipulators.examples)

## [0.1.0] - 2024-08-09
### Added
- franka and universal_robots folder
