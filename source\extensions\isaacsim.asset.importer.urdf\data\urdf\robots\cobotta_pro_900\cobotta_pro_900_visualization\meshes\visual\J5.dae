<?xml version="1.0" encoding="UTF-8"?>
<COLLADA xmlns="http://www.collada.org/2008/03/COLLADASchema" version="1.5.0">
	<asset>
		<contributor>
			<author>DENSO WAVE</author>
			<comments/>
		</contributor>
		<unit/>
		<up_axis>Y_UP</up_axis>
	</asset>
	<library_materials>
		<material id="blinn20.material" name="blinn20">
			<instance_effect url="#blinn20.effect"/>
		</material>
		<material id="blinn21.material" name="blinn21">
			<instance_effect url="#blinn21.effect"/>
		</material>
	</library_materials>
	<library_effects>
		<effect id="blinn20.effect">
			<profile_COMMON>
				<technique sid="common">
					<phong>
						<emission>
							<color>0.023529 0.023529 0.023529 1</color>
						</emission>
						<diffuse>
							<color>0.752941 0.752941 0.752941 1</color>
						</diffuse>
						<specular>
							<color>0.752941 0.752941 0.752941 1</color>
						</specular>
					</phong>
				</technique>
			</profile_COMMON>
		</effect>
		<effect id="blinn21.effect">
			<profile_COMMON>
				<technique sid="common">
					<phong>
						<emission>
							<color>0.04 0.04 0.04 1</color>
						</emission>
						<diffuse>
							<color>1 1 1 1</color>
						</diffuse>
						<specular>
							<color>0.2 0.2 0.2 1</color>
						</specular>
					</phong>
				</technique>
			</profile_COMMON>
		</effect>
	</library_effects>
	<library_geometries>
		<geometry id="base2_M1KShape0" name="base2_M1KShape0">
			<mesh>
				<source id="base2_M1KShape0.positions">
					<float_array id="base2_M1KShape0.positions-array" count="414" digits="2490374">-0.0212174 0.162867 0.0472828 -0.0201715 0.16226 0.0493321 -0.0325668 0.155691 0.0402853 -0.0117239 0.166332 0.0507075 -0.0323943 0.154552 0.0416319 -0.00812437 0.165825 0.0528969 -0.00134998 0.167754 0.0521618 0.00298516 0.166454 0.0535179 -0.0419911 0.144181 0.0312456 -0.0342063 0.153841 0.0378823 -0.0325668 0.155691 0.0402853 -0.0420376 0.145077 0.029538 -0.0510784 0.126866 0.0139418 -0.047668 0.135258 0.0196013 -0.0420376 0.145077 0.029538 0.010063 0.166766 0.0511554 0.0156187 0.164019 0.0510894 0.0265899 0.158707 0.0457829 0.0208389 0.163066 0.0474858 0.0292934 0.15809 0.0425102 0.0354595 0.151732 0.0387936 0.0386028 0.149591 0.0339376 0.0437735 0.142704 0.0270063 0.0460387 0.13844 0.0254773 0.0499359 0.130233 0.0172867 0.0486492 0.133178 0.0175987 -0.0494903 0.104293 -0.0112872 -0.0408572 0.0865258 -0.0264835 -0.0432104 0.0921906 -0.0235329 -0.0501627 0.1046 -0.00831926 -0.0387587 0.0864391 -0.0293505 -0.0380179 0.0838565 -0.0290754 0.0406231 0.0886601 -0.0271485 0.0471683 0.0988992 -0.0162586 0.0511142 0.109642 -0.00601566 0.0507533 0.106214 -0.00672114 0.0471683 0.0988992 -0.0162586 0.0441327 0.0921691 -0.0207589 0.0406231 0.0886601 -0.0271485 -0.0115656 0.0691568 -0.0463122 -0.00556928 0.0666279 -0.0463047 -0.00172305 0.0678825 -0.0477449 -0.0176088 0.0697248 -0.0432035 -0.0281893 0.0751863 -0.0377421 -0.0208171 0.0724409 -0.0430355 -0.0308985 0.0785273 -0.0369462 0.0367522 0.0842809 -0.0314326 0.0359597 0.0816075 -0.0313153 -0.0308985 0.0785273 -0.0369462 -0.0277948 0.0772742 -0.0384772 -0.0387587 0.0864391 -0.0293505 -0.0129488 0.0703058 -0.0454519 -0.0208171 0.0724409 -0.0430355 -0.00172305 0.0678825 -0.0477449 -0.0115656 0.0691568 -0.0463122 0.00501943 0.0689004 -0.0468622 0.0101678 0.0688683 -0.0467458 0.0154544 0.0714271 -0.0449735 0.0290595 0.0773207 -0.0382411 0.0292389 0.0782694 -0.0379703 0.020929 0.0725252 -0.0429983 0.0367522 0.0842809 -0.0314326 0.0292934 0.15809 0.0425102 0.0342211 0.153826 0.0378772 0.0386028 0.149591 0.0339376 -0.0325668 0.155691 0.0402853 -0.0215523 0.16258 0.0460349 -0.0212174 0.162867 0.0472828 -0.0342063 0.153841 0.0378823 -0.0117239 0.166332 0.0507075 -0.0100029 0.166068 0.0503097 -0.00134998 0.167754 0.0521618 0.00299843 0.166875 0.0511156 0.010063 0.166766 0.0511554 0.0179015 0.163817 0.0480664 0.0208389 0.163066 0.0474858 0.0292934 0.15809 0.0425102 0.0342211 0.153826 0.0378772 -0.0524394 0.119407 0.00638306 -0.0506374 0.127545 0.0118788 -0.0520625 0.114653 0.00172722 -0.0520893 0.117058 0.00150311 0.0511142 0.109642 -0.00601566 0.0517996 0.121936 0.00629246 0.0520886 0.117914 0.0049783 0.050529 0.127002 0.0140727 0.0290595 0.0773207 -0.0382411 0.020929 0.0725252 -0.0429983 0.0219944 0.0714185 -0.0415126 0.00772627 0.0670238 -0.0459076 0.0101678 0.0688683 -0.0467458 -0.0476411 0.114061 0.047035 -0.046398 0.113135 0.0531317 -0.0461976 0.110926 0.0563194 -0.0467372 0.114103 0.0459944 -0.0430675 0.104092 0.058776 -0.0445216 0.105475 0.0586532 -0.041625 0.0975232 0.0562907 -0.0400456 0.0954711 0.0540533 -0.0405536 0.094638 0.0512937 -0.0394596 0.094061 0.0460793 -0.0404899 0.0944099 0.0447265 -0.0475618 0.126156 0.0500607 -0.0403274 0.145966 0.0496571 -0.0461352 0.130082 0.0568018 -0.0429739 0.138769 0.0583624 -0.0409795 0.143531 0.0548582 -0.0406645 0.145106 0.0434558 -0.0425509 0.139934 0.0381335 -0.0456058 0.131309 0.0379856 -0.0468123 0.126856 0.0428678 -0.0429739 0.138769 0.0583624 -0.0409795 0.143531 0.0548582 -0.0425522 0.137767 0.0587485 -0.0461352 0.130082 0.0568018 -0.0458063 0.128571 0.0557251 -0.0475618 0.126156 0.0500607 -0.046774 0.125758 0.0493143 -0.0476411 0.114061 0.047035 -0.0404899 0.0944099 0.0447265 -0.0465857 0.111311 0.0402503 -0.0452733 0.107724 0.0376092 -0.0414356 0.0974236 0.0395252 -0.0405536 0.094638 0.0512937 -0.041625 0.0975232 0.0562907 -0.0445216 0.105475 0.0586532 -0.0461976 0.110926 0.0563194 -0.0414356 0.0974236 0.0395252 -0.0432705 0.104286 0.0371122 -0.0452733 0.107724 0.0376092 -0.0465857 0.111311 0.0402503 -0.0468123 0.126856 0.0428678 -0.0456058 0.131309 0.0379856 -0.0432038 0.135619 0.0370333 -0.0425509 0.139934 0.0381335 -0.0405722 0.143225 0.0405718 -0.0406645 0.145106 0.0434558 -0.0403274 0.145966 0.0496571</float_array>
					<technique_common>
						<accessor count="138" source="#base2_M1KShape0.positions-array" stride="3">
							<param name="X" type="float"/>
							<param name="Y" type="float"/>
							<param name="Z" type="float"/>
						</accessor>
					</technique_common>
				</source>
				<source id="base2_M1KShape0.normals">
					<float_array id="base2_M1KShape0.normals-array" count="414" digits="2490374">-0.560778 0.610292 0.559529 -0.538608 0.62088 0.56957 -0.766327 0.477818 0.429457 -0.334395 0.69183 0.639962 -0.765666 0.478535 0.429837 -0.234604 0.712556 0.66123 -0.03966 0.730853 0.681382 0.087311 0.728863 0.679069 -0.895549 0.33817 0.289194 -0.567744 0.800203 -0.193241 -0.551187 0.818785 -0.160576 -0.63691 0.718271 -0.280057 -0.97682 0.174083 0.124572 -0.947756 0.248812 0.19963 -0.898513 0.334018 0.28479 0.289451 0.702091 0.650605 0.432419 0.663098 0.610995 0.665579 0.55189 0.502416 0.553053 0.613955 0.563198 0.714968 0.518198 0.469352 0.807506 0.440928 0.391812 0.853989 0.391629 0.342533 0.913811 0.310487 0.261816 0.936009 0.271854 0.22357 0.962842 0.21396 0.164792 0.956654 0.229119 0.179772 -0.964794 -0.159691 -0.208978 -0.88084 -0.309448 -0.358278 -0.907594 -0.271381 -0.320352 -0.971718 -0.140505 -0.189798 -0.859197 -0.336142 -0.385732 -0.84613 -0.351434 -0.400697 0.648385 0.243889 -0.72119 0.683021 0.317621 -0.657723 0.683801 0.424115 -0.593753 0.978154 -0.120272 -0.169556 0.942148 -0.211155 -0.260328 0.916073 -0.257738 -0.307215 0.882641 -0.306551 -0.35633 -0.329857 -0.641291 -0.692777 -0.163395 -0.672164 -0.722147 -0.050454 -0.681184 -0.730372 -0.47941 -0.594141 -0.645881 -0.694816 -0.48343 -0.532472 -0.552433 -0.563529 -0.614209 -0.743147 -0.4482 -0.49684 0.832181 -0.366649 -0.415984 0.818042 -0.381465 -0.430456 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 1.3e-05 0.707188 -0.707026 0.552657 0.829793 -0.07755 0.562825 0.803944 -0.192098 0.604388 0.749744 -0.269443 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 9.2e-05 0.70691 -0.707304 -0.997418 0.069033 0.019807 -0.980436 0.161647 0.112317 -0.998684 -0.001939 -0.051249 -0.999275 0.013712 -0.03551 0.985415 -0.09316 -0.142403 0.995731 0.085074 0.035814 0.998942 0.045868 -0.003438 0.977422 0.172012 0.122714 0.710988 -0.47258 -0.520735 0.555067 -0.563342 -0.612002 0.576865 -0.552851 -0.601317 0.223448 -0.66379 -0.71376 0.292544 -0.650752 -0.700671 -0.340639 0.935898 -0.089774 -0.300488 0.825585 0.477616 -0.226632 0.622666 0.748949 -0.336001 0.923154 -0.186788 0.007754 -0.021303 0.999743 -0.050138 0.137752 0.989197 0.218492 -0.600302 0.76935 0.285222 -0.783642 0.551865 0.324856 -0.892534 0.312813 0.336759 -0.925238 -0.174724 0.325703 -0.894863 -0.305184 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 -0.939683 0.342048 0.000138 0.093176 0.255999 0.962176 0.260865 0.716719 0.646733 0.066759 0.183418 0.980766 -0.199135 -0.547121 0.813022 -0.240058 -0.659555 0.712291 -0.335639 -0.922161 0.192262 -0.339454 -0.932642 0.12227 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 -0.939684 -0.342044 -0.000138 0.21691 -0.595957 -0.773166 -0.000346 0.000951 -1 -0.120315 0.330564 -0.936083 -0.242277 0.665651 -0.70584 -0.301632 -0.828729 -0.471409 -0.150961 -0.414761 -0.897321 -0.003473 -0.009543 -0.999948 0.133302 0.366244 -0.920921 0.250702 0.6888 -0.680223 0.30948 0.850291 -0.425708 0.33799 0.928621 0.153054</float_array>
					<technique_common>
						<accessor count="138" source="#base2_M1KShape0.normals-array" stride="3">
							<param name="X" type="float"/>
							<param name="Y" type="float"/>
							<param name="Z" type="float"/>
						</accessor>
					</technique_common>
				</source>
				<vertices id="base2_M1KShape0.vertices">
					<input semantic="POSITION" source="#base2_M1KShape0.positions"/>
				</vertices>
				<triangles count="115" material="blinn20.material">
					<input offset="0" semantic="VERTEX" source="#base2_M1KShape0.vertices" set="0"/>
					<input offset="1" semantic="NORMAL" source="#base2_M1KShape0.normals" set="0"/>
					<p>0 0 2 2 1 1 3 3 0 0 1 1 1 1 2 2 4 4 5 5 3 3 1 1 6 6 3 3 5 5 7 7 6 6 5 5 8 8 4 4 2 2 9 9 11 11 10 10 8 8 13 13 12 12 14 14 13 13 8 8 15 15 6 6 7 7 15 15 7 7 16 16 17 17 18 18 16 16 19 19 17 17 20 20 18 18 15 15 16 16 19 19 18 18 17 17 21 21 19 19 20 20 22 22 21 21 23 23 22 22 23 23 24 24 25 25 22 22 24 24 26 26 28 28 27 27 29 29 26 26 27 27 28 28 30 30 27 27 30 30 31 31 27 27 32 32 34 34 33 33 35 35 37 37 36 36 38 38 36 36 37 37 39 39 41 41 40 40 42 42 39 39 40 40 43 43 44 44 42 42 44 44 39 39 42 42 45 45 44 44 43 43 45 45 43 43 31 31 38 38 47 47 46 46 37 37 47 47 38 38 48 48 50 50 49 49 51 51 52 52 49 49 48 48 49 49 52 52 53 53 54 54 51 51 52 52 51 51 54 54 51 51 55 55 53 53 56 56 53 53 55 55 57 57 56 56 55 55 58 58 60 60 59 59 60 60 56 56 57 57 59 59 60 60 57 57 61 61 58 58 59 59 2 2 14 14 8 8 62 62 64 64 63 63 65 65 67 67 66 66 68 68 65 65 66 66 69 69 70 70 67 67 66 66 67 67 70 70 69 69 71 71 70 70 72 72 70 70 71 71 71 71 73 73 72 72 72 72 73 73 74 74 73 73 75 75 74 74 75 75 76 76 74 74 77 77 74 74 76 76 20 20 23 23 21 21 78 78 12 12 79 79 80 80 78 78 81 81 29 29 80 80 81 81 79 79 12 12 13 13 81 81 78 78 79 79 29 29 81 81 26 26 45 45 31 31 30 30 82 82 35 35 36 36 83 83 84 84 82 82 25 25 85 85 83 83 84 84 35 35 82 82 85 85 84 84 83 83 24 24 85 85 25 25 86 86 88 88 87 87 46 46 47 47 86 86 89 89 40 40 41 41 90 90 89 89 41 41 88 88 89 89 90 90 87 87 88 88 90 90 47 47 88 88 86 86 91 91 93 93 92 92 94 94 91 91 92 92 93 93 96 96 95 95 96 96 97 97 95 95 98 98 95 95 97 97 98 98 97 97 99 99 99 99 101 101 100 100 102 102 104 104 103 103 104 104 105 105 103 103 105 105 106 106 103 103 103 103 107 107 102 102 107 107 108 108 102 102 108 108 109 109 102 102 109 109 110 110 102 102 111 111 113 113 112 112 111 111 114 114 113 113 115 115 113 113 114 114 114 114 116 116 115 115 117 117 115 115 116 116 118 118 120 120 119 119 120 120 121 121 119 119 121 121 122 122 119 119 119 119 123 123 118 118 123 123 124 124 118 118 124 124 125 125 118 118 125 125 126 126 118 118 127 127 100 100 101 101 127 127 129 129 128 128 94 94 130 130 91 91 131 131 117 117 116 116 132 132 134 134 133 133 133 133 134 134 135 135 134 134 136 136 135 135 137 137 135 135 136 136</p>
				</triangles>
			</mesh>
		</geometry>
		<geometry id="base2_M1KShape1" name="base2_M1KShape1">
			<mesh>
				<source id="base2_M1KShape1.positions">
					<float_array id="base2_M1KShape1.positions-array" count="2325" digits="2490374">-0.0425522 0.137767 0.0587485 -0.0414346 0.143181 0.0577086 -0.0439001 0.138154 0.0600772 -0.045888 0.132261 0.0594795 -0.0420376 0.145077 0.029538 -0.0391491 0.149295 0.0318757 -0.0342063 0.153841 0.0378823 -0.0446075 0.141596 0.0256686 -0.0410909 0.150219 0.0127765 -0.0415223 0.150956 0.0023371 -0.0363569 0.155779 0.0105016 -0.0366728 0.156266 0.000335455 -0.0450829 0.143443 0.0162477 -0.0458072 0.144193 0.00521398 -0.0492791 0.13638 0.00656474 -0.0483447 0.13556 0.0175331 -0.0446075 0.141596 0.0256686 -0.0506179 0.129529 0.0136992 -0.0391491 0.149295 0.0318757 -0.0342063 0.153841 0.0378823 -0.0481237 0.139661 -0.00440001 -0.0452872 0.145185 -0.00498104 -0.0404435 0.151566 -0.00845599 -0.035188 0.156919 -0.00812662 -0.0510685 0.130598 -0.00456679 -0.0523306 0.124969 -5.1856e-05 -0.0366602 0.153127 -0.0162253 -0.0300201 0.161196 0.000165105 -0.0262468 0.162961 0.00992048 -0.0321953 0.159044 0.0112276 -0.0215409 0.165314 -0.00189018 -0.0195145 0.165975 0.00940895 -0.0147261 0.167545 0.00919247 -0.0121441 0.168052 -0.00352681 -0.00987029 0.16868 0.0087471 -0.00484221 0.169401 0.00784957 -0.0023893 0.169265 -0.00449622 -0.0302615 0.157286 0.0403876 -0.0253567 0.161447 0.0315632 -0.0188554 0.16457 0.0318758 -0.0120167 0.166881 0.0317999 -0.00212927 0.168156 0.0347741 -0.0296076 0.160799 -0.00861609 -0.0213259 0.164683 -0.00933933 -0.0158211 0.16579 -0.0129198 -0.00777403 0.167803 -0.0112295 -0.00046951 0.167856 -0.013268 9.8725e-05 0.16962 0.00749803 0.0297176 0.157807 0.0407448 0.0342211 0.153826 0.0378772 0.0179015 0.163817 0.0480664 -0.00412301 0.16544 -0.019626 -0.00380437 0.166923 -0.0155332 -0.00983291 0.164887 -0.0194412 -0.0299793 0.158885 -0.0149536 -0.0232987 0.16173 -0.0170152 -0.0172631 0.160834 -0.0241121 0.00434535 0.165926 -0.0183669 0.00985117 0.164907 -0.019385 0.00338797 0.167227 -0.0149333 0.00831587 0.167806 -0.0108384 0.0160855 0.165726 -0.0128733 0.0233809 0.161642 -0.0171611 0.0216432 0.164579 -0.00920784 0.0293222 0.161013 -0.00824916 0.0304513 0.158691 -0.0148401 0.0339154 0.158597 -0.000213742 0.0370742 0.15517 -0.0087173 0.0366086 0.153382 -0.015873 0.0168914 0.16207 -0.021848 0.00326477 0.164977 -0.0208334 -0.0334381 0.122176 -0.0339645 -0.0294183 0.123226 -0.0328306 -0.0309517 0.130292 -0.0394934 -0.0345975 0.122053 -0.0378914 -0.0332508 0.127319 -0.0384965 -0.0327905 0.118238 -0.0379076 -0.0292897 0.117553 -0.0385143 -0.0327905 0.118238 -0.0379076 -0.0280998 0.121003 -0.042241 -0.0336746 0.141569 -0.0331534 -0.0258656 0.143561 -0.0373387 -0.0296064 0.148376 -0.0313151 -0.0211418 0.151046 -0.0341425 -0.0169588 0.145691 -0.0399122 -0.0117782 0.152902 -0.0359069 -0.00740077 0.147191 -0.0411999 -0.00228457 0.152105 -0.0381047 -0.0300794 0.135885 -0.0385345 -0.0216303 0.137351 -0.0423424 -0.0122629 0.141683 -0.0433756 -0.00237322 0.140074 -0.0454969 -0.0368832 0.148313 -0.0238717 -0.035351 0.134317 -0.0351169 0.00260087 0.142145 -0.0445296 -0.0446075 0.141596 0.0256686 -0.047668 0.135258 0.0196013 -0.0506179 0.129529 0.0136992 -0.0506374 0.127545 0.0118788 -0.05249 0.122036 0.00627017 -0.0520893 0.117058 0.00150311 -0.0494903 0.104293 -0.0112872 -0.0520506 0.111437 -0.00434244 -0.0478766 0.100055 -0.0158848 -0.0432104 0.0921906 -0.0235329 -0.0420376 0.145077 0.029538 -0.0410332 0.107285 -0.0303035 -0.03647 0.110224 -0.03576 -0.0415536 0.117919 -0.0309228 -0.0380566 0.119805 -0.0348859 -0.0387587 0.0864391 -0.0293505 -0.045154 0.103582 -0.0232399 -0.0458562 0.114881 -0.0241052 -0.0489345 0.111747 -0.0168822 -0.0478766 0.100055 -0.0158848 -0.0329224 0.118165 -0.0392698 -0.0353082 0.0840695 -0.0330876 -0.0432104 0.0921906 -0.0235329 -0.051017 0.110482 -0.00957501 -0.0478766 0.100055 -0.0158848 0.0428266 0.147489 -0.0113902 0.043379 0.14807 -0.00594461 0.0475495 0.139688 -0.008816 0.0475713 0.141016 -0.00251639 0.0380487 0.154901 0.000272989 0.0416007 0.150876 0.0020752 0.0458111 0.144195 0.00515962 0.0489267 0.136982 0.00788593 0.0505024 0.133415 -0.0012579 0.0520424 0.125034 0.00918019 0.0130606 0.144059 -0.0420914 0.00722171 0.147337 -0.0411373 0.0145762 0.152463 -0.0354302 0.00690653 0.153101 -0.036708 0.0216305 0.137407 -0.0423239 0.0215183 0.1447 -0.0387785 0.0215528 0.150935 -0.0340428 0.0296373 0.142453 -0.0356911 0.0300554 0.136696 -0.0382463 0.0298276 0.14812 -0.0313613 -0.00235654 0.157505 -0.0328032 0.035326 0.139184 -0.0331115 0.0368736 0.145896 -0.0263432 0.011223 0.156308 -0.0325929 0.0368444 0.14959 -0.0220853 0.0428699 0.144483 -0.0173234 0.0468275 0.137475 -0.0152845 0.049534 0.132693 -0.0110313 0.0514605 0.127833 -0.00532234 0.0524382 0.123374 -0.00038588 0.0433623 0.140715 -0.0207196 -0.0322625 0.109037 -0.0390452 -0.0103 0.111115 -0.0486028 -0.00515787 0.111434 -0.0493578 -0.0119698 0.124021 -0.0480676 -0.00190674 0.122655 -0.0494363 -0.017084 0.110887 -0.0468942 -0.0213508 0.122088 -0.0453873 -0.0262635 0.110213 -0.0429726 -0.00796392 0.131527 -0.0477804 0.000121608 0.130025 -0.0485861 -0.0255593 0.126875 -0.0431302 3.7952e-05 0.109541 -0.0495054 -5.7995e-05 0.0839733 -0.0480765 -0.00570042 0.0843332 -0.0477848 -0.0119619 0.0883969 -0.0469038 -0.0188957 0.0884907 -0.0445843 -0.0254243 0.0885917 -0.0414155 -0.0303382 0.0797659 -0.0372347 -0.0258492 0.129631 -0.0427109 -0.0184193 0.129854 -0.04561 0.0400161 0.139726 -0.0272619 0.0400083 0.135131 -0.0298417 0.0455818 0.134538 -0.0211551 0.0459491 0.126186 -0.0239472 0.0488765 0.128258 -0.016457 0.0508701 0.124374 -0.0113897 0.0520207 0.118229 -0.00637591 0.0487024 0.116224 -0.0183029 0.0430202 0.126768 -0.0285342 0.0373591 0.129484 -0.0346901 0.0298808 0.117268 -0.0387815 0.0291325 0.12318 -0.0328813 0.0262011 0.119273 -0.0368141 0.0262936 0.121579 -0.0345207 0.0321825 0.12275 -0.0333219 0.0341641 0.120616 -0.03547 -0.0216343 0.0744975 -0.0426265 -0.0127778 0.0711883 -0.04592 -0.00238215 0.0695372 -0.047621 -0.0129488 0.0703058 -0.0454519 -0.0127778 0.0711883 -0.04592 -0.0216343 0.0744975 -0.0426265 -0.00238215 0.0695372 -0.047621 -0.0277948 0.0772742 -0.0384772 -0.0303382 0.0797659 -0.0372347 -0.0387587 0.0864391 -0.0293505 -0.0353082 0.0840695 -0.0330876 0.00501943 0.0689004 -0.0468622 0.0406231 0.0886601 -0.0271485 0.0478903 0.10005 -0.0158348 0.044892 0.0962052 -0.0217563 0.0437735 0.142704 0.0270063 0.0492701 0.133025 0.0172057 0.0486492 0.133178 0.0175987 0.0520424 0.125034 0.00918019 0.0517996 0.121936 0.00629246 0.0525166 0.113973 -0.0017941 0.0511142 0.109642 -0.00601566 0.0478903 0.10005 -0.0158348 0.0502446 0.105387 -0.0104072 0.0406231 0.0886601 -0.0271485 0.0355229 0.0845021 -0.0329243 0.0367522 0.0842809 -0.0314326 0.00550013 0.0847455 -0.0478228 0.00825604 0.0701721 -0.0469642 0.0292389 0.0782694 -0.0379703 0.0294628 0.0891129 -0.0388252 0.0216376 0.0744972 -0.0426247 0.0355229 0.0845021 -0.0329243 0.0119719 0.0881036 -0.0468855 0.0154544 0.0714271 -0.0449735 0.0210458 0.0882142 -0.0436842 0.00825604 0.0701721 -0.0469642 0.0216376 0.0744972 -0.0426247 0.0154544 0.0714271 -0.0449735 0.0292389 0.0782694 -0.0379703 -0.0262815 0.118952 -0.0371745 -0.0255593 0.126875 -0.0431302 -0.0265 0.121911 -0.0341649 -0.0258492 0.129631 -0.0427109 -0.0278434 0.131309 -0.0411481 0.0276307 0.131187 -0.0412956 0.0308327 0.130418 -0.0395501 0.025823 0.129512 -0.0427319 0.0257756 0.125887 -0.0431265 0.0262011 0.119273 -0.0368141 0.0280521 0.121445 -0.0422796 0.0309949 0.118991 -0.0405074 0.00299843 0.166875 0.0511156 -0.0100029 0.166068 0.0503097 0.00222967 0.16763 0.05045 -0.0302615 0.157286 0.0403876 -0.0215523 0.16258 0.0460349 -0.0127789 0.165917 0.0488302 -0.00568728 0.167246 0.0500929 0.00222967 0.16763 0.05045 -0.00568728 0.167246 0.0500929 -0.0127789 0.165917 0.0488302 -0.0215523 0.16258 0.0460349 -0.0483447 0.13556 0.0175331 -0.0428126 0.147044 -0.0125359 -0.0430544 0.144363 -0.017028 -0.0478294 0.137671 -0.0115327 -0.0415796 0.139927 -0.0247564 -0.0385649 0.142328 -0.0272906 -0.0456281 0.135871 -0.020259 -0.0495568 0.129133 -0.0141668 -0.0334897 0.154085 -0.0202816 0.0215648 0.162667 0.0454949 0.012779 0.165917 0.0488302 0.0055606 0.167815 0.0354048 0.012779 0.165917 0.0488302 0.0118594 0.166917 0.0319169 0.0211049 0.163619 0.0324119 0.0294009 0.158812 0.0314964 0.0215648 0.162667 0.0454949 0.0297176 0.157807 0.0407448 0.0342211 0.153826 0.0378772 0.00251843 0.158386 -0.0146513 0.00187013 0.163384 -0.00964773 -0.00144825 0.157908 -0.0151423 -0.00407707 0.160096 -0.012939 -0.00307713 0.162649 -0.0104952 0.004094 0.160844 -0.0122219 -0.05249 0.122036 0.00627017 -0.0520506 0.111437 -0.00434244 -0.0518076 0.120794 -0.00787866 -0.0396015 0.134247 -0.0307461 -0.0449556 0.127238 -0.0253891 -0.0402857 0.127989 -0.0319179 -0.0474122 0.122564 -0.0213453 -0.050197 0.119556 -0.0143561 -0.00307713 0.162649 -0.0104952 -0.00380437 0.166923 -0.0155332 -0.00412301 0.16544 -0.019626 -0.00407707 0.160096 -0.012939 -0.000454019 0.164355 -0.0224133 -0.000454019 0.164355 -0.0224133 0.00326477 0.164977 -0.0208334 0.00434535 0.165926 -0.0183669 0.0118896 0.168092 -0.00376105 0.0103217 0.168593 0.00888705 0.00518198 0.169353 0.0086025 0.00470841 0.169076 -0.00434113 0.0151231 0.167431 0.00937259 0.0191368 0.166121 -0.00255346 0.0198595 0.165837 0.00967896 0.0256404 0.163551 -0.00127161 0.0243113 0.163872 0.0102847 0.0305288 0.160347 0.0107062 0.0365556 0.155511 0.0109779 -0.0262069 0.157491 -0.0230207 -0.00746298 0.161713 -0.0264201 -0.0121992 0.157201 -0.031232 -0.021396 0.15529 -0.0295171 -0.0298805 0.152452 -0.026727 0.00241143 0.160258 -0.0292969 -0.0327905 0.118238 -0.0379076 0.00742565 0.161726 -0.0264094 0.0170858 0.159227 -0.0266167 0.0218266 0.157251 -0.0268983 0.0297159 0.155819 -0.0221359 0.0297602 0.152437 -0.026875 0.0411289 0.150179 0.0127236 0.0451227 0.143371 0.0162624 0.0492701 0.133025 0.0172057 0.0386028 0.149591 0.0339376 0.0437735 0.142704 0.0270063 -0.0126842 0.13525 -0.0458741 -0.0278434 0.131309 -0.0411481 0.0126803 0.135245 -0.0458765 0.00779914 0.131392 -0.0478395 0.0170073 0.127566 -0.0465342 0.0276307 0.131187 -0.0412956 0.0341115 0.125392 -0.0380331 0.0459055 0.11507 -0.0240445 0.0415141 0.117986 -0.0309678 0.0380251 0.119995 -0.0349146 0.0506358 0.113796 -0.0120808 0.0339488 0.119268 -0.0384352 0.0341641 0.120616 -0.03547 0.0339488 0.119268 -0.0384352 0.0341115 0.125392 -0.0380331 0.0502446 0.105387 -0.0104072 0.0525166 0.113973 -0.0017941 0.00990948 0.111239 -0.048678 0.00709357 0.123854 -0.0488975 0.00490303 0.111828 -0.0493852 0.0169333 0.110587 -0.0469356 0.0212677 0.12197 -0.0454217 0.0262655 0.110265 -0.0429757 0.0337878 0.110426 -0.0380712 0.0257756 0.125887 -0.0431265 0.0389517 0.10795 -0.0328888 0.0428427 0.105906 -0.0275143 0.044892 0.0962052 -0.0217563 0.0406231 0.0886601 -0.0271485 -0.0458063 0.128571 0.0557251 -0.0472439 0.125609 0.0542737 -0.039116 0.147244 0.0504305 -0.0405722 0.143225 0.0405718 -0.0416472 0.144351 0.0389673 -0.046774 0.125758 0.0493143 -0.0458063 0.128571 0.0557251 -0.0451474 0.137504 0.035581 -0.0432038 0.135619 0.0370333 -0.0456058 0.131309 0.0379856 -0.0470016 0.131851 0.0363153 -0.0474944 0.127536 0.0399544 -0.0468123 0.126856 0.0428678 -0.0467372 0.114103 0.0459944 -0.0475391 0.116125 0.0480255 -0.046398 0.113135 0.0531317 -0.0477179 0.113598 0.0411936 -0.0392122 0.0927753 0.050741 -0.0407491 0.0955714 0.0562894 -0.0367507 0.0897074 0.0524691 0.00174608 0.0724561 0.0594642 -0.0073843 0.0729467 0.0591476 -0.0023717 0.0724908 0.0735 -0.0117535 0.0739111 0.073501 -0.0172501 0.0757027 0.0592071 -0.0204868 0.0770787 0.0734998 -0.0326854 0.0853411 0.0558456 -0.0282585 0.0817593 0.0735 -0.0252286 0.0796976 0.0576514 -0.0350978 0.0879253 0.0735 -0.0393831 0.09345 0.0735 -0.0439158 0.10182 0.0602083 -0.0429103 0.099507 0.0735 -0.0460807 0.1082 0.0735 -0.0466767 0.110875 0.0580604 -0.0474982 0.117759 0.0735 -0.047031 0.127099 0.0735 -0.0475199 0.117948 0.0533962 -0.0447015 0.136263 0.0735 -0.0408024 0.14443 0.0735 -0.0359918 0.151154 0.0535278 -0.0352259 0.151933 0.0735 -0.0302191 0.156785 0.0521806 -0.0281337 0.158316 0.0735 -0.0220002 0.16209 0.0553524 -0.0203664 0.162977 0.0735 -0.016475 0.164565 0.0516728 -0.0117988 0.166072 0.0735 -0.00968518 0.166519 0.0536069 -0.00485371 0.167255 0.0735 -0.00146513 0.167574 0.0546539 0.00209106 0.167534 0.0735 0.0460182 0.132034 0.0511477 0.0445056 0.136996 0.0494279 0.047565 0.130847 0.0343426 0.0366959 0.151363 0.0384628 0.0392375 0.146812 0.061751 0.0353748 0.151808 0.0528071 0.0417655 0.143036 0.0522914 0.0447519 0.138477 0.0359812 0.0419444 0.145009 0.0321637 0.00844284 0.166806 0.0538777 0.0117915 0.166074 0.0735022 0.0149902 0.165049 0.0521599 0.0184817 0.163765 0.0735 0.0217944 0.162199 0.0554388 0.0244912 0.160761 0.0735002 0.0302071 0.156648 0.0735 0.0301217 0.156826 0.0533012 0.0352297 0.15193 0.0735 0.0407622 0.144492 0.0735 0.0447416 0.136149 0.0735 0.0472254 0.12613 0.0520699 0.0470429 0.127075 0.0735 0.0475096 0.117768 0.0534058 0.0474967 0.117595 0.0735 0.0464761 0.109663 0.0508679 0.0437956 0.101465 0.0523547 0.0460867 0.108219 0.0735 0.0429007 0.0994832 0.0735 0.0393831 0.09345 0.0735 0.0396304 0.0936116 0.0537378 0.0337464 0.0864635 0.0563426 0.0351035 0.0879312 0.0735 0.0296882 0.0828425 0.0565051 0.0282757 0.0817693 0.0735 0.0245378 0.0792623 0.0555416 0.0205335 0.0771058 0.0735 0.0189504 0.0764201 0.0587518 0.0120596 0.0739925 0.0735 0.0106399 0.0736481 0.0593375 0.00485371 0.0727454 0.0735 -0.00750149 0.0722594 0.0544585 -0.0328488 0.0704518 0.0352985 -0.0302574 0.0755733 0.0408012 -0.0257673 0.0687526 0.0408854 -0.0215984 0.0709558 0.0448886 -0.0170719 0.066746 0.0451649 -0.0145474 0.0700735 0.0480494 -0.00744551 0.0664532 0.0480056 -0.0215432 0.0743174 0.0479633 -0.0276415 0.0773907 0.0454012 -0.0118683 0.0713824 0.0507035 -0.00504968 0.070263 0.0514387 -0.0314555 0.0817829 0.0460811 -0.0298286 0.0819874 0.0502969 -0.0235983 0.0779185 0.0518228 -0.0169714 0.0747953 0.0532792 -0.0305222 0.0636635 0.0365009 -0.0246152 0.0621362 0.0406132 -0.019636 0.0613268 0.0432578 -0.0134173 0.06065 0.0456058 -0.00699787 0.0605277 0.0470151 0.000772483 0.060133 0.0475833 0.00225693 0.0646357 0.0481282 0.0017136 0.0671811 0.0492551 0.00235361 0.0698759 0.0515001 7.7117e-05 0.071579 0.0545301 -0.0362196 0.0865112 0.0442513 -0.0362128 0.0804489 0.0369039 -0.0363392 0.0742464 0.0326476 0.0460387 0.13844 0.0254773 0.0499359 0.130233 0.0172867 -0.0362997 0.0628604 0.0307193 -0.0476723 0.0713073 0.00169921 -0.0475041 0.073753 -0.00510263 -0.0487449 0.0847829 0.00198805 -0.0488801 0.0869425 -0.00414467 -0.0514234 0.10293 0.000592828 -0.0516017 0.105188 0.00711489 -0.0490139 0.090811 0.00923669 -0.0505548 0.103639 0.0138401 -0.0518838 0.111493 0.0119613 -0.0488712 0.116232 0.0341362 -0.051798 0.118904 0.0145206 -0.0486047 0.122153 0.0361404 -0.047621 0.124328 0.0460588 -0.0468923 0.0709186 0.00828481 -0.0480211 0.0841654 0.00870001 -0.048639 0.10829 0.0284876 -0.052687 0.115015 0.00212789 -0.0473162 0.0831407 -0.0106926 -0.0474243 0.0487501 0.00277293 -0.0471521 0.0487502 -0.00630355 0.0257625 0.068761 0.0408933 0.0313438 0.0704723 0.0367612 0.0272703 0.0631062 0.0389947 0.0302572 0.0755731 0.0408012 0.0258666 0.0750746 0.0449737 0.0299177 0.0798539 0.0458825 0.0215984 0.0709558 0.0448886 0.0214115 0.0751163 0.0490273 0.0298491 0.0819002 0.049898 0.017012 0.0667425 0.0451919 0.0169548 0.0747929 0.05329 0.0122766 0.0687139 0.047976 0.0118683 0.0713824 0.0507035 0.00955196 0.0620458 0.0466191 0.00739511 0.0722203 0.0544157 0.0334577 0.064071 0.0338311 0.0372898 0.0663589 0.0296316 0.0361522 0.0736775 0.0326297 0.0361994 0.0804026 0.0368878 0.0361638 0.0855975 0.0429536 0.0361358 0.0881319 0.0486184 0.0155855 0.0608157 0.0448517 0.0214119 0.0617631 0.0424303 0.0405737 0.0934925 0.0445502 0.0416863 0.0684286 0.0229863 0.040606 0.0767279 0.0269574 0.0431648 0.0780695 0.0221059 0.0405831 0.0843202 0.0317296 0.0434656 0.08814 0.0275638 0.0459635 0.0802893 0.0152537 0.0465983 0.09287 0.0215144 0.0421256 0.0924517 0.0368977 0.0464919 0.101034 0.0307499 0.0431864 0.0986107 0.0440913 0.0459856 0.10534 0.0412077 0.0474063 0.0709818 0.00485861 0.0480206 0.0841641 0.00870025 0.0489834 0.0905715 0.0091821 0.0483351 0.110161 0.0320079 0.0476018 0.114662 0.0451725 0.0443786 0.0681297 0.0170079 0.0461332 0.0686722 0.0115855 0.0440539 0.0908788 -0.0220326 0.0457412 0.0826802 -0.0156577 0.0487771 0.0998834 -0.0130292 0.0421048 0.0805024 -0.0235375 -0.0408572 0.0865258 -0.0264835 -0.0359674 0.080788 -0.0321447 -0.0380179 0.0838565 -0.0290754 -0.0298019 0.0754562 -0.0374699 -0.0281893 0.0751863 -0.0377421 -0.0224205 0.0708316 -0.0420965 -0.0149534 0.0678329 -0.0450612 -0.0176088 0.0697248 -0.0432035 -0.00837539 0.0661014 -0.0468225 -0.00556928 0.0666279 -0.0463047 -0.000222317 0.0653583 -0.0475461 0.00692719 0.0658871 -0.0470308 0.0122735 0.066994 -0.0459025 0.00772627 0.0670238 -0.0459076 0.0219944 0.0714185 -0.0415126 0.0208679 0.0699236 -0.0430033 0.0375405 0.0823103 -0.0306243 0.0359597 0.0816075 -0.0313153 0.0295276 0.0752462 -0.0376855 0.0440539 0.0908788 -0.0220326 0.0441327 0.0921691 -0.0207589 0.0487771 0.0998834 -0.0130292 0.0507533 0.106214 -0.00672114 0.0512778 0.106547 -0.0063591 0.0520886 0.117914 0.0049783 0.052658 0.114491 0.00157869 0.052156 0.122836 0.00992119 0.0499359 0.130233 0.0172867 0.050529 0.127002 0.0140727 0.0354595 0.151732 0.0387936 0.0460387 0.13844 0.0254773 0.0419444 0.145009 0.0321637 0.0366959 0.151363 0.0384628 0.029838 0.15745 0.0445217 0.022344 0.162138 0.0492101 0.0265899 0.158707 0.0457829 0.0149902 0.165049 0.0521599 0.0156187 0.164019 0.0510894 0.00844284 0.166806 0.0538777 -0.00146513 0.167574 0.0546539 0.00298516 0.166454 0.0535179 -0.00812437 0.165825 0.0528969 -0.0201715 0.16226 0.0493321 -0.016475 0.164565 0.0516728 -0.00968518 0.166519 0.0536069 -0.0323943 0.154552 0.0416319 -0.0277441 0.159056 0.0461289 -0.0388465 0.149241 0.0363281 -0.0419911 0.144181 0.0312456 -0.0476179 0.135685 0.0227461 -0.0510784 0.126866 0.0139418 -0.0520625 0.114653 0.00172722 -0.0524394 0.119407 0.00638306 -0.052687 0.115015 0.00212789 -0.0510414 0.105786 -0.00714314 -0.0501627 0.1046 -0.00831926 -0.0478149 0.0976831 -0.0152689 0.0463286 0.0722505 -0.0111021 0.0481345 0.0851902 -0.00768924 0.0512778 0.106547 -0.0063591 0.052156 0.122836 0.00992119 0.0479487 0.121838 0.0439506 0.0513256 0.102237 0.000326037 0.052658 0.114491 0.00157869 0.0473574 0.0708237 -0.00530612 0.0490204 0.123114 0.0325109 0.0388645 0.0738483 -0.0281055 0.0375405 0.0823103 -0.0306243 0.0339818 0.0686876 -0.0334222 0.0443566 0.0729399 -0.0176675 0.0445083 0.0487504 -0.0166001 0.0410668 0.0582402 -0.0238935 0.0404678 0.0487503 -0.0249658 -0.00485371 0.04875 -0.04725 0.002529 0.04875 -0.0475105 -0.000222317 0.0653583 -0.0475461 -0.00837539 0.0661014 -0.0468225 -0.0118286 0.0487477 -0.0460614 -0.0149534 0.0678329 -0.0450612 -0.0204469 0.0487498 -0.0429406 -0.0218331 0.0644379 -0.0421721 -0.0282493 0.0487501 -0.0382427 -0.0351815 0.04875 -0.0319858 -0.0302131 0.0672244 -0.0367732 -0.0380647 0.0660378 -0.0285796 -0.0407601 0.04875 -0.0244915 -0.0353411 0.0672525 -0.0318501 -0.0414868 0.0672583 -0.0235234 -0.0442758 0.0698391 -0.0176384 -0.0447708 0.04875 -0.0160772 -0.0465016 0.0673686 -0.0101405 -0.0455338 0.0715353 0.0139059 -0.0460903 0.04875 0.0117693 -0.0437136 0.0702074 0.0188317 -0.0429074 0.04875 0.0204999 -0.0399422 0.0663832 0.0258828 -0.0381685 0.04875 0.0283643 -0.0320284 0.04875 0.0351397 -0.0246391 0.04875 0.040674 -0.0161813 0.04875 0.0447252 -0.00705179 0.04875 0.047034 0.00244007 0.04875 0.0474997 -0.0400456 0.0954711 0.0540533 -0.0394596 0.094061 0.0460793 -0.0405536 0.094638 0.0512937 -0.0466046 0.103961 0.0341833 -0.0453792 0.102023 0.0356203 -0.0431312 0.0959513 0.0379997 -0.0464421 0.108061 0.0370603 -0.0402916 0.0928008 0.0441656 -0.0465857 0.111311 0.0402503 -0.0409795 0.143531 0.0548582 -0.0403274 0.145966 0.0496571 -0.0277441 0.159056 0.0461289 -0.0388465 0.149241 0.0363281 -0.046398 0.113135 0.0531317 -0.0476179 0.135685 0.0227461 -0.0430675 0.104092 0.058776 -0.0461976 0.110926 0.0563194 -0.0432705 0.104286 0.0371122 -0.0452733 0.107724 0.0376092 -0.0421177 0.100071 0.0375472 -0.0414356 0.0974236 0.0395252 -0.0394596 0.094061 0.0460793 0.022344 0.162138 0.0492101 0.029838 0.15745 0.0445217 -0.0204868 0.0770787 0.0734998 -0.0023717 0.0724908 0.0735 -0.0117535 0.0739111 0.073501 0.00209106 0.167534 0.0735 0.00485371 0.0727454 0.0735 0.0120596 0.0739925 0.0735 0.0205335 0.0771058 0.0735 0.0282757 0.0817693 0.0735 0.0351035 0.0879312 0.0735 0.0393831 0.09345 0.0735 0.0429007 0.0994832 0.0735 0.0460867 0.108219 0.0735 0.0474967 0.117595 0.0735 0.0470429 0.127075 0.0735 0.0447416 0.136149 0.0735 0.0407622 0.144492 0.0735 0.0352297 0.15193 0.0735 0.0302071 0.156648 0.0735 0.0244912 0.160761 0.0735002 0.0184817 0.163765 0.0735 0.0117915 0.166074 0.0735022 -0.00485371 0.167255 0.0735 -0.0117988 0.166072 0.0735 -0.0203664 0.162977 0.0735 -0.0281337 0.158316 0.0735 -0.0352259 0.151933 0.0735 -0.0408024 0.14443 0.0735 -0.0447015 0.136263 0.0735 -0.047031 0.127099 0.0735 -0.0474982 0.117759 0.0735 -0.0460807 0.1082 0.0735 -0.0429103 0.099507 0.0735 -0.0393831 0.09345 0.0735 -0.0350978 0.0879253 0.0735 -0.0282585 0.0817593 0.0735 -0.0405846 0.0760743 0.0267056 -0.040561 0.0841786 0.0316513 -0.0407063 0.0907392 0.0386257 -0.0433897 0.0800931 0.0225432 -0.04357 0.0894247 0.0282894 -0.0462407 0.0834427 0.0161096 -0.0465919 0.092998 0.021636 -0.0408572 0.0865258 -0.0264835 -0.0478149 0.0976831 -0.0152689 -0.043442 0.0846506 -0.0218066 -0.0510414 0.105786 -0.00714314 -0.0510784 0.126866 0.0139418 -0.0524394 0.119407 0.00638306 -0.0359674 0.080788 -0.0321447 -0.0298019 0.0754562 -0.0374699 -0.0224205 0.0708316 -0.0420965 0.0476105 0.0703238 7.7486e-05 0.0488118 0.0847092 0.000280261 0.0519453 0.10835 0.00870478 0.0514053 0.115599 0.0172155 0.0488815 0.116098 0.0340195 0.0505298 0.103489 0.0138415 0.0470317 0.04875 0.00714028 0.0474897 0.0487501 -0.000109553 0.0204904 0.0487504 0.0429167 0.00244007 0.04875 0.0474997 0.0117409 0.0487482 0.0460887 -0.00485371 0.04875 -0.04725 0.002529 0.04875 -0.0475105 -0.00705179 0.04875 0.047034 -0.0161813 0.04875 0.0447252 -0.0246391 0.04875 0.040674 -0.0320284 0.04875 0.0351397 -0.0381685 0.04875 0.0283643 -0.0429074 0.04875 0.0204999 -0.0460903 0.04875 0.0117693 -0.0474243 0.0487501 0.00277293 -0.0471521 0.0487502 -0.00630355 -0.0447708 0.04875 -0.0160772 -0.0407601 0.04875 -0.0244915 -0.0351815 0.04875 -0.0319858 -0.0282493 0.0487501 -0.0382427 -0.0204469 0.0487498 -0.0429406 -0.0118286 0.0487477 -0.0460614 0.00956787 0.04875 -0.04653 0.0166534 0.04875 -0.0445486 0.0247791 0.04875 -0.0405655 0.0302071 0.04875 -0.0366501 0.0350139 0.04875 -0.0321764 0.0404678 0.0487503 -0.0249658 0.0445083 0.0487504 -0.0166001 0.0470158 0.04875 -0.00713515 0.0474897 0.0487501 -0.000109553 0.0470317 0.04875 0.00714028 0.0446706 0.04875 0.0163162 0.0406151 0.04875 0.0247296 0.0352626 0.04875 0.0318822 0.0283668 0.0487499 0.0381655 0.019253 0.0634816 -0.0434273 0.0122735 0.066994 -0.0459025 0.0166534 0.04875 -0.0445486 0.0269001 0.0656132 -0.0391898 0.0247791 0.04875 -0.0405655 0.0208679 0.0699236 -0.0430033 0.0295276 0.0752462 -0.0376855 0.0117409 0.0487482 0.0460887 0.0204904 0.0487504 0.0429167 0.0283668 0.0487499 0.0381655 0.0352626 0.04875 0.0318822 0.0406151 0.04875 0.0247296 0.0446706 0.04875 0.0163162 0.0470158 0.04875 -0.00713515 0.0350139 0.04875 -0.0321764 0.0302071 0.04875 -0.0366501 0.00692719 0.0658871 -0.0470308 0.00956787 0.04875 -0.04653</float_array>
					<technique_common>
						<accessor count="775" source="#base2_M1KShape1.positions-array" stride="3">
							<param name="X" type="float"/>
							<param name="Y" type="float"/>
							<param name="Z" type="float"/>
						</accessor>
					</technique_common>
				</source>
				<source id="base2_M1KShape1.normals">
					<float_array id="base2_M1KShape1.normals-array" count="2325" digits="2490374">-0.796969 0.038713 -0.602778 -0.916941 0.372185 -0.143866 -0.924714 0.375873 -0.060191 -0.943319 0.298303 -0.145484 -0.881365 0.334164 0.333961 -0.849954 0.37245 0.372638 -0.784126 0.438697 0.438966 -0.910405 0.293752 0.29133 -0.783911 0.609921 0.116107 -0.788593 0.614257 0.028471 -0.675979 0.72982 0.102054 -0.674755 0.737971 0.010217 -0.875418 0.468524 0.118866 -0.875296 0.480656 0.05316 -0.937563 0.34077 0.069649 -0.937679 0.316367 0.143775 -0.891606 0.434759 0.126585 -0.970054 0.181459 0.161456 -0.790508 0.6079 0.074531 -0.709802 0.70035 0.075445 -0.923095 0.377073 -0.075572 -0.863962 0.499693 -0.062259 -0.762119 0.63091 -0.145352 -0.645531 0.752796 -0.128792 -0.971562 0.22217 -0.081899 -0.991607 0.127642 -0.020555 -0.680082 0.669655 -0.298413 -0.517508 0.855678 0.000902 -0.470777 0.880059 0.062179 -0.589218 0.803021 0.08933 -0.364263 0.930577 -0.036603 -0.35053 0.935543 0.043452 -0.266619 0.963134 0.035875 -0.21103 0.975121 -0.067865 -0.18111 0.982982 0.030759 -0.091057 0.995485 0.026796 -0.046269 0.995319 -0.084854 -0.625455 0.777364 0.06717 -0.502792 0.860557 0.081496 -0.375033 0.923907 0.075803 -0.241316 0.967618 0.074037 -0.043997 0.997393 0.057201 -0.507553 0.849818 -0.142122 -0.355558 0.922135 -0.152464 -0.262737 0.93417 -0.241446 -0.132257 0.970676 -0.20074 0.011643 0.777473 -0.628809 0.001789 0.999707 0.024155 0.714935 0.494438 0.494367 0.782733 0.440002 0.440144 0.484386 0.618615 0.618615 0.066613 0.93627 -0.344908 0.371783 0.775156 -0.510795 -0.160383 0.911999 -0.377539 -0.5099 0.814524 -0.276683 -0.383587 0.865421 -0.322348 -0.276371 0.835058 -0.475708 -0.026425 0.93703 -0.348249 0.15987 0.912176 -0.377329 -0.541217 0.603722 -0.585324 0.141639 0.969809 -0.19852 0.267392 0.932045 -0.244529 0.384808 0.863903 -0.324953 0.362414 0.918232 -0.159707 0.504055 0.852514 -0.138382 0.520699 0.808976 -0.272819 0.602469 0.798135 0.003331 0.681815 0.719844 -0.130202 0.672633 0.6768 -0.299175 0.272534 0.863411 -0.424553 -0.416297 0.908455 -0.0375 0.767559 -0.311571 -0.560158 -0.114199 -0.063109 -0.991451 -0.011919 -0.45661 -0.889587 -0.377058 -0.019544 -0.925983 -0.187906 -0.126583 -0.973996 0.240872 0.873766 -0.422509 -0.063732 0.897713 -0.435947 0.43872 0.890503 -0.120535 -0.759811 0.396792 -0.515019 -0.598657 0.422907 -0.680264 -0.426055 0.471646 -0.772028 -0.497846 0.580597 -0.644249 -0.338454 0.632617 -0.696595 -0.270958 0.515627 -0.812841 -0.187707 0.66355 -0.724201 -0.118239 0.543955 -0.830742 -0.037312 0.642448 -0.76542 -0.510124 0.285433 -0.811358 -0.352273 0.327943 -0.87656 -0.198628 0.413865 -0.888405 -0.041251 0.392515 -0.91882 -0.677497 0.566994 -0.468525 -0.639946 0.259269 -0.723359 0.043187 0.434664 -0.899557 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.000758 -0.7071 0.707113 -0.786001 -0.103807 -0.609448 -0.678141 -0.095717 -0.728672 -0.789304 -0.026243 -0.613441 -0.707022 -0.006711 -0.70716 -0.785953 -0.104133 -0.609453 -0.876176 -0.12241 -0.466187 -0.876003 -0.05468 -0.479197 -0.930695 -0.074743 -0.358079 -0.938754 -0.187358 -0.289202 -0.565509 0.066818 -0.822031 -0.728539 -0.080735 -0.68023 -0.86928 -0.122552 -0.478887 -0.970619 -0.081265 -0.226482 -0.939988 -0.238763 -0.243754 0.820157 0.527191 -0.222289 0.821833 0.567052 -0.055162 0.909907 0.383406 -0.158331 0.90439 0.426421 -0.015632 0.713338 0.700773 -0.008127 0.79727 0.603605 -0.004732 0.882202 0.470432 0.020329 0.93643 0.346906 0.052484 0.959189 0.282118 -0.019154 0.986404 0.127729 0.103403 0.209221 0.467742 -0.858745 0.115412 0.546907 -0.8292 0.231947 0.6571 -0.717231 0.111602 0.665139 -0.738332 0.353568 0.329272 -0.87554 0.347428 0.495797 -0.795914 0.345455 0.630771 -0.694829 0.501672 0.447038 -0.740596 0.511271 0.307163 -0.802654 0.503079 0.57514 -0.645078 -0.037394 0.752002 -0.658099 0.640526 0.365951 -0.675135 0.678843 0.514331 -0.524058 0.178978 0.732339 -0.656998 0.677746 0.596532 -0.429895 0.818167 0.478501 -0.318808 0.897893 0.337583 -0.282535 0.946568 0.248898 -0.205084 0.980459 0.166528 -0.104735 0.995688 0.089062 -0.025952 0.828411 0.397707 -0.394416 -0.589307 -0.091634 -0.802696 -0.187968 -0.032115 -0.98165 -0.096327 -0.031204 -0.994861 -0.206227 0.070792 -0.97594 -0.04021 0.059407 -0.997424 -0.307058 -0.04091 -0.950811 -0.344905 0.036648 -0.937922 -0.469348 -0.066306 -0.88052 -0.135128 0.213106 -0.96764 0.001437 0.183844 -0.982954 -0.693424 0.140337 -0.706731 -0.000211 -0.035391 -0.999373 -0.001543 -0.053357 -0.998574 -0.11607 -0.051714 -0.991894 -0.239861 -0.070392 -0.968252 -0.376477 -0.075268 -0.923363 -0.504367 -0.072946 -0.860403 -0.627144 -0.066353 -0.776072 -0.509224 0.143948 -0.84851 -0.307675 0.172086 -0.9358 0.754694 0.372791 -0.539873 0.755936 0.273262 -0.594885 0.873073 0.260576 -0.412121 0.875052 0.069851 -0.478963 0.933269 0.142243 -0.329812 0.96789 0.07538 -0.239807 0.98836 -0.010878 -0.151746 0.928961 -0.027357 -0.369166 0.813179 0.079618 -0.576542 0.70079 0.173994 -0.691823 0.099136 0.927733 -0.359838 0.149986 0.094915 -0.984122 0.866429 0.487568 -0.107602 0.848151 -0.054137 -0.526981 -0.451804 -0.17676 -0.874431 -0.624166 0.623701 -0.470547 -0.455577 -0.050717 -0.888751 -0.272089 -0.030217 -0.961798 -0.050252 -0.022476 -0.998484 -0.363689 -0.658684 -0.658684 -0.358445 -0.66012 -0.66012 -0.565244 -0.583307 -0.583311 -0.069705 -0.705387 -0.705387 -0.681203 -0.5177 -0.517639 -0.724477 -0.487447 -0.48737 -0.845087 -0.377984 -0.378095 -0.803089 -0.421137 -0.421535 0.14688 -0.699438 -0.699438 0.865586 -0.354155 -0.354028 0.939307 -0.215899 -0.266628 0.913449 -0.281173 -0.294199 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.000629 -0.707149 0.707064 0.810566 -0.413943 -0.414289 0.817271 -0.407634 -0.407312 0.112139 -0.053561 -0.992248 0.17692 -0.028859 -0.983802 0.607564 -0.068772 -0.791288 0.584575 -0.078716 -0.807512 0.455642 -0.050715 -0.888717 0.730838 -0.068766 -0.679078 0.24077 -0.069011 -0.968126 0.32947 -0.032322 -0.943613 0.41867 -0.073623 -0.905149 0.236097 -0.687117 -0.687117 0.565231 -0.583315 -0.583315 0.423858 -0.640447 -0.640447 0.704527 -0.501833 -0.501802 -0.862927 0.427834 0.268914 -0.989175 0.14401 0.028175 -0.844881 -0.192225 -0.499224 -0.914745 -0.234988 -0.328667 -0.484364 -0.566141 -0.66699 0.553639 -0.563071 -0.613543 0.077153 -0.471504 -0.878482 0.694197 0.023894 -0.719389 0.943275 0.219917 0.248735 0.916768 0.273647 0.290953 0.662673 0.269886 -0.698589 0.604108 0.225754 -0.764257 0.087099 0.70442 0.70442 -0.286863 0.677388 0.677388 0.064679 0.705626 0.705626 -0.724548 0.487363 0.487347 -0.560716 0.58549 0.58549 -0.35802 0.660235 0.660235 -0.163945 0.697539 0.697539 0.047057 0.998641 0.022403 -0.119863 0.992547 0.021964 -0.272301 0.961702 0.031344 -0.455773 0.888776 0.048462 -0.946528 0.225366 0.230855 -0.81685 0.535046 -0.215598 -0.821966 0.476011 -0.312708 -0.915284 0.348302 -0.202341 -0.789202 0.377344 -0.484532 -0.71995 0.432713 -0.542615 -0.873266 0.291239 -0.390624 -0.945431 0.164623 -0.281176 -0.591811 0.70303 -0.394346 0.563417 0.584192 0.584192 0.358082 0.660219 0.660219 0.112961 0.992213 0.052475 0.272289 0.961705 0.031334 0.237878 0.968507 0.073549 0.419779 0.904287 0.077791 0.583977 0.807492 0.083234 0.454497 0.889403 0.048932 0.614635 0.785715 0.069823 0.708994 0.700714 0.079547 -0.520807 0.846838 -0.107819 -0.299922 0.245877 -0.921733 0.315247 0.9063 0.281496 0.788085 0.495576 -0.365138 0.090952 0.762866 -0.640127 -0.92878 0.221093 -0.297464 -0.995396 0.067551 0.068004 -0.98917 -0.095818 -0.111182 -0.983765 0.023141 -0.177963 -0.745946 0.25447 -0.615475 -0.857446 0.102028 -0.504357 -0.761202 0.113577 -0.638492 -0.90064 0.015319 -0.434296 -0.952441 -0.011461 -0.304509 0.73419 -0.416703 -0.536026 0.873387 -0.335049 -0.353465 0.912807 0.23319 0.335271 0.942607 0.332195 -0.033757 0.11454 0.773473 0.623394 0.06231 0.972834 0.22296 -0.768024 0.472716 0.432063 -0.989842 0.068828 0.124397 0.206637 0.975958 -0.069334 0.188856 0.981513 0.031073 0.097026 0.994808 0.030719 0.08782 0.992809 -0.081355 0.273444 0.96118 0.036891 0.324367 0.944826 -0.045714 0.356709 0.933115 0.045337 0.434741 0.900297 -0.021561 0.437057 0.897542 0.058312 0.552612 0.829549 0.080425 0.677778 0.727752 0.104857 -0.433146 0.776315 -0.457951 -0.141889 0.814066 -0.563173 -0.194224 0.751919 -0.629996 -0.343357 0.721956 -0.600737 -0.505522 0.669728 -0.543978 0.040906 0.791925 -0.609246 0.587474 0.717233 0.374768 0.128401 0.830659 -0.541773 0.272821 0.799638 -0.534928 0.350746 0.762022 -0.544334 0.50411 0.743542 -0.439338 0.502519 0.669291 -0.547287 0.784529 0.6091 0.116236 0.875974 0.467187 0.120022 0.952305 0.257809 0.163249 0.785449 0.614927 0.070249 0.879738 0.459035 0.123886 -0.211491 0.301207 -0.929809 -0.476571 0.157915 -0.864837 0.211402 0.300758 -0.929975 0.133152 0.211425 -0.968282 0.285176 0.116642 -0.951351 0.466499 0.149601 -0.871779 0.019006 -0.108418 -0.993924 0.88336 -0.019892 -0.468274 0.795365 0.003746 -0.606119 0.712215 0.009445 -0.701898 0.962681 -0.04418 -0.267009 0.485024 0.116947 -0.866646 -0.988359 -0.114935 -0.09968 -0.806285 0.477761 0.348782 -0.860938 -0.440078 -0.25518 0.964425 -0.138329 -0.225278 0.995831 -0.064458 -0.064536 0.181393 -0.032258 -0.982881 0.127466 0.07315 -0.989142 0.091692 -0.028396 -0.995382 0.304775 -0.042523 -0.951475 0.343962 0.029425 -0.938523 0.469118 -0.065708 -0.880688 0.618652 -0.088241 -0.780694 0.65666 0.198416 -0.727618 0.736109 -0.116828 -0.666705 0.826503 -0.110551 -0.551971 0.883622 -0.140175 -0.446725 0.822534 -0.108584 -0.558254 -0.909267 0.403218 -0.10319 -0.985755 0.16612 -0.026302 -0.860261 0.509843 -0.003151 -0.947282 -0.08786 0.308119 -0.91508 0.343004 0.212076 -0.861401 0.507157 -0.027933 -0.748405 0.620901 -0.233178 -0.898798 0.317705 0.302035 -0.76479 0.249182 0.594142 -0.257435 0.563859 0.784723 -0.934062 0.302482 0.189821 -0.967442 0.205868 0.147225 -0.790689 0.582778 0.187566 -0.85789 -0.512004 0.043319 -0.986132 -0.161699 0.03737 -0.922658 -0.385076 0.020471 -0.90115 -0.357751 0.244832 -0.885525 -0.462588 0.043095 -0.884785 -0.462384 -0.05793 -0.779292 -0.625011 0.045453 0.037447 -0.998921 0.027466 -0.155519 -0.987666 0.018162 -0.049113 -0.998793 -5.7e-05 -0.246364 -0.969177 -0.000162 -0.364552 -0.930967 0.020066 -0.431392 -0.902165 0.000443 -0.690488 -0.723069 0.019941 -0.594976 -0.803743 -3.8e-05 -0.53236 -0.846301 0.019187 -0.73869 -0.674045 -0.000713 -0.828657 -0.559757 0 -0.92593 -0.367643 -0.086555 -0.902247 -0.431215 0.001847 -0.968837 -0.247681 0.002934 -0.97341 -0.226272 -0.035688 -0.998949 -0.045837 0.000404 -0.988744 0.149603 -0.001919 -0.999146 -0.037346 0.017657 -0.939683 0.342045 0.001302 -0.857225 0.514941 0.000717 -0.749563 0.661727 0.016505 -0.740279 0.6723 0.000576 -0.629659 0.776689 0.016843 -0.594672 0.803935 -0.007277 -0.463272 0.886164 0.009589 -0.42897 0.903318 0.000277 -0.351599 0.935906 0.021408 -0.247571 0.96887 8.1e-05 -0.207431 0.97818 0.011674 -0.10089 0.994898 0 -0.031056 0.999501 0.005732 0.043571 0.99905 -0.000409 0.968128 0.249875 0.017039 0.939963 0.340493 0.023126 0.963125 0.243986 0.113405 0.747956 0.6608 0.062482 0.825379 0.564449 0.012118 0.738716 0.673774 0.018054 0.878662 0.476632 0.027834 0.922742 0.375101 0.088582 0.8585 0.505654 0.085393 0.18072 0.983456 0.012462 0.247224 0.968958 -4e-05 0.32016 0.947178 0.018752 0.389438 0.921053 0 0.45913 0.888328 0.008504 0.516939 0.856013 -0.003891 0.636476 0.771296 0 0.628605 0.777606 0.013577 0.74057 0.671979 -0.000387 0.856683 0.515844 0.000277 0.940559 0.33963 0.000627 0.991679 0.127559 0.017349 0.989093 0.147289 -0.000696 0.998891 -0.044577 0.015166 0.998782 -0.049343 8.8e-05 0.975503 -0.219248 0.018009 0.92156 -0.387825 0.017877 0.969023 -0.246971 -0.000131 0.901829 -0.432094 -4.1e-05 0.828668 -0.559741 0 0.835596 -0.548951 0.020793 0.709471 -0.704611 0.013211 0.738032 -0.674766 -0.000291 0.625373 -0.779882 0.02632 0.59528 -0.803518 -5e-06 0.520072 -0.852823 0.047093 0.432438 -0.901664 -0.000208 0.40051 -0.916114 0.018066 0.252867 -0.967501 -0.000416 0.225199 -0.974089 0.020863 0.100977 -0.994889 0 -0.174895 -0.940847 0.290204 -0.698863 -0.187119 0.690346 -0.649036 -0.481714 0.588816 -0.558073 -0.253148 0.790235 -0.480072 -0.501214 0.719941 -0.381435 -0.27229 0.883383 -0.333706 -0.601747 0.725632 -0.175482 -0.371257 0.911797 -0.477009 -0.675342 0.562473 -0.597694 -0.61388 0.515667 -0.27653 -0.75221 0.59809 -0.122077 -0.768282 0.628363 -0.675343 -0.631145 0.381532 -0.637671 -0.738442 0.219269 -0.514791 -0.81815 0.256167 -0.3792 -0.884859 0.270616 -0.641983 -0.020203 0.766452 -0.519389 -0.01721 0.854364 -0.414578 -0.013598 0.909912 -0.283507 -0.016752 0.958824 -0.148287 -0.025669 0.988611 0.017171 -0.019539 0.999662 0.053099 -0.245058 0.968053 0.041757 -0.52234 0.851714 0.058091 -0.761785 0.645221 0.001402 -0.94653 0.322614 -0.770654 -0.559659 0.304751 -0.773998 -0.420881 0.47306 -0.774956 -0.250118 0.580417 0.914919 0.382426 0.129126 0.963584 0.234878 0.127823 -0.763718 -0.007263 0.64551 -0.99859 -0.040876 0.03388 -0.992874 -0.048392 -0.108899 -0.9912 -0.128548 0.031595 -0.98506 -0.138768 -0.101985 -0.990239 -0.136398 -0.028689 -0.987779 -0.139794 0.068925 -0.971378 -0.167776 0.168156 -0.972882 -0.170207 0.156622 -0.988554 -0.08293 0.126032 -0.98708 -0.072537 0.142872 -0.991433 0.024234 0.128349 -0.991273 0.054504 0.120028 -0.984959 0.16237 0.059095 -0.983239 -0.030338 0.179782 -0.978188 -0.131963 0.160417 -0.96557 -0.187683 0.180138 -0.999231 -0.038787 -0.005704 -0.96476 -0.101636 -0.242712 -0.998305 0.008166 0.05763 -0.991289 0.006265 -0.131553 0.557839 -0.252695 0.790545 0.670645 -0.225346 0.706721 0.572551 -0.024978 0.819489 0.649072 -0.481128 0.589255 0.563482 -0.585149 0.583171 0.644386 -0.627137 0.437568 0.480072 -0.501215 0.719941 0.473905 -0.728099 0.495264 0.639686 -0.728672 0.244621 0.38032 -0.27386 0.883378 0.378899 -0.884652 0.271709 0.284679 -0.543607 0.789588 0.276481 -0.75228 0.598024 0.207176 -0.042396 0.977385 0.172309 -0.940392 0.293209 0.702862 -0.016624 0.711132 0.786641 -0.032777 0.61654 0.770294 -0.238693 0.591332 0.772927 -0.421438 0.474315 0.773848 -0.531059 0.345161 0.767935 -0.619062 0.16443 0.330852 -0.017878 0.943513 0.450562 -0.019143 0.89254 0.858958 -0.482923 0.170224 0.879272 -0.02736 0.475533 0.860582 -0.192944 0.47135 0.904284 -0.15426 0.398088 0.865945 -0.326148 0.379166 0.906892 -0.274704 0.319507 0.950341 -0.129817 0.282843 0.940041 -0.2252 0.256142 0.888651 -0.382102 0.253569 0.942947 -0.26736 0.198419 0.906847 -0.401738 0.127421 0.953318 -0.27896 0.115616 0.993848 -0.030172 0.106562 0.978775 -0.130786 0.15778 0.972713 -0.164364 0.163751 0.967517 -0.185708 0.171533 0.991162 -0.115575 0.06512 0.934164 -0.018058 0.356388 0.969138 -0.017498 0.245897 0.890132 -0.111851 -0.441763 0.939541 -0.094107 -0.329251 0.946073 -0.14096 -0.291678 0.880502 -0.053846 -0.470975 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 -0 0.707108 -0.707106 0.971306 -0.029003 -0.236059 0.976947 -0.117741 -0.178078 0.978342 -0.130744 -0.160476 0.989763 0.095308 0.106234 0.996632 0.046261 0.067703 0.989849 -0.138084 -0.03363 0.998743 -0.047275 -0.016665 0.993356 -0.027837 -0.111669 0.98769 0.081097 0.133763 0.802079 -0.044152 -0.595584 0.761626 -0.06689 -0.644555 0.705719 -0.018877 -0.70824 0.937295 -0.029573 -0.34728 0.935556 -0.000421 -0.353177 0.863818 -0.016453 -0.503536 0.852404 0.001882 -0.522881 -0.100977 0 -0.994889 0.052533 0.000564 -0.998619 -0.004156 -0.005414 -0.999977 -0.179758 -0.013637 -0.983616 -0.248011 4.2e-05 -0.968757 -0.319863 -0.020392 -0.947244 -0.430578 0.002114 -0.902551 -0.460162 -0.009115 -0.887788 -0.596901 0.006857 -0.802286 -0.739344 -0.001096 -0.673326 -0.629055 -0.017419 -0.777166 -0.794069 -0.018252 -0.607554 -0.856618 0 -0.515951 -0.73747 -0.018253 -0.675133 -0.872701 -0.009491 -0.488162 -0.936124 -0.009069 -0.351553 -0.941122 0.000353 -0.338068 -0.976563 -0.013452 -0.214812 -0.956096 -0.039621 0.290362 -0.96918 -9e-05 0.246353 -0.919808 -0.034138 0.39088 -0.901963 2.4e-05 0.431813 -0.842896 -0.026349 0.537432 -0.802278 -0.000189 0.596951 -0.67381 -0.000169 0.738904 -0.518902 -0.000103 0.854833 -0.340266 1.4e-05 0.940329 -0.14693 0.000709 0.989147 0.05078 0.001854 0.998708 -0.983527 -0.171208 -0.05799 -0.996379 0.028451 0.080118 -0.932521 -0.360715 0.016995 -0.94206 -0.273648 0.194011 -0.572749 -0.083585 0.815458 -0.884861 -0.134984 0.44587 -0.279931 -0.458182 0.843628 -0.916937 -0.335513 0.216002 -0.487786 -0.714657 0.501328 -0.962395 0.265165 -0.059021 -0.944783 0.327549 0.009781 -0.580852 0.812162 0.054805 -0.790597 0.608954 0.064271 -0.83343 -0.524355 -0.174488 -0.932903 0.334244 0.134064 -0.782183 -0.265636 -0.563585 -0.833192 -0.490335 -0.255661 -0.677158 -0.260988 0.687999 -0.519062 -0.45995 0.720431 -0.505867 0.179398 0.843751 -0.749095 0.233047 0.620118 -0.995212 0.021739 0.095294 0.474064 0.879129 0.048952 0.622979 0.780031 0.058737 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0.859372 -0.180693 0.478361 -0.865554 -0.323966 0.381918 -0.864295 -0.422542 0.272861 -0.907503 -0.176065 0.381366 -0.908005 -0.285505 0.306618 -0.948921 -0.151842 0.276575 -0.939697 -0.227178 0.255657 -0.833669 -0.096371 -0.543791 -0.935738 -0.131715 -0.327179 -0.885186 -0.154714 -0.438758 -0.975895 -0.131518 -0.174164 -0.979022 0.167095 0.116596 -0.997563 0.035666 0.059967 -0.734025 -0.062513 -0.676239 -0.622204 -0.05864 -0.780656 -0.475698 -0.0489 -0.878248 0.999667 -0.025744 -0.001586 0.992172 -0.124815 -0.004016 0.988548 -0.12175 0.089163 0.988593 -0.035984 0.146253 0.98624 -0.080421 0.144441 0.972457 -0.171268 0.1581 0.988769 0.00378 0.149402 0.999948 0.009995 -0.002098 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0.405147 -0.006389 -0.914229 0.26565 -0.020598 -0.963849 0.349997 0.002531 -0.936747 0.56193 -0.010237 -0.827121 0.52174 0.001931 -0.853102 0.44255 -0.047345 -0.895493 0.622944 -0.05709 -0.78018 0.246291 0.001008 0.969196 0.431377 -0.000235 0.902172 0.59694 0.00018 0.802286 0.741626 -0.000414 0.670814 0.853703 -0.000635 0.52076 0.93926 -9.4e-05 0.343207 0.98884 -0.000795 -0.148979 0.736221 0.000626 -0.676741 0.636476 0 -0.771296 0.144479 -0.009211 -0.989465 0.200377 0 -0.979719</float_array>
					<technique_common>
						<accessor count="775" source="#base2_M1KShape1.normals-array" stride="3">
							<param name="X" type="float"/>
							<param name="Y" type="float"/>
							<param name="Z" type="float"/>
						</accessor>
					</technique_common>
				</source>
				<vertices id="base2_M1KShape1.vertices">
					<input semantic="POSITION" source="#base2_M1KShape1.positions"/>
				</vertices>
				<triangles count="1145" material="blinn21.material">
					<input offset="0" semantic="VERTEX" source="#base2_M1KShape1.vertices" set="0"/>
					<input offset="1" semantic="NORMAL" source="#base2_M1KShape1.normals" set="0"/>
					<p>0 0 2 2 1 1 0 0 3 3 2 2 4 4 6 6 5 5 5 5 7 7 4 4 8 8 10 10 9 9 11 11 9 9 10 10 12 12 8 8 13 13 9 9 13 13 8 8 13 13 14 14 12 12 14 14 15 15 12 12 12 12 15 15 16 16 14 14 17 17 15 15 16 16 18 18 12 12 8 8 12 12 18 18 10 10 8 8 18 18 18 18 19 19 10 10 13 13 20 20 14 14 21 21 20 20 13 13 9 9 21 21 13 13 22 22 21 21 9 9 11 11 22 22 9 9 23 23 22 22 11 11 24 24 25 25 14 14 20 20 24 24 14 14 22 22 23 23 26 26 25 25 17 17 14 14 27 27 29 29 28 28 27 27 28 28 30 30 30 30 28 28 31 31 30 30 31 31 32 32 30 30 32 32 33 33 33 33 32 32 34 34 33 33 34 34 35 35 33 33 35 35 36 36 37 37 29 29 19 19 38 38 29 29 37 37 28 28 29 29 38 38 39 39 28 28 38 38 31 31 28 28 39 39 40 40 31 31 39 39 32 32 31 31 40 40 34 34 32 32 40 40 41 41 34 34 40 40 35 35 34 34 41 41 42 42 23 23 27 27 30 30 42 42 27 27 43 43 42 42 30 30 33 33 43 43 30 30 44 44 43 43 33 33 45 45 44 44 33 33 36 36 45 45 33 33 46 46 45 45 36 36 10 10 19 19 29 29 27 27 10 10 29 29 11 11 10 10 27 27 23 23 11 11 27 27 47 47 35 35 41 41 36 36 35 35 47 47 48 48 50 50 49 49 51 51 53 53 52 52 42 42 55 55 54 54 52 52 53 53 45 45 53 53 44 44 45 45 44 44 55 55 43 43 55 55 42 42 43 43 56 56 44 44 53 53 55 55 44 44 56 56 26 26 23 23 54 54 54 54 23 23 42 42 46 46 52 52 45 45 57 57 59 59 58 58 60 60 58 58 59 59 58 58 60 60 61 61 61 61 63 63 62 62 64 64 65 65 62 62 62 62 63 63 64 64 66 66 67 67 64 64 64 64 67 67 65 65 60 60 59 59 46 46 67 67 68 68 65 65 62 62 69 69 61 61 69 69 58 58 61 61 57 57 58 58 70 70 71 71 73 73 72 72 74 74 75 75 71 71 76 76 74 74 71 71 77 77 79 79 78 78 75 75 73 73 71 71 80 80 82 82 81 81 81 81 82 82 83 83 81 81 83 83 84 84 84 84 83 83 85 85 84 84 85 85 86 86 86 86 85 85 87 87 88 88 80 80 81 81 88 88 81 81 89 89 89 89 81 81 84 84 89 89 84 84 90 90 90 90 84 84 86 86 90 90 86 86 91 91 80 80 92 92 82 82 88 88 93 93 80 80 94 94 86 86 87 87 91 91 86 86 94 94 95 95 97 97 96 96 97 97 99 99 98 98 98 98 96 96 97 97 98 98 99 99 100 100 101 101 100 100 102 102 99 99 102 102 100 100 103 103 101 101 102 102 101 101 103 103 104 104 105 105 95 95 96 96 106 106 108 108 107 107 109 109 107 107 108 108 110 110 106 106 107 107 111 111 112 112 106 106 108 108 106 106 112 112 106 106 110 110 111 111 112 112 111 111 113 113 114 114 113 113 111 111 115 115 109 109 74 74 109 109 115 115 107 107 116 116 110 110 107 107 117 117 111 111 110 110 111 111 117 117 114 114 118 118 113 113 119 119 120 120 68 68 67 67 121 121 120 120 67 67 122 122 120 120 121 121 123 123 122 122 121 121 124 124 67 67 66 66 125 125 67 67 124 124 121 121 67 67 125 125 126 126 121 121 125 125 123 123 121 121 126 126 127 127 123 123 126 126 128 128 123 123 127 127 129 129 128 128 127 127 130 130 94 94 131 131 132 132 131 131 133 133 130 130 131 131 132 132 134 134 130 130 135 135 136 136 130 130 132 132 135 135 130 130 136 136 137 137 134 134 135 135 138 138 134 134 137 137 139 139 135 135 136 136 137 137 135 135 139 139 87 87 131 131 94 94 133 133 131 131 87 87 140 140 133 133 87 87 137 137 141 141 138 138 139 139 141 141 137 137 142 142 141 141 139 139 132 132 133 133 143 143 144 144 145 145 142 142 120 120 145 145 68 68 144 144 68 68 145 145 122 122 146 146 145 145 145 145 120 120 122 122 146 146 122 122 147 147 128 128 147 147 122 122 147 147 128 128 148 148 129 129 149 149 128 128 148 148 128 128 149 149 122 122 123 123 128 128 145 145 146 146 150 150 150 150 142 142 145 145 116 116 107 107 151 151 152 152 154 154 153 153 155 155 153 153 154 154 156 156 154 154 152 152 154 154 156 156 157 157 158 158 157 157 156 156 157 157 158 158 79 79 151 151 115 115 158 158 79 79 158 158 115 115 159 159 160 160 155 155 151 151 107 107 115 115 79 79 161 161 157 157 155 155 162 162 153 153 162 162 163 163 153 153 163 163 164 164 153 153 152 152 153 153 165 165 156 156 152 152 165 165 164 164 165 165 153 153 156 156 166 166 158 158 165 165 166 166 156 156 167 167 158 158 166 166 158 158 167 167 151 151 116 116 151 151 168 168 168 168 151 151 167 167 161 161 169 169 157 157 170 170 157 157 169 169 154 154 157 157 170 170 170 170 159 159 154 154 155 155 154 154 159 159 171 171 173 173 172 172 174 174 172 172 173 173 173 173 175 175 174 174 171 171 150 150 173 173 150 150 171 171 142 142 175 175 173 173 147 147 146 146 173 173 150 150 176 176 175 175 147 147 146 146 147 147 173 173 148 148 149 149 176 176 147 147 148 148 176 176 149 149 177 177 176 176 174 174 175 175 178 178 176 176 178 178 175 175 172 172 174 174 179 179 179 179 180 180 172 172 180 180 141 141 172 172 171 171 172 172 141 141 141 141 142 142 171 171 181 181 183 183 182 182 183 183 184 184 182 182 182 182 185 185 181 181 185 185 186 186 181 181 187 187 167 167 166 166 188 188 187 187 166 166 165 165 188 188 166 166 164 164 188 188 165 165 187 187 168 168 167 167 189 189 188 188 164 164 189 189 164 164 163 163 190 190 192 192 191 191 190 190 191 191 193 193 194 194 196 196 195 195 197 197 195 195 196 196 195 195 192 192 194 194 192 192 190 190 194 194 190 190 193 193 198 198 199 199 201 201 200 200 202 202 204 204 203 203 205 205 203 203 206 206 206 206 203 203 204 204 207 207 205 205 206 206 208 208 207 207 206 206 209 209 210 210 208 208 207 207 208 208 210 210 208 208 211 211 209 209 212 212 199 199 213 213 163 163 214 214 189 189 215 215 189 189 214 214 216 216 218 218 217 217 219 219 216 216 217 217 215 215 214 214 220 220 221 221 215 215 220 220 222 222 221 221 220 220 218 218 221 221 222 222 217 217 218 218 222 222 193 193 223 223 198 198 224 224 226 226 225 225 212 212 213 213 226 226 227 227 79 79 77 77 228 228 79 79 227 227 229 229 228 228 227 227 230 230 161 161 229 229 231 231 230 230 229 229 72 72 231 231 229 229 73 73 231 231 72 72 223 223 225 225 198 198 232 232 233 233 182 182 184 184 232 232 182 182 232 232 184 184 234 234 234 234 184 184 235 235 183 183 235 235 184 184 236 236 181 181 237 237 235 235 236 236 237 237 237 237 181 181 238 238 239 239 241 241 240 240 242 242 6 6 243 243 244 244 243 243 240 240 245 245 244 244 240 240 240 240 241 241 245 245 41 41 247 247 246 246 39 39 249 249 248 248 37 37 249 249 38 38 40 40 247 247 41 41 248 248 247 247 40 40 39 39 248 248 40 40 38 38 249 249 39 39 17 17 7 7 250 250 251 251 26 26 252 252 92 92 252 252 26 26 20 20 251 251 253 253 252 252 253 253 251 251 253 253 24 24 20 20 252 252 92 92 254 254 255 255 254 254 92 92 253 253 252 252 256 256 254 254 256 256 252 252 24 24 253 253 257 257 256 256 257 257 253 253 251 251 20 20 21 21 21 21 22 22 251 251 26 26 251 251 22 22 92 92 26 26 258 258 259 259 50 50 48 48 260 260 50 50 259 259 50 50 260 260 239 239 241 241 239 239 260 260 246 246 261 261 41 41 262 262 263 263 261 261 264 264 263 263 262 262 265 265 264 264 266 266 262 262 261 261 246 246 264 264 262 262 266 266 267 267 265 265 266 266 268 268 265 265 267 267 269 269 271 271 270 270 271 271 272 272 270 270 272 272 273 273 270 270 270 270 274 274 269 269 25 25 275 275 17 17 276 276 277 277 118 118 275 275 25 25 276 276 277 277 276 276 25 25 255 255 278 278 254 254 279 279 256 256 278 278 254 254 278 278 256 256 256 256 279 279 257 257 25 25 24 24 277 277 277 277 24 24 257 257 92 92 80 80 255 255 278 278 255 255 80 80 80 80 93 93 278 278 278 278 280 280 279 279 281 281 257 257 279 279 281 281 282 282 257 257 282 282 277 277 257 257 283 283 46 46 270 270 52 52 46 46 283 283 272 272 284 284 283 283 285 285 284 284 272 272 271 271 285 285 286 286 287 287 285 285 271 271 271 271 269 269 288 288 288 288 269 269 289 289 290 290 70 70 269 269 269 269 274 274 290 290 59 59 290 290 274 274 274 274 270 270 59 59 46 46 59 59 270 270 119 119 276 276 118 118 291 291 293 293 292 292 294 294 293 293 291 291 291 291 292 292 295 295 296 296 295 295 297 297 291 291 295 295 296 296 298 298 297 297 299 299 296 296 297 297 298 298 298 298 299 299 300 300 298 298 300 300 66 66 261 261 293 293 41 41 263 263 293 293 261 261 292 292 293 293 263 263 295 295 292 292 263 263 264 264 295 295 263 263 297 297 295 295 264 264 299 299 297 297 264 264 265 265 299 299 264 264 300 300 299 299 265 265 268 268 300 300 265 265 60 60 46 46 294 294 291 291 60 60 294 294 61 61 60 60 291 291 296 296 61 61 291 291 63 63 61 61 296 296 298 298 63 63 296 296 64 64 63 63 298 298 66 66 64 64 298 298 47 47 41 41 293 293 294 294 47 47 293 293 36 36 47 47 294 294 46 46 36 36 294 294 301 301 300 300 268 268 66 66 300 300 301 301 302 302 258 258 54 54 303 303 140 140 304 304 56 56 303 303 304 304 304 304 305 305 56 56 302 302 56 56 305 305 305 305 306 306 302 302 258 258 302 302 306 306 303 303 56 56 53 53 55 55 56 56 302 302 304 304 140 140 85 85 87 87 85 85 140 140 305 305 304 304 83 83 85 85 83 83 304 304 306 306 305 305 82 82 83 83 82 82 305 305 82 82 92 92 306 306 92 92 258 258 306 306 258 258 26 26 54 54 54 54 55 55 302 302 51 51 303 303 53 53 51 51 288 288 303 303 307 307 303 303 288 288 140 140 303 303 307 307 93 93 280 280 278 278 113 113 118 118 282 282 281 281 113 113 282 282 112 112 113 113 281 281 279 279 112 112 281 281 108 108 112 112 279 279 280 280 108 108 279 279 109 109 108 108 280 280 109 109 280 280 74 74 118 118 277 277 282 282 280 280 93 93 75 75 74 74 280 280 75 75 77 77 78 78 72 72 76 76 71 71 72 72 72 72 229 229 77 77 229 229 227 227 77 77 76 76 115 115 74 74 79 79 115 115 308 308 307 307 143 143 133 133 309 309 143 143 307 307 310 310 143 143 309 309 69 69 310 310 309 309 309 309 58 58 69 69 143 143 310 310 311 311 310 310 69 69 311 311 62 62 311 311 69 69 312 312 313 313 311 311 65 65 312 312 62 62 311 311 62 62 312 312 133 133 140 140 307 307 312 312 65 65 68 68 307 307 288 288 309 309 288 288 70 70 309 309 70 70 58 58 309 309 313 313 312 312 144 144 144 144 312 312 68 68 144 144 142 142 313 313 142 142 139 139 313 313 311 311 313 313 136 136 139 139 136 136 313 313 143 143 311 311 132 132 136 136 132 132 311 311 314 314 125 125 301 301 124 124 301 301 125 125 315 315 126 126 314 314 125 125 314 314 126 126 126 126 315 315 127 127 127 127 315 315 316 316 124 124 66 66 301 301 268 268 317 317 301 301 301 301 317 317 314 314 314 314 317 317 315 315 318 318 315 315 317 317 315 315 318 318 316 316 316 316 129 129 127 127 319 319 90 90 91 91 170 170 89 89 319 319 169 169 89 89 170 170 89 89 169 169 320 320 91 91 159 159 319 319 320 320 88 88 89 89 73 73 88 88 320 320 73 73 75 75 88 88 93 93 88 88 75 75 91 91 160 160 159 159 159 159 170 170 319 319 90 90 319 319 89 89 321 321 94 94 130 130 94 94 321 321 322 322 323 323 321 321 134 134 234 234 323 323 134 134 134 134 324 324 234 234 324 324 134 134 138 138 233 233 324 324 138 138 130 130 134 134 321 321 141 141 180 180 138 138 138 138 180 180 233 233 233 233 180 180 325 325 323 323 322 322 321 321 91 91 322 322 160 160 322 322 91 91 94 94 174 174 178 178 326 326 179 179 174 174 326 326 327 327 179 179 326 326 180 180 179 179 327 327 328 328 180 180 327 327 325 325 180 180 328 328 176 176 177 177 329 329 178 178 176 176 329 329 186 186 330 330 181 181 185 185 325 325 331 331 233 233 185 185 182 182 181 181 332 332 238 238 333 333 330 330 186 186 233 233 325 325 185 185 334 334 329 329 335 335 335 335 149 149 129 129 177 177 149 149 335 335 335 335 329 329 177 177 336 336 338 338 337 337 337 337 323 323 336 336 339 339 336 336 323 323 323 323 340 340 339 339 341 341 339 339 340 340 340 340 237 237 341 341 237 237 238 238 341 341 342 342 341 341 238 238 219 219 217 217 342 342 323 323 337 337 322 322 234 234 343 343 340 340 323 323 234 234 340 340 343 343 237 237 340 340 330 330 342 342 238 238 341 341 342 342 217 217 217 217 222 222 341 341 222 222 220 220 339 339 339 339 341 341 222 222 220 220 214 214 336 336 336 336 339 339 220 220 214 214 163 163 338 338 338 338 336 336 214 214 163 163 162 162 338 338 338 338 155 155 337 337 155 155 338 338 162 162 160 160 337 337 155 155 337 337 160 160 322 322 200 200 178 178 329 329 328 328 327 327 344 344 345 345 344 344 327 327 327 327 326 326 345 345 346 346 345 345 326 326 326 326 178 178 346 346 178 178 200 200 346 346 347 347 344 344 345 345 329 329 334 334 200 200 347 347 219 219 344 344 346 346 347 347 345 345 219 219 342 342 344 344 328 328 344 344 342 342 330 330 325 325 328 328 342 342 330 330 328 328 0 0 348 348 3 3 3 3 348 348 349 349 350 350 352 352 351 351 353 353 349 349 354 354 355 355 357 357 356 356 356 356 352 352 355 355 356 356 351 351 352 352 358 358 359 359 357 357 357 357 355 355 358 358 357 357 359 359 360 360 361 361 363 363 362 362 361 361 362 362 364 364 365 365 367 367 366 366 368 368 370 370 369 369 371 371 369 369 370 370 369 369 371 371 372 372 373 373 372 372 371 371 374 374 376 376 375 375 372 372 373 373 376 376 375 375 376 376 373 373 377 377 374 374 375 375 366 366 367 367 378 378 374 374 377 377 367 367 378 378 367 367 377 377 366 366 380 380 379 379 380 380 366 366 378 378 380 380 381 381 379 379 382 382 379 379 381 381 383 383 382 382 381 381 383 383 384 384 349 349 382 382 383 383 385 385 385 385 383 383 349 349 384 384 386 386 3 3 3 3 386 386 2 2 349 349 384 384 3 3 386 386 387 387 2 2 1 1 387 387 350 350 1 1 2 2 387 387 388 388 350 350 389 389 390 390 388 388 389 389 389 389 350 350 387 387 391 391 390 390 389 389 392 392 391 391 393 393 393 393 395 395 394 394 392 392 393 393 394 394 395 395 397 397 396 396 394 394 395 395 396 396 397 397 399 399 398 398 398 398 396 396 397 397 400 400 402 402 401 401 403 403 405 405 404 404 406 406 403 403 404 404 402 402 407 407 401 401 406 406 401 401 408 408 408 408 401 401 407 407 406 406 408 408 403 403 398 398 399 399 409 409 410 410 409 409 399 399 409 409 410 410 411 411 412 412 411 411 410 410 411 411 412 412 413 413 414 414 413 413 412 412 415 415 416 416 414 414 417 417 416 416 415 415 417 417 418 418 404 404 416 416 417 417 405 405 405 405 417 417 404 404 401 401 406 406 419 419 406 406 404 404 418 418 420 420 400 400 421 421 419 419 406 406 418 418 401 401 419 419 400 400 421 421 400 400 419 419 422 422 420 420 423 423 423 423 420 420 421 421 424 424 426 426 425 425 423 423 426 426 424 424 422 422 423 423 424 424 427 427 425 425 426 426 428 428 429 429 427 427 429 429 425 425 427 427 429 429 431 431 430 430 431 431 429 429 428 428 432 432 430 430 433 433 433 433 430 430 431 431 433 433 435 435 434 434 434 434 432 432 433 433 436 436 434 434 435 435 435 435 437 437 436 436 438 438 436 436 437 437 439 439 370 370 368 368 437 437 439 439 438 438 368 368 438 438 439 439 368 368 369 369 440 440 441 441 443 443 442 442 444 444 442 442 443 443 443 443 445 445 444 444 446 446 444 444 445 445 445 445 447 447 446 446 448 448 449 449 444 444 442 442 444 444 449 449 444 444 446 446 448 448 450 450 446 446 447 447 451 451 450 450 447 447 442 442 449 449 452 452 450 450 448 448 446 446 453 453 452 452 449 449 454 454 453 453 448 448 449 449 448 448 453 453 455 455 454 454 448 448 448 448 450 450 455 455 440 440 455 455 450 450 450 450 451 451 440 440 369 369 372 372 440 440 441 441 456 456 443 443 456 456 457 457 443 443 445 445 443 443 458 458 457 457 458 458 443 443 458 458 459 459 445 445 447 447 445 445 459 459 459 459 460 460 447 447 460 460 461 461 447 447 461 461 462 462 447 447 463 463 447 447 462 462 464 464 451 451 463 463 447 447 463 463 451 451 440 440 451 451 465 465 464 464 465 465 451 451 440 440 465 465 368 368 440 440 372 372 455 455 372 372 376 376 455 455 455 455 376 376 454 454 376 376 374 374 453 453 454 454 376 376 453 453 374 374 367 367 453 453 452 452 453 453 466 466 367 367 466 466 453 453 442 442 452 452 467 467 466 466 467 467 452 452 467 467 468 468 442 442 442 442 468 468 441 441 407 407 469 469 408 408 470 470 469 469 402 402 469 469 407 407 402 402 471 471 456 456 441 441 472 472 474 474 473 473 475 475 473 473 474 474 476 476 475 475 474 474 474 474 477 477 476 476 477 477 474 474 478 478 478 478 479 479 477 477 480 480 477 477 479 479 479 479 481 481 480 480 480 480 481 481 482 482 483 483 482 482 481 481 483 483 481 481 362 362 484 484 483 483 385 385 385 385 483 483 362 362 349 349 484 484 385 385 474 474 472 472 485 485 485 485 486 486 474 474 474 474 486 486 478 478 487 487 479 479 478 478 479 479 487 487 481 481 487 487 364 364 481 481 362 362 481 481 364 364 362 362 382 382 385 385 359 359 483 483 484 484 359 359 482 482 483 483 482 482 488 488 480 480 480 480 488 488 477 477 476 476 477 477 488 488 475 475 489 489 473 473 472 472 473 473 490 490 491 491 490 490 473 473 490 490 485 485 472 472 492 492 494 494 493 493 493 493 495 495 492 492 495 495 497 497 496 496 498 498 492 492 495 495 495 495 496 496 498 498 499 499 496 496 497 497 497 497 500 500 499 499 434 434 499 499 500 500 492 492 498 498 501 501 499 499 498 498 496 496 499 499 434 434 502 502 503 503 501 501 498 498 498 498 499 499 503 503 504 504 503 503 499 499 499 499 502 502 504 504 503 503 505 505 501 501 506 506 504 504 502 502 505 505 503 503 462 462 463 463 462 462 503 503 464 464 463 463 504 504 503 503 504 504 463 463 504 504 506 506 464 464 507 507 508 508 493 493 368 368 465 465 506 506 508 508 509 509 493 493 510 510 495 495 509 509 493 493 509 509 495 495 511 511 497 497 510 510 495 495 510 510 497 497 512 512 500 500 511 511 497 497 511 511 500 500 430 430 432 432 512 512 500 500 512 512 432 432 432 432 434 434 500 500 434 434 436 436 502 502 436 436 438 438 502 502 506 506 502 502 438 438 438 438 368 368 506 506 506 506 465 465 464 464 461 461 505 505 462 462 513 513 501 501 505 505 514 514 501 501 513 513 494 494 492 492 514 514 501 501 514 514 492 492 507 507 493 493 494 494 515 515 429 429 512 512 516 516 517 517 508 508 509 509 508 508 517 517 517 517 516 516 518 518 517 517 519 519 509 509 510 510 509 509 519 519 518 518 520 520 517 517 519 519 517 517 520 520 521 521 522 522 518 518 520 520 518 518 522 522 519 519 523 523 510 510 511 511 510 510 523 523 520 520 523 523 519 519 522 522 524 524 520 520 523 523 520 520 524 524 523 523 515 515 511 511 512 512 511 511 515 515 523 523 525 525 515 515 524 524 526 526 523 523 525 525 523 523 526 526 527 527 528 528 521 521 528 528 529 529 521 521 521 521 529 529 522 522 530 530 522 522 529 529 522 522 530 530 524 524 524 524 530 530 526 526 530 530 531 531 526 526 531 531 424 424 526 526 424 424 425 425 526 526 526 526 425 425 525 525 525 525 425 425 515 515 516 516 532 532 518 518 533 533 521 521 532 532 518 518 532 532 521 521 429 429 515 515 425 425 534 534 536 536 535 535 537 537 534 534 535 535 538 538 540 540 539 539 541 541 539 539 542 542 540 540 542 542 539 539 543 543 541 541 542 542 544 544 543 543 545 545 542 542 545 545 543 543 546 546 544 544 547 547 545 545 547 547 544 544 548 548 547 547 549 549 548 548 546 546 547 547 550 550 549 549 551 551 551 551 549 549 547 547 551 551 552 552 550 550 553 553 550 550 552 552 554 554 556 556 555 555 553 553 552 552 556 556 555 555 556 556 552 552 557 557 554 554 555 555 555 555 558 558 557 557 559 559 557 557 558 558 558 558 560 560 559 559 559 559 560 560 561 561 562 562 563 563 560 560 561 561 560 560 563 563 564 564 563 563 562 562 565 565 564 564 566 566 562 562 566 566 564 564 567 567 569 569 568 568 569 569 567 567 570 570 571 571 570 570 567 567 572 572 571 571 573 573 567 567 573 573 571 571 574 574 572 572 575 575 573 573 575 575 572 572 576 576 574 574 575 575 577 577 576 576 578 578 575 575 578 578 576 576 579 579 577 577 578 578 580 580 581 581 579 579 577 577 579 579 582 582 582 582 579 579 581 581 583 583 584 584 580 580 581 581 580 580 584 584 584 584 583 583 585 585 583 583 586 586 585 585 587 587 585 585 586 586 586 586 588 588 587 587 589 589 591 591 590 590 592 592 591 591 593 593 589 589 593 593 591 591 594 594 592 592 593 593 593 593 538 538 594 594 535 535 596 596 595 595 536 536 597 597 535 535 596 596 535 535 597 597 400 400 420 420 402 402 598 598 470 470 402 402 402 402 420 420 599 599 597 597 601 601 600 600 602 602 595 595 596 596 598 598 402 402 603 603 604 604 606 606 605 605 605 605 537 537 604 604 535 535 607 607 537 537 535 535 595 595 607 607 608 608 609 609 537 537 537 537 607 607 608 608 537 537 609 609 604 604 610 610 604 604 609 609 604 604 610 610 606 606 611 611 613 613 612 612 613 613 611 611 614 614 615 615 614 614 611 611 614 614 615 615 616 616 617 617 616 616 615 615 616 616 617 617 618 618 619 619 618 618 617 617 620 620 621 621 619 619 620 620 623 623 622 622 621 621 620 620 624 624 624 624 620 620 622 622 625 625 622 622 623 623 625 625 623 623 626 626 627 627 626 626 623 623 626 626 627 627 628 628 491 491 628 628 627 627 485 485 630 630 629 629 629 629 630 630 631 631 490 490 630 630 485 485 632 632 631 631 630 630 633 633 634 634 471 471 634 634 633 633 632 632 633 633 631 631 632 632 456 456 471 471 635 635 635 635 471 471 634 634 635 635 636 636 456 456 457 457 456 456 636 636 459 459 458 458 637 637 458 458 457 457 636 636 636 636 637 637 458 458 460 460 459 459 638 638 461 461 460 460 638 638 637 637 638 638 459 459 638 638 639 639 461 461 640 640 642 642 641 641 643 643 645 645 644 644 644 644 646 646 643 643 364 364 643 643 646 646 365 365 641 641 647 647 641 641 645 645 647 647 648 648 364 364 646 646 649 649 350 350 650 650 350 350 351 351 650 650 392 392 390 390 391 391 651 651 652 652 390 390 392 392 651 651 390 390 394 394 651 651 392 392 652 652 388 388 390 390 652 652 350 350 388 388 350 350 652 652 352 352 484 484 349 349 353 353 382 382 362 362 653 653 652 652 654 654 352 352 352 352 654 654 355 355 358 358 355 355 654 654 655 655 382 382 656 656 361 361 364 364 648 648 366 366 655 655 640 640 640 640 365 365 366 366 363 363 656 656 382 382 655 655 379 379 382 382 366 366 379 379 655 655 657 657 658 658 646 646 644 644 657 657 646 646 657 657 644 644 659 659 644 644 645 645 659 659 641 641 660 660 645 645 659 659 645 645 660 660 640 640 661 661 365 365 658 658 648 648 646 646 660 660 657 657 659 659 405 405 403 403 416 416 662 662 411 411 413 413 416 416 662 662 413 413 663 663 662 662 416 416 403 403 663 663 416 416 416 416 413 413 414 414 664 664 666 666 665 665 665 665 668 668 667 667 668 668 669 669 667 667 669 669 670 670 667 667 670 670 671 671 667 667 671 671 672 672 667 667 672 672 673 673 667 667 673 673 674 674 667 667 674 674 675 675 667 667 675 675 676 676 667 667 676 676 677 677 667 667 677 677 678 678 667 667 678 678 679 679 667 667 679 679 680 680 667 667 680 680 681 681 667 667 681 681 682 682 667 667 682 682 683 683 667 667 683 683 684 684 667 667 667 667 685 685 665 665 685 685 686 686 665 665 686 686 687 687 665 665 687 687 688 688 665 665 688 688 689 689 665 665 689 689 690 690 665 665 690 690 691 691 665 665 691 691 692 692 665 665 692 692 693 693 665 665 693 693 694 694 665 665 694 694 695 695 665 665 695 695 696 696 665 665 696 696 697 697 665 665 697 697 698 698 665 665 698 698 664 664 665 665 643 643 364 364 487 487 468 468 699 699 633 633 467 467 700 700 468 468 699 699 468 468 700 700 466 466 701 701 467 467 700 700 467 467 701 701 701 701 466 466 647 647 466 466 367 367 647 647 631 631 699 699 702 702 700 700 703 703 699 699 702 702 699 699 703 703 701 701 703 703 700 700 703 703 701 701 645 645 702 702 704 704 631 631 629 629 631 631 704 704 703 703 705 705 702 702 704 704 702 702 705 705 705 705 703 703 643 643 645 645 643 643 703 703 365 365 647 647 367 367 645 645 701 701 647 647 699 699 631 631 633 633 468 468 633 633 441 441 705 705 643 643 487 487 487 487 478 478 705 705 704 704 705 705 478 478 478 478 486 486 704 704 629 629 704 704 486 486 486 486 485 485 629 629 633 633 471 471 441 441 706 706 708 708 707 707 707 707 708 708 489 489 430 430 512 512 429 429 489 489 628 628 473 473 707 707 489 489 709 709 709 709 489 489 475 475 359 359 654 654 710 710 359 359 358 358 654 654 491 491 473 473 628 628 709 709 475 475 476 476 488 488 709 709 476 476 710 710 711 711 482 482 359 359 710 710 482 482 482 482 711 711 488 488 622 622 712 712 624 624 628 628 489 489 626 626 489 489 708 708 626 626 625 625 626 626 708 708 625 625 706 706 622 622 706 706 625 625 708 708 712 712 622 622 706 706 527 527 521 521 533 533 605 605 534 534 537 537 712 712 621 621 624 624 618 618 619 619 621 621 713 713 621 621 712 712 714 714 621 621 713 713 618 618 621 621 714 714 616 616 618 618 714 714 715 715 602 602 716 716 716 716 600 600 717 717 603 603 719 719 718 718 719 719 603 603 599 599 420 420 422 422 599 599 527 527 715 715 716 716 717 717 529 529 716 716 529 529 717 717 720 720 718 718 719 719 717 717 720 720 717 717 719 719 599 599 531 531 719 719 531 531 599 599 422 422 531 531 422 422 424 424 527 527 721 721 715 715 715 715 722 722 602 602 716 716 602 602 596 596 600 600 716 716 597 597 596 596 597 597 716 716 601 601 717 717 600 600 717 717 598 598 718 718 598 598 717 717 601 601 718 718 598 598 603 603 603 603 402 402 599 599 719 719 531 531 530 530 720 720 719 719 530 530 530 530 529 529 720 720 716 716 529 529 528 528 528 528 527 527 716 716 723 723 725 725 724 724 726 726 727 727 724 724 724 724 728 728 726 726 728 728 729 729 726 726 729 729 730 730 726 726 730 730 731 731 726 726 731 731 732 732 726 726 732 732 733 733 726 726 733 733 734 734 726 726 734 734 735 735 726 726 735 735 736 736 726 726 736 736 737 737 726 726 737 737 738 738 726 726 738 738 739 739 726 726 739 739 740 740 726 726 740 740 741 741 726 726 741 741 742 742 726 726 727 727 743 743 724 724 743 743 744 744 724 724 744 744 745 745 724 724 745 745 746 746 724 724 746 746 747 747 724 724 747 747 748 748 724 724 748 748 749 749 724 724 749 749 750 750 724 724 750 750 751 751 724 724 751 751 752 752 724 724 752 752 753 753 724 724 753 753 754 754 724 724 754 754 755 755 724 724 755 755 756 756 724 724 756 756 723 723 724 724 757 757 759 759 758 758 760 760 761 761 757 757 757 757 758 758 762 762 760 760 757 757 762 762 763 763 760 760 762 762 606 606 760 760 763 763 605 605 606 606 763 763 461 461 639 639 505 505 764 764 505 505 639 639 505 505 764 764 513 513 765 765 513 513 764 764 494 494 514 514 766 766 513 513 765 765 514 514 766 766 514 514 765 765 766 766 767 767 507 507 494 494 766 766 507 507 767 767 768 768 508 508 508 508 507 507 767 767 508 508 768 768 516 516 532 532 516 516 769 769 769 769 516 516 768 768 533 533 532 532 769 769 533 533 721 721 527 527 769 769 721 721 533 533 722 722 715 715 721 721 602 602 722 722 770 770 770 770 608 608 595 595 602 602 770 770 595 595 607 607 595 595 608 608 609 609 608 608 610 610 606 606 610 610 771 771 771 771 772 772 606 606 772 772 761 761 760 760 760 760 606 606 772 772 761 761 759 759 757 757 613 613 773 773 612 612 759 759 774 774 758 758 773 773 758 758 774 774 774 774 612 612 773 773 360 360 359 359 484 484 360 360 484 484 353 353 350 350 649 649 1 1 649 649 0 0 1 1</p>
				</triangles>
			</mesh>
		</geometry>
	</library_geometries>
	<library_visual_scenes>
		<visual_scene id="VisualSceneNode" name="base1d_med">
			<node id="J5.dae" name="J5.dae">
				<instance_geometry url="#base2_M1KShape0">
					<bind_material>
						<technique_common>
							<instance_material symbol="blinn20.material" target="#blinn20.material"/>
						</technique_common>
					</bind_material>
				</instance_geometry>
				<instance_geometry url="#base2_M1KShape1">
					<bind_material>
						<technique_common>
							<instance_material symbol="blinn21.material" target="#blinn21.material"/>
						</technique_common>
					</bind_material>
				</instance_geometry>
			</node>
		</visual_scene>
	</library_visual_scenes>
	<scene>
		<instance_visual_scene url="#VisualSceneNode"/>
	</scene>
</COLLADA>
