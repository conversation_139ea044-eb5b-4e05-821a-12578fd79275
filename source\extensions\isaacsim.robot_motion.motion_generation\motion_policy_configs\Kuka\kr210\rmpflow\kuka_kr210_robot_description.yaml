# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# Lula will only use these joints to control the robot position.
cspace:
    - joint_a1
    - joint_a2
    - joint_a3
    - joint_a4
    - joint_a5
    - joint_a6
default_q: [
    -0.0,0.0,0.0,0.0,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - link_1:
    - "center": [0.39, 0.073, 0.417]
      "radius": 0.25
    - "center": [-0.337, -0.263, 0.269]
      "radius": 0.25
    - "center": [0.037, -0.248, 0.305]
      "radius": 0.25
    - "center": [-0.277, 0.114, 0.16]
      "radius": 0.25
    - "center": [-0.15, -0.256, 0.287]
      "radius": 0.25
    - "center": [0.168, 0.087, 0.331]
      "radius": 0.25
    - "center": [-0.055, 0.1, 0.245]
      "radius": 0.25
  - link_2:
    - "center": [0.069, -0.155, -0.0]
      "radius": 0.2
    - "center": [-0.119, -0.178, -0.0]
      "radius": 0.175
    - "center": [0.005, -0.215, 1.28]
      "radius": 0.175
    - "center": [0.077, -0.205, 0.193]
      "radius": 0.196
    - "center": [0.067, -0.228, 0.383]
      "radius": 0.193
    - "center": [0.04, -0.246, 0.569]
      "radius": 0.189
    - "center": [0.031, -0.257, 0.752]
      "radius": 0.185
    - "center": [0.022, -0.248, 0.931]
      "radius": 0.182
    - "center": [0.013, -0.207, 1.107]
      "radius": 0.178
    - "center": [-0.02, -0.265, 0.207]
      "radius": 0.175
  - link_3:
    - "center": [-0.006, 0.216, -0.037]
      "radius": 0.224
    - "center": [0.314, 0.192, -0.033]
      "radius": 0.191
    - "center": [0.857, 0.19, -0.051]
      "radius": 0.156
    - "center": [-0.144, 0.206, -0.091]
      "radius": 0.196
    - "center": [0.481, 0.195, -0.046]
      "radius": 0.179
    - "center": [-0.147, 0.204, 0.041]
      "radius": 0.191
    - "center": [0.195, 0.169, -0.008]
      "radius": 0.213
    - "center": [-0.0, 0.092, 0.0]
      "radius": 0.175
    - "center": [-0.0, 0.378, 0.0]
      "radius": 0.15
    - "center": [-0.29, 0.183, 0.074]
      "radius": 0.15
    - "center": [-0.315, 0.187, -0.194]
      "radius": 0.15
    - "center": [-0.302, 0.185, -0.06]
      "radius": 0.15
    - "center": [0.676, 0.192, -0.048]
      "radius": 0.167
  - link_4:
    - "center": [0.059, -0.0, -0.0]
      "radius": 0.15
    - "center": [0.525, 0.013, -0.0]
      "radius": 0.2
    - "center": [0.2, 0.004, -0.0]
      "radius": 0.165
    - "center": [0.355, 0.009, -0.0]
      "radius": 0.182
  - link_6:
    - "center": [-0.029, -0.0, -0.0]
      "radius": 0.125
