{"IsaacGenerate32FC1": {"version": 1, "icon": "icons/isaac-sim.svg", "description": "<PERSON> No<PERSON> that generates a constant 32FC1 buffer", "categoryDefinitions": "config/CategoryDefinition.json", "categories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {"uiName": "Isaac Generate 32FC1"}, "inputs": {"value": {"type": "float", "description": "Value for output image"}, "width": {"type": "uint", "description": "Width for output image", "default": 100}, "height": {"type": "uint", "description": "Height for output image", "default": 100}}, "outputs": {"data": {"type": "uchar[]", "memoryType": "cpu", "description": "Buffer 32FC1 array data", "default": []}, "width": {"type": "uint", "description": "Width for output image"}, "height": {"type": "uint", "description": "Height for output image"}, "encoding": {"type": "token", "description": "Encoding as a token"}}}}