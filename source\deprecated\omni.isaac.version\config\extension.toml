[package]
version = "2.0.7"
category = "Other"
title = "Isaac Sim Version"
description = "Isaac Sim Version"
keywords = ["isaac"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.core.version"

[dependencies]
"isaacsim.core.deprecation_manager" = {}
"isaacsim.core.version" = {}

[[python.module]]
name = "omni.isaac.version"

[[python.module]]
name = "omni.isaac.version.tests"
