# Changelog
## [0.1.16] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [0.1.15] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [0.1.14] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [0.1.13] - 2025-03-26
### Fixed
- Fix unit test failure

## [0.1.12] - 2025-03-25
### Changed
- Add import tests for deprecated extensions

## [0.1.11] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [0.1.10] - 2024-11-18
### Changed
- omni.client._omniclient to omni.client

## [0.1.9] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [0.1.8] - 2023-09-11
### Deprecated
- Add Deprecation Warning

## [0.1.7] - 2023-09-11
### Fixed
- UsdLux api change from Light to LightApi

## [0.1.6] - 2023-02-08
### Fixed
- included payloads as well as references when scaling

## [0.1.5] - 2023-01-06
### Fixed
- onclick_fn warning when creating UI

## [0.1.4] - 2022-05-09
- bugfix when Joints are nested in Meshes or Primitive shapes get double-scaled

## [0.1.3] - 2022-04-25
- bugfix when Update All referenced stages is uncheckeced - local delta to complete the conversion.

## [0.1.2] - 2021-12-14
- Bugfix for inverse OPs in Xforms

## [0.1.1] - 2021-12-14
- Add Physx Collision attributes
- Disable Update All Referenced Stages by default
- Do not save current stage unless Update All Referenced Stages is enabled

## [0.1.0] - 2021-08-13
- Initial Release
