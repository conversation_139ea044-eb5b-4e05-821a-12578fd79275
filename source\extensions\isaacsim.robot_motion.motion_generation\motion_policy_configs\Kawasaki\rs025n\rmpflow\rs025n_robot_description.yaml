# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The robot descriptor defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - joint1
    - joint2
    - joint3
    - joint4
    - joint5
    - joint6
default_q: [
    0.0,-0.2,-1.7,-1.507,0.0,0.0
]

acceleration_limits: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]
jerk_limits: [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 10000.0]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:
    - {name: finger_joint, rule: fixed, value: -0.0}
    - {name: left_inner_knuckle_joint, rule: fixed, value: 0.0}
    - {name: right_inner_knuckle_joint, rule: fixed, value: -0.0}
    - {name: right_outer_knuckle_joint, rule: fixed, value: 0.0}
    - {name: left_inner_finger_joint, rule: fixed, value: -0.0}
    - {name: right_inner_finger_joint, rule: fixed, value: 0.0}

# Lula uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, Lula will
# not be able to avoid obstacles.

collision_spheres:
  - link5:
    - "center": [0.065, 0.0, 0.002]
      "radius": 0.049
  - onrobot_rg2_base_link:
    - "center": [0.0, 0.001, 0.04]
      "radius": 0.053
    - "center": [0.0, -0.002, 0.084]
      "radius": 0.044
    - "center": [0.0, 0.01, 0.12]
      "radius": 0.037
    - "center": [-0.0, -0.011, 0.115]
      "radius": 0.037
  - left_outer_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.018
    - "center": [-0.0, -0.04, 0.034]
      "radius": 0.018
    - "center": [-0.0, -0.013, 0.011]
      "radius": 0.018
    - "center": [-0.0, -0.027, 0.023]
      "radius": 0.018
  - left_inner_knuckle:
    - "center": [0.0, -0.014, 0.014]
      "radius": 0.018
    - "center": [-0.001, -0.002, 0.002]
      "radius": 0.018
    - "center": [0.001, -0.031, 0.031]
      "radius": 0.018
  - right_inner_knuckle:
    - "center": [0.0, -0.014, 0.014]
      "radius": 0.018
    - "center": [-0.001, -0.002, 0.002]
      "radius": 0.018
    - "center": [0.001, -0.031, 0.031]
      "radius": 0.018
  - right_inner_finger:
    - "center": [0.002, 0.01, 0.028]
      "radius": 0.016
    - "center": [0.003, 0.006, 0.014]
      "radius": 0.014
    - "center": [-0.003, 0.012, 0.037]
      "radius": 0.014
  - left_inner_finger:
    - "center": [0.002, 0.01, 0.028]
      "radius": 0.016
    - "center": [0.003, 0.006, 0.014]
      "radius": 0.014
    - "center": [-0.003, 0.012, 0.037]
      "radius": 0.014
  - right_outer_knuckle:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.018
    - "center": [-0.0, -0.04, 0.034]
      "radius": 0.018
    - "center": [-0.0, -0.013, 0.011]
      "radius": 0.018
    - "center": [-0.0, -0.027, 0.023]
      "radius": 0.018
  - link3:
    - "center": [0.015, 0.001, 0.0]
      "radius": 0.162
  - link4:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.097
    - "center": [-0.003, 0.003, 0.214]
      "radius": 0.065
    - "center": [-0.001, 0.001, 0.05]
      "radius": 0.09
    - "center": [-0.001, 0.002, 0.096]
      "radius": 0.083
    - "center": [-0.002, 0.002, 0.139]
      "radius": 0.077
    - "center": [-0.002, 0.003, 0.178]
      "radius": 0.07
    - "center": [-0.011, 0.016, 0.411]
      "radius": 0.081
    - "center": [-0.004, 0.005, 0.222]
      "radius": 0.072
    - "center": [-0.006, 0.008, 0.267]
      "radius": 0.075
    - "center": [-0.007, 0.011, 0.314]
      "radius": 0.077
    - "center": [-0.009, 0.013, 0.362]
      "radius": 0.079
    - "center": [-0.0, 0.009, 0.829]
      "radius": 0.086
    - "center": [-0.007, 0.012, 0.417]
      "radius": 0.081
    - "center": [-0.005, 0.011, 0.474]
      "radius": 0.082
    - "center": [-0.003, 0.01, 0.533]
      "radius": 0.084
    - "center": [-0.001, 0.009, 0.754]
      "radius": 0.086
    - "center": [-0.002, 0.009, 0.68]
      "radius": 0.085
    - "center": [-0.002, 0.01, 0.606]
      "radius": 0.085
  - link1:
    - "center": [-0.007, 0.0, -0.003]
      "radius": 0.149
    - "center": [-0.118, -0.0, -0.103]
      "radius": 0.132
    - "center": [-0.014, 0.013, -0.183]
      "radius": 0.108
    - "center": [-0.133, 0.001, -0.035]
      "radius": 0.132
  - link2:
    - "center": [0.027, 0.005, -0.19]
      "radius": 0.11
    - "center": [0.231, 0.004, -0.208]
      "radius": 0.086
    - "center": [0.811, 0.008, -0.216]
      "radius": 0.086
    - "center": [0.397, 0.003, -0.225]
      "radius": 0.082
    - "center": [0.561, -0.001, -0.229]
      "radius": 0.078
    - "center": [0.929, -0.002, -0.204]
      "radius": 0.074
    - "center": [0.664, 0.0, -0.225]
      "radius": 0.075
    - "center": [-0.046, -0.0, -0.172]
      "radius": 0.092
    - "center": [0.099, 0.005, -0.205]
      "radius": 0.099
    - "center": [0.054, -0.044, -0.173]
      "radius": 0.093
    - "center": [0.321, -0.001, -0.221]
      "radius": 0.086
    - "center": [0.868, -0.073, -0.19]
      "radius": 0.06
    - "center": [-0.004, 0.057, -0.167]
      "radius": 0.087
    - "center": [0.907, 0.046, -0.201]
      "radius": 0.07
    - "center": [0.492, -0.007, -0.228]
      "radius": 0.076
    - "center": [0.735, 0.004, -0.221]
      "radius": 0.08
