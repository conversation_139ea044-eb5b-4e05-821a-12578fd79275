# Changelog
## [2.0.7] - 2025-05-19
### Changed
- Update copyright and license to apache v2.0

## [2.0.6] - 2025-05-16
### Changed
- Make extension target a specific kit version

## [2.0.5] - 2025-04-04
### Changed
- Version bump to fix extension publishing issues

## [2.0.4] - 2025-03-26
### Changed
- Cleanup and standardize extension.toml, update code formatting for all code

## [2.0.3] - 2025-03-25
### Changed
- Add import tests for deprecated extensions

## [2.0.2] - 2025-01-21
### Changed
- Update extension description and add extension specific test settings

## [2.0.1] - 2024-10-24
### Changed
- Updated dependencies and imports after renaming

## [2.0.0] - 2024-10-07
### Deprecated
- Extension deprecated since Isaac Sim 4.5.0. Replaced by isaacsim.robot_setup.grasp_editor.

## [1.3.0] - 2024-09-06
### Changed
- Changed metadata fields in Isaac Grasp file that will not interfere with grasp files that have already been created.

## [1.2.1] - 2024-08-28
### Fixed
- Unit test errors due to missing dependencies

## [1.2.0] - 2024-08-14
### Fixed
- Fixed serious bug in supported use-case where selected frames of reference have arbitrary relative transforms from the base frame.

## [1.1.1] - 2024-08-06
### Fixed
- Fix UI bug that allowed users to continue from selection frame prematurely.

## [1.1.0] - 2024-08-02
### Added
- Add Python API with tests to support loading and using imported grasps in Isaac Sim.

## [1.0.0] - 2024-07-23
### Added
- Initial version of Grasp Editor Extension
