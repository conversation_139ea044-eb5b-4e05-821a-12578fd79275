[package]
version = "0.1.16"
category = "Simulation"
title = "Articulation Inspector"
description = "This extension can inspect and modify the properties of an articulation."
keywords = ["isaac", "physics", "analyze", "inspect"]
changelog = "docs/CHANGELOG.md"
readme = "docs/README.md"
preview_image = "data/preview.png"
icon = "data/icon.png"
writeTarget.kit = true

[deprecation]
warning = "Extension deprecated since Isaac Sim 4.0.0. Replaced by omni.physx.inspector"

[dependencies]
"isaacsim.core.api" = {}
"isaacsim.gui.components" = {}
"omni.isaac.dynamic_control" = {}
"omni.physx" = {}

[[python.module]]
name = "omni.isaac.articulation_inspector"
